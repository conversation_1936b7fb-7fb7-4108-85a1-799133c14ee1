<template>
	<Layout title="开票帮助">
		<view style="padding: 24rpx">
			<view class="help-item">
				<h2>1、如何找到对应订单进行开票？</h2>
				<p v-if="status1">
					在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票
				</p>
				<p v-else>在我的-点击电子发票，找到对应已缴费的可开票</p>
				<view>
					<UiButton @click="open1" type="transparent" size="sm">{{ status1 ? '收起 ▴' : '更多 ▾' }}</UiButton>
				</view>
			</view>
			<view class="help-item">
				<h2>2、如何查询历史开票信息？</h2>
				<p v-if="status2">
					在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票在我的-点击电子发票，找到对应已缴费的可开票
				</p>
				<p v-else>在我的-点击电子发票，找到对应已缴费的可开票</p>
				<view>
					<UiButton @click="open2" type="transparent" size="sm">{{ status2 ? '收起 ▴' : '更多 ▾' }}</UiButton>
				</view>
			</view>
		</view>
	</Layout>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import Layout from '@/layout/index.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'

const status1 = ref(false)
const status2 = ref(false)
defineOptions({
	name: 'InvoiceHelp',
})
const open1 = () => {
	status1.value = !status1.value
}
const open2 = () => {
	status2.value = !status2.value
}
</script>
<style lang="scss" scoped>
.help-item {
	display: flex;
	padding: var(--content-default-padding-md);
	margin-bottom: var(--content-default-padding-md);
	flex-direction: column;
	border-radius: var(--content-default-radius-md);
	background: var(--light-fill-blank);
	h2 {
		color: var(--light-list-normal-default-text);
		font-size: var(--font-body-lg);
		margin-bottom: var(--content-default-radius-md);
		font-style: normal;
		font-weight: 500;
		line-height: 48rpx;
	}
	p {
		padding-left: 44rpx;
	}
}
</style>
