export default class Cache {
	private static KEY_LOGIN_USER = 'eslink_self_help_login_user'

	private static KEY_LOGIN_PROJECT = 'eslink_self_help_login_project'

	private static KEY_WEB_FINGERPRINT = 'eslink_self_help_web_fingerprint'
	
	private static KEY_CHANNEL_TOKEN = 'eslink_self_help_channel_token' // 渠道token
	
	private static KEY_TOKEN = 'eslink_self_help_token' // 全局token

	public static saveUser(obj: any) {
		uni.setStorageSync(this.KEY_LOGIN_USER, obj.userInfo)
	}

	public static getUser() {
		return uni.getStorageSync(this.KEY_LOGIN_USER)
	}

	public static saveProject(obj: any) {
		uni.setStorageSync(this.KEY_LOGIN_PROJECT, obj)
	}

	public static getProject() {
		return uni.getStorageSync(this.KEY_LOGIN_PROJECT)
	}

	public static saveWebFingerprint(obj: any) {
		uni.setStorageSync(this.KEY_WEB_FINGERPRINT, obj)
	}

	public static getWebFingerprint() {
		return uni.getStorageSync(this.KEY_WEB_FINGERPRINT)
	}
	// 保存渠道token
	public static saveChannelToken(obj: any) {
		uni.setStorageSync(this.KEY_CHANNEL_TOKEN, obj)
	}
	// 获取渠道token
	public static getChannelToken() {
		return uni.getStorageSync(this.KEY_CHANNEL_TOKEN)
	}
	// 移除渠道token
	public static removeChannelToken() {
		return uni.removeStorageSync(this.KEY_CHANNEL_TOKEN)
	}
	// 保存全局token
	public static saveToken(obj: any) {
		uni.setStorageSync(this.KEY_TOKEN, obj)
	}
	// 获取全局token
	public static getToken() {
		return uni.getStorageSync(this.KEY_TOKEN)
	}
	// 移除全局token
	public static removeToken() {
		return uni.removeStorageSync(this.KEY_TOKEN)
	}
}
