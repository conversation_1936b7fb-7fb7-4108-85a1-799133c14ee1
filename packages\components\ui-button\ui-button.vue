<template>
	<button
		class="ui-button"
		plain
		:style="{ width: width }"
		:open-type="openType"
		:class="chooseBtnClass()"
		:app-parameter="appParameter"
		:loading="loading"
		@click.stop="handleClick"
		@getphonenumber="handleGetPhoneNumber"
		@getuserinfo="handleGetUserInfo"
		@error="handleError"
		@opensetting="handleOpenSetting"
		@launchapp="handleLaunchApp"
		@agreeprivacyauthorization="handleAgreePrivacyAuthorization"
		@chooseavatar="handleChooseAvatar"
	>
		<slot name="default">
			<text v-if="!!text" class="ui-button-text">{{ text }}</text>
			<slot name="icon">
				<i v-if="!!props.icon" class="ui-button-icon" :class="chooseIconClass()"></i>
			</slot>
		</slot>
	</button>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import type { ButtonProps } from './types'
import { ButtonType, StyleName, IconPosition } from './types'
import { SizeEnum } from '../../shared/enum'
defineOptions({
	name: 'UiButton',
})

const props = withDefaults(defineProps<ButtonProps>(), {
	type: ButtonType.normal,
	size: SizeEnum.lg,
	text: '',
	iconfontClass: 'iconfont',
	icon: '',
	iconPosition: IconPosition.right,
	disabled: false,
	styleName: StyleName.light,
	loading: false,
})

const emits = defineEmits([
	'click',
	'chooseAvatar',
	'getPhoneNumber',
	'getUserInfo',
	'error',
	'openSetting',
	'lanchApp',
	'agreePrivacyAuthorization',
])

const loadingState = ref(false)

watch(() => props.loading,
(val: boolean) => {
	loadingState.value = val
})

const chooseBtnClass = () => {
	const { styleName, type, size, iconPosition, disabled } = props
	const baseClass = [styleName, type, `ui-button-${size}`, `icon-${iconPosition}`]
	return disabled || loadingState.value ? [...baseClass, 'disabled'] : baseClass
}

const chooseIconClass = () => {
	const { iconfontClass, icon, size, text } = props
	const baseClass = [iconfontClass, icon, `ui-button-icon-${size}`]
	return !!text ? [...baseClass, 'fix'] : baseClass
}

const handleClick = () => {
	if (props.disabled || loadingState.value) {
		return
	}
	emits('click')
}

// 获取用户头像回调
const handleChooseAvatar = (...data: any) => {
	emits('chooseAvatar', ...data)
}

// 获取用户手机号回调
const handleGetPhoneNumber = (...data: any) => {
	emits('getPhoneNumber', ...data)
}

// 用户点击该按钮时，会返回获取到的用户信息
// 从返回参数的detail中获取到的值同uni.getUserInfo
const handleGetUserInfo = (...data: any) => {
	emits('getUserInfo', ...data)
}

// 当使用开放能力时，发生错误的回调
const handleError = (...data: any) => {
	emits('error', ...data)
}

// 在打开授权设置页并关闭后回调
const handleOpenSetting = (...data: any) => {
	emits('openSetting', ...data)
}

// 从小程序打开 App 成功的回调
const handleLaunchApp = (...data: any) => {
	emits('launchApp', ...data)
}

// 用户同意隐私协议事件回调
const handleAgreePrivacyAuthorization = (...data: any) => {
	emits('agreePrivacyAuthorization', ...data)
}
</script>
<style scoped lang="scss">
@use './ui-button.scss';
</style>
