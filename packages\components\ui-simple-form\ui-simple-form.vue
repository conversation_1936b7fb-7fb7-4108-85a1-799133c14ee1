<template>
	<view class="form-wrap" :class="{transparent: transparent}" v-for="(formProps, propIndex) in list" :key="propIndex">
		<UiFormItem
			v-for="(item, index) in formProps"
			:key="index"
			:size="size"
			:focus="item.focus"
			:vertical="vertical"
			:label="item.label"
			:labelWidth="item.labelWidth"
			:required="item.required"
		>
			<!-- <template #labelLeft>
				<view>111</view>
			 </template>
			 <template #labelRight>
				<view>222</view>
			 </template> -->
			<UiInput
				v-if="item.type == 'input'"
				v-model="modelList[propIndex][item.value]"
				:bottomLine="false"
				:size="size"
				:vertical="vertical"
				:type="item.inputType"
				:disabled="item.disabled"
				:placeholder="item.placeholder"
				@focus="inputOnFocus(item, $event)"
				@blur="inputOnBlur(item, $event)"
			>
			</UiInput>
			<!-- <UiRadio v-model="model[item.value]"></UiRadio> -->
			<!-- <div>111</div> -->
		</UiFormItem>
	</view>
	<UiFooterBtns :btnList="btnList"></UiFooterBtns>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import UiInput from '../ui-input/ui-input.vue'
// import UiRadio fom '../ui-radio/ui-radio.vue'
import UiFooterBtns from '../ui-footer-btns/ui-footer-btns.vue'
import UiFormItem from '../ui-form-item/ui-form-item.vue'
import type { BtnItemProps } from '../ui-footer-btns/types'
const props = defineProps({
	transparent: {
		type: Boolean,
		default: false,
	},
	size: {
		type: String,
		default: 'md',
	},
	vertical: {
		type: Boolean,
		default: false,
	},
})
const btnList = ref([
	{
		text: '返回',
		type: 'normal',
		size: 'lg',
		click: () => {
			handleBack()
		},
	},
	{
		text: '提交',
		type: 'secondary',
		size: 'lg',
		click: () => {
			handleSubmit()
		},
	},
] as BtnItemProps[])
const emits = defineEmits(['submit'])
const list = ref([
	[
		{
			type: 'input',
			label: '姓名',
			placeholder: '请输入名字',
			// required: true,
			value: 'name',
			inputType: 'digit',
			labelWidth: '150rpx',
		},
		{
			type: 'input',
			label: '身高',
			placeholder: '请输入内容',
			value: 'cm',
			readonly: true,
			disabled: true,
		}
	],
	[
		{
			type: 'input',
			label: '姓名',
			placeholder: '请输入名字',
			// required: true,
			value: 'name',
		},
		{
			type: 'input',
			label: '身高',
			placeholder: '请输入内容',
			value: 'cm',
			readonly: true,
		},
		// {
		// 	type: 'radio',
		// 	label: '抬头类型',
		// 	placeholder: '请输入内容',
		// 	options: [
		// 		{
		// 			label: '企业单位',
		// 			value: '1',
		// 		},
		// 		{
		// 			label: '个人/非企业',
		// 			value: '2',
		// 		},
		// 	],
		// 	value: '',
		// 	readonly: true,
		// }
	],
])
const modelList = ref([{}, {}])
const rule = ref({ required: true, message: `名字不能为空！` })

// 输入框获取焦点时
const inputOnFocus = (item: any, value: string | number) => {
	item.focus = true
}
// 输入框失去焦点时
const inputOnBlur = (item: any, value: string | number) => {
	item.focus = false
	// 判断是否必填
	if (item.required) {
		if (typeof value == 'number') {
			if (value == undefined || value == null) {
				uni.showToast({ title: `${item.label || ''}不能为空`, icon: 'none' })
			}
		} else if (!value) {
			uni.showToast({ title: `${item.label || ''}不能为空`, icon: 'none' })
		}
	}
}

// 返回按钮点击
const handleBack = () => {
	uni.navigateBack()
}
// 提交按钮点击
const handleSubmit = () => {
	// 获取所有输入框的值
	let validate = true
	for (let i = 0; i < list.value.length; i++) {
		const groupItem = list.value[i];
		for (let j = 0; j < groupItem.length; j++) {
			const item = groupItem[j];
			if (item.required && !modelList.value[i][item.name]) {
				validate = false
				uni.showToast({ title: `${item.label || ''}不能为空`, icon: 'none' })
				break;
			}
		}
		if (!validate) {
			break;
		}
	}
	if (validate) {
		emits('submit', modelList.value)
	}
}

</script>
<style lang="scss" scoped>
@use './ui-simple-form.scss';
</style>
