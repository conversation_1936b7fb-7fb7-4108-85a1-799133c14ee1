@font-face {
  font-family: "iconfont-uniapp";
  src: url('iconfont-uniapp.eot?t=1753859083121'); /* IE9*/
  src: url('iconfont-uniapp.eot?t=1753859083121#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("iconfont-uniapp.woff2?t=1753859083121") format("woff2"),
  url("iconfont-uniapp.woff?t=1753859083121") format("woff"),
  url('iconfont-uniapp.ttf?t=1753859083121') format('truetype'); /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}

.iconfont-uniapp {
  font-family: 'iconfont-uniapp' !important;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.icon-uniapp-alert-fill:before { content: "\ea01"; }
.icon-uniapp-arrow-down-line:before { content: "\ea02"; }
.icon-uniapp-arrow-down-s-fill:before { content: "\ea03"; }
.icon-uniapp-arrow-left-s-fill:before { content: "\ea04"; }
.icon-uniapp-arrow-left-s-line:before { content: "\ea05"; }
.icon-uniapp-arrow-right-s-line:before { content: "\ea06"; }
.icon-uniapp-check-line:before { content: "\ea07"; }
.icon-uniapp-close-line:before { content: "\ea08"; }
.icon-uniapp-delete-bin-line:before { content: "\ea09"; }
.icon-uniapp-expand-diagonal-s-2-line:before { content: "\ea0a"; }
.icon-uniapp-eye-close-fill:before { content: "\ea0b"; }
.icon-uniapp-eye-fill:before { content: "\ea0c"; }
.icon-uniapp-heart-fill:before { content: "\ea0d"; }
.icon-uniapp-information-fill:before { content: "\ea0e"; }
.icon-uniapp-information-line:before { content: "\ea0f"; }
.icon-uniapp-input-delete:before { content: "\ea10"; }
.icon-uniapp-more-2-line:before { content: "\ea11"; }
.icon-uniapp-question-fill:before { content: "\ea12"; }
.icon-uniapp-question-line:before { content: "\ea13"; }
.icon-uniapp-search-2-line:before { content: "\ea14"; }
.icon-uniapp-settings-3-line:before { content: "\ea15"; }
.icon-uniapp-star-fill:before { content: "\ea16"; }
.icon-uniapp-star-half-line:before { content: "\ea17"; }
.icon-uniapp-star-line:before { content: "\ea18"; }
.icon-uniapp-toggle-fill:before { content: "\ea19"; }
.icon-uniapp-user-fill:before { content: "\ea1a"; }
.icon-uniapp-volume-mute-line:before { content: "\ea1b"; }

