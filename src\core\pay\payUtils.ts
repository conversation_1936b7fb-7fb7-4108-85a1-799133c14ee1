import type { BillInfoRes, IPrePayParams } from '@/pages/payment/paymentTypings'
import type { MeterInfo, UserInfo } from '@/pages/commonTypes'

/** 整理预下单接口参数 */
const getPrePayParams: (
	userInfo: UserInfo,
	billInfo: BillInfoRes,
	curMeterItem: MeterInfo,
	payAmount: number,
) => IPrePayParams = (userInfo: UserInfo, billInfo: BillInfoRes, curMeterItem: MeterInfo, payAmount: number) => {
	const { userNo, addressId } = userInfo
	const { accountBalance, payableTotalAmt, accoutnExpenditure, feeRecordCount, orgNo, feeRecordList = [] } = billInfo
	const idsArr: string[] = []
	for (const group of feeRecordList) {
		for (const rItem of group.groupList) {
			idsArr.push(rItem.feeId)
		}
	}
	const payRes = getPayAmount(payableTotalAmt, payAmount.toString())
	let backUrl = ''
	// #ifdef H5 || WEB
	backUrl = window.location.href
	// #endif
	const bizProcessMsg: any = {
		userNo, // 户号
		addressId, // 地址id
		meterId: curMeterItem.meterType == '13' ? curMeterItem.meterId || '' : '', // 表具id 物联网表必填
		meterNo: curMeterItem.meterType == '13' ? curMeterItem.meterNo || '' : '', // 表具编号 物联网表必填
		realAmt: payAmount.toString(), // 实收金额
		accountBalance: accountBalance || '', // 账户余额
		accoutnExpenditure: accoutnExpenditure || '', // 账户支出
		accountDepositAmt: curMeterItem.meterType == '13' ? '0' : payRes.accountMoney, // 账户存入 -- 物联网表是充值到表具，因此账户存入为0
		feeIdCount: feeRecordCount || '0', // 账单笔数
		feeIds: feeRecordList.length > 0 ? idsArr.join(',') : '', // 账单id,英文逗号分隔
		orgNo: orgNo || '', // 机构码
		discountAmtExp: billInfo.discountAmt || '', // 优惠账户支出
	}
	const preParams: IPrePayParams = {
		payMoney: payAmount + '',
		orgNo: orgNo,
		userNo,
		bizTradeTypeCode: 'ICF224',
		bizProcessMsgJson: JSON.stringify(bizProcessMsg),
		payCallBackUrl: backUrl,
		// assignTradeChannel: 'WECHAT', // 目前只有微信渠道，因此写死该值,微信公众号和小程序都传WECHAT，后面增加其它渠道时增加判断，支付宝传ALIPAY
	}
	console.log(JSON.stringify(curMeterItem))
	console.log(JSON.stringify(preParams))

	return preParams
}

const getPayType = (meterInfo: MeterInfo, feesTotal: string, payAmount: number) => {
	// 如果是物联网表
	// 只有13是物联网表，其它暂时都做普表
	if (meterInfo.meterType == '13') {
		// 有账单
		if (Number(feesTotal) > 0) {
			if (payAmount > Number(feesTotal)) {
				// 支付金额大于账单
				return 'bill+iot'
			} else {
				// 支付金额等于账单（小于账单不会走到这里）
				return 'bill'
			}
		} else {
			// 没账单
			return 'iot'
		}
	} else {
		// 普表
		// 有账单
		if (Number(feesTotal) > 0) {
			if (payAmount > Number(feesTotal)) {
				// 支付金额大于账单
				return 'bill+account'
			} else {
				// 支付金额等于账单（小于账单不会走到这里）
				return 'bill'
			}
		} else {
			// 没账单
			return 'account'
		}
	}
}

// 计算账单缴费和缴费余额
const getPayAmount = (totalMoney: string, money: string) => {
	let billMoney = ''
	let accountMoney = ''
	if (!totalMoney) {
		totalMoney = '0'
	}
	if (!money) {
		money = '0'
	}
	if (Number(money) < Number(totalMoney)) {
		billMoney = money
		accountMoney = ''
	} else {
		billMoney = totalMoney
		const newNum = Number(money) * 100
		const totalNum = Number(totalMoney) * 100
		const accountNum = Number(newNum.toFixed(2)) - Number(totalNum.toFixed(2))
		accountMoney = (accountNum / 100).toString()
	}
	return {
		billMoney,
		accountMoney,
	}
}

export { getPrePayParams, getPayType, getPayAmount }
