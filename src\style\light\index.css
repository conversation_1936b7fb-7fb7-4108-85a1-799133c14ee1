.main,
.main.light {
	--light-text-title: rgb(23, 25, 40);
	--light-text-primary: rgb(50, 54, 77);
	--light-text-secondary: rgb(95, 103, 125);
	--light-text-light: rgb(147, 155, 177);
	--light-text-disabled: rgb(203, 209, 225);
	--light-text-placeholder: rgb(203, 209, 225);
	--light-border-transparent: rgba(255, 255, 255, 0%);
	--light-text-link: rgb(42, 86, 232);
	--light-border-base: rgb(255, 255, 255);
	--light-border-lighter: rgb(244, 246, 251);
	--light-border-light: rgb(234, 237, 247);
	--light-border-dark: rgb(220, 225, 238);
	--light-border-darker: rgb(203, 209, 225);
	--light-border-disabled: rgb(220, 225, 238);
	--light-fill-transparent: rgba(255, 255, 255, 0%);
	--light-fill-alpha-base: rgba(255, 255, 255, 72%);
	--light-fill-alpha-light: rgba(255, 255, 255, 50%);
	--light-fill-alpha-lighter: rgba(255, 255, 255, 25%);
	--light-fill-blank: rgb(255, 255, 255);
	--light-border-split: var(--light-border-dark);
	--light-fill-lighter: rgb(244, 246, 251);
	--light-bg-base: #f8f9ffff;
	--light-bg-base-light-blue: #d3e1ffff;
	--light-icon-transparent: rgba(255, 255, 255, 0%);
	--light-icon-primary: var(--light-text-primary);
	--light-icon-secondary: var(--light-text-secondary);
	--light-icon-light: var(--light-text-light);
	--light-icon-disabled: var(--light-text-disabled);
	--light-fill-light: rgb(234, 237, 247);
	--light-icon-placeholder: var(--light-text-placeholder);
	--light-icon-link: var(--light-primary-default);
	--light-icon-sub-color: rgb(255, 107, 44);
	--light-success-l1: rgb(236, 248, 239);
	--light-success-l2: rgb(220, 244, 226);
	--light-fill-dark: rgb(220, 225, 238);
	--light-success-l3: rgb(197, 236, 207);
	--light-success-l4: rgb(167, 223, 181);
	--light-success-l5: rgb(137, 207, 154);
	--light-success-l6: rgb(100, 190, 122);
	--light-success-l7: rgb(64, 170, 90);
	--light-fill-darker: rgb(203, 209, 225);
	--light-fill-disabled: rgb(234, 237, 247);
	--light-success-light: rgb(33, 150, 62);
	--light-success-default: rgb(15, 136, 45);
	--light-success-dark: rgb(1, 122, 31);
	--light-warning-l1: rgb(248, 245, 236);
	--light-warning-l2: rgb(242, 232, 204);
	--light-warning-l3: rgb(235, 218, 174);
	--light-warning-l4: rgb(229, 205, 145);
	--light-warning-l5: rgb(223, 191, 118);
	--light-warning-l6: rgb(216, 177, 91);
	--light-warning-l7: rgb(210, 163, 67);
	--light-warning-light: rgb(204, 148, 43);
	--light-warning-default: rgb(197, 134, 21);
	--light-warning-dark: rgb(191, 120, 0);
	--light-error-l1: rgb(248, 236, 236);
	--light-error-l2: rgb(243, 213, 213);
	--light-error-l3: rgb(238, 190, 190);
	--light-error-l4: rgb(233, 168, 168);
	--light-error-l5: rgb(228, 147, 147);
	--light-error-l6: rgb(224, 127, 127);
	--light-error-l7: rgb(219, 107, 107);
	--light-error-light: rgb(214, 88, 88);
	--light-error-default: rgb(209, 70, 70);
	--light-error-dark: rgb(204, 53, 53);
	--dark-text-title: rgb(255, 255, 255);
	--dark-text-primary: rgb(255, 255, 255);
	--dark-text-secondary: rgba(255, 255, 255, 80%);
	--dark-text-light: rgba(255, 255, 255, 60%);
	--dark-text-disabled: rgba(255, 255, 255, 30%);
	--dark-text-placeholder: rgba(255, 255, 255, 40%);
	--dark-border-transparent: rgba(0, 0, 0, 0%);
	--dark-border-base: rgb(255, 255, 255);
	--dark-border-lighter: rgba(255, 255, 255, 10%);
	--dark-border-light: rgba(255, 255, 255, 20%);
	--dark-border-dark: rgba(255, 255, 255, 30%);
	--dark-border-darker: rgba(255, 255, 255, 40%);
	--dark-border-disabled: rgba(255, 255, 255, 4%);
	--dark-border-split: rgba(255, 255, 255, 4%);
	--dark-fill-transparent: rgba(0, 0, 0, 0%);
	--dark-fill-alpha-base: rgba(0, 0, 0, 80%);
	--dark-fill-alpha-light: rgba(0, 0, 0, 60%);
	--dark-fill-alpha-lighter: rgba(0, 0, 0, 20%);
	--dark-fill-blank: rgb(0, 0, 0);
	--dark-fill-lighter: rgba(0, 0, 0, 20%);
	--dark-fill-light: rgb(96, 99, 127);
	--dark-fill-dark: rgb(72, 75, 102);
	--dark-fill-darker: rgb(50, 54, 77);
	--dark-fill-disabled: rgba(0, 0, 0, 10%);
	--dark-icon-transparent: rgba(0, 0, 0, 0%);
	--dark-icon-primary: rgb(255, 255, 255);
	--dark-icon-secondary: rgba(255, 255, 255, 80%);
	--dark-icon-light: rgba(255, 255, 255, 60%);
	--dark-icon-disabled: rgba(255, 255, 255, 30%);
	--dark-icon-placeholder: rgba(255, 255, 255, 40%);
	--dark-icon-link: rgb(173, 190, 242);
	--dark-success-light: rgb(15, 136, 45);
	--dark-success-default: rgb(15, 124, 42);
	--dark-success-dark: rgb(14, 112, 39);
	--dark-success-l4: rgb(14, 99, 36);
	--dark-success-l5: rgb(13, 87, 32);
	--dark-success-l6: rgb(12, 75, 29);
	--dark-success-l7: rgb(10, 63, 25);
	--dark-success-l8: rgb(9, 50, 20);
	--dark-success-l9: rgb(7, 38, 16);
	--dark-success-l10: rgb(5, 26, 11);
	--dark-warning-light: rgb(197, 134, 21);
	--dark-warning-default: rgb(178, 121, 19);
	--dark-warning-dark: rgb(159, 108, 17);
	--dark-warning-04: rgb(141, 96, 15);
	--dark-warning-05: rgb(122, 83, 13);
	--dark-warning-06: rgb(103, 70, 11);
	--dark-warning-07: rgb(84, 57, 9);
	--dark-warning-08: rgb(66, 45, 7);
	--dark-warning-09: rgb(47, 32, 5);
	--dark-warning-10: rgb(28, 19, 3);
	--dark-error-light: rgb(209, 70, 70);
	--dark-error-default: rgb(189, 63, 63);
	--dark-error-dark: rgb(170, 57, 57);
	--dark-error-04: rgb(150, 50, 50);
	--dark-error-05: rgb(131, 44, 44);
	--dark-error-06: rgb(111, 37, 37);
	--dark-error-07: rgb(92, 31, 31);
	--dark-error-08: rgb(72, 24, 24);
	--dark-error-09: rgb(53, 18, 18);
	--dark-error-10: rgb(33, 11, 11);
	--light-info-l1: rgb(236, 248, 248);
	--light-primary-l1: rgb(238, 241, 250);
	--light-primary-l2: rgb(228, 233, 248);
	--light-primary-l3: rgb(214, 222, 246);
	--light-primary-l4: rgb(195, 207, 244);
	--light-primary-l5: rgb(173, 190, 242);
	--light-primary-l6: rgb(147, 170, 240);
	--light-primary-l7: rgb(120, 149, 238);
	--light-primary-light: rgb(92, 126, 236);
	--light-primary-default: rgb(66, 106, 234);
	--light-primary-dark: rgb(42, 86, 232);
	--light-primary-split: rgb(195, 207, 244);
	--light-primary-transparent: rgba(66, 106, 234, 0%);
	--light-info-l2: rgb(220, 244, 244);
	--light-info-l3: rgb(197, 236, 236);
	--light-info-l4: rgb(167, 223, 223);
	--light-info-l5: rgb(137, 207, 207);
	--light-info-l6: rgb(100, 190, 190);
	--light-info-l7: rgb(64, 170, 170);
	--light-info-light: rgb(33, 150, 150);
	--light-info-default: rgb(15, 136, 136);
	--light-info-dark: rgb(12, 125, 125);
	--dark-info-light: rgb(26, 139, 139);
	--dark-primary-l1: rgb(63, 103, 234);
	--dark-primary-l2: rgb(55, 92, 213);
	--dark-primary-l3: rgb(47, 81, 192);
	--dark-primary-l4: rgb(40, 71, 170);
	--dark-primary-l5: rgb(33, 61, 149);
	--dark-primary-l6: rgb(27, 51, 128);
	--dark-primary-l7: rgb(21, 42, 107);
	--dark-primary-light: rgb(63, 103, 234);
	--dark-primary-default: rgb(55, 92, 213);
	--dark-primary-dark: rgb(47, 81, 192);
	--dark-primary-split: rgb(27, 51, 128);
	--dark-accent-error-light: rgb(193, 65, 160);
	--dark-accent-error: rgb(175, 59, 145);
	--dark-accent-error-dark: rgb(157, 52, 129);
	--dark-accent-04: rgb(139, 46, 114);
	--dark-accent-05: rgb(121, 40, 99);
	--dark-accent-06: rgb(103, 34, 84);
	--dark-accent-07: rgb(85, 28, 69);
	--dark-accent-08: rgb(67, 22, 54);
	--dark-accent-09: rgb(49, 16, 40);
	--dark-accent-10: rgb(31, 10, 25);
	--dark-info-default: rgb(23, 126, 126);
	--dark-info-dark: rgb(19, 114, 114);
	--dark-info-04: rgb(17, 101, 101);
	--dark-info-05: rgb(14, 89, 89);
	--dark-info-06: rgb(11, 76, 76);
	--dark-info-07: rgb(9, 64, 64);
	--dark-info-08: rgb(7, 51, 51);
	--dark-info-09: rgb(5, 39, 39);
	--dark-info-10: rgb(3, 26, 26);
	--light-accent-l1: rgb(248, 236, 245);
	--light-accent-l2: rgb(241, 211, 234);
	--light-accent-l3: rgb(234, 187, 222);
	--light-accent-l4: rgb(227, 164, 211);
	--light-accent-l5: rgb(220, 142, 201);
	--light-accent-l6: rgb(214, 121, 190);
	--light-accent-l7: rgb(207, 101, 180);
	--light-accent-light: rgb(200, 82, 170);
	--light-accent-default: rgb(193, 65, 160);
	--light-accent-dark: rgb(186, 48, 151);
	--light-otherflow-l1: rgb(242, 236, 248);
	--light-otherflow-l2: rgb(229, 215, 244);
	--light-otherflow-l3: rgb(217, 194, 240);
	--light-otherflow-l4: rgb(205, 173, 236);
	--light-otherflow-l5: rgb(193, 154, 232);
	--light-otherflow-l6: rgb(181, 135, 228);
	--light-otherflow-l7: rgb(170, 116, 224);
	--light-otherflow-light: rgb(159, 98, 220);
	--light-otherflow-default: rgb(148, 81, 216);
	--light-otherflow-dark: rgb(138, 64, 212);
	--dark-otherflow-error-light: rgb(148, 81, 216);
	--dark-otherflow-error: rgb(134, 74, 196);
	--dark-otherflow-error-dark: rgb(120, 67, 176);
	--dark-otherflow-04: rgb(106, 59, 156);
	--dark-otherflow-05: rgb(92, 52, 136);
	--dark-otherflow-06: rgb(78, 44, 116);
	--dark-otherflow-07: rgb(65, 37, 96);
	--dark-otherflow-08: rgb(51, 29, 76);
	--dark-otherflow-09: rgb(37, 22, 56);
	--dark-otherflow-10: rgb(24, 14, 36);
	--dark-doing-error-light: rgb(87, 127, 48);
	--dark-doing-error: rgb(79, 115, 43);
	--dark-doing-error-dark: rgb(70, 104, 39);
	--dark-doing-04: rgb(62, 92, 34);
	--dark-doing-05: rgb(54, 81, 29);
	--dark-doing-06: rgb(46, 69, 25);
	--dark-doing-07: rgb(38, 58, 21);
	--dark-doing-08: rgb(30, 46, 16);
	--dark-doing-09: rgb(23, 35, 12);
	--dark-doing-10: rgb(15, 23, 8);
	--light-doing-l1: rgb(242, 248, 236);
	--light-doing-l2: rgb(219, 233, 205);
	--light-doing-l3: rgb(197, 218, 176);
	--light-doing-l4: rgb(176, 203, 149);
	--light-doing-l5: rgb(156, 188, 124);
	--light-doing-l6: rgb(137, 172, 102);
	--light-doing-l7: rgb(120, 157, 82);
	--light-doing-light: rgb(103, 142, 64);
	--light-doing-default: rgb(87, 127, 48);
	--light-doing-dark: rgb(73, 112, 34);
	--dark-text-link: rgb(173, 190, 242);
	--light-text-subcolor: rgb(255, 107, 44);
	--light-important-l1: rgb(255, 216, 200);
	--light-important-l2: rgb(255, 198, 176);
	--light-important-l3: rgb(255, 182, 150);
	--light-important-l4: rgb(255, 167, 129);
	--light-important-l5: rgb(255, 153, 109);
	--light-important-l6: rgb(255, 141, 92);
	--light-important-l7: rgb(255, 131, 78);
	--light-important-light: rgb(255, 123, 65);
	--light-important-default: rgb(255, 114, 54);
	--light-important-dark: rgb(255, 107, 44);
	--light-important-transparent: rgba(255, 107, 44, 0%);
	--button-xlg-padding: var(--gap-20);
	--button-lg-padding: var(--gap-16);
	--button-lg-radius: var(--radius-lg);
	--button-lg-margin: var(--gap-6);
	--button-lg-height: 40px;
	--button-lg-iconsize: var(--icon-lg);
	--button-lg-fontsize: var(--font-body-lg);
	--button-xlg-radius: var(--radius-xlg);
	--button-xlg-margin: var(--gap-8);
	--button-xlg-height: 48px;
	--button-xlg-iconsize: var(--icon-xlg);
	--button-xlg-fontsize: var(--font-body-xlg);
	--button-md-padding: var(--gap-12);
	--button-md-radius: var(--radius-md);
	--button-md-margin: var(--gap-4);
	--button-md-height: 32px;
	--button-md-iconsize: var(--icon-md);
	--form-sm-margin: var(--gap-4);
	--form-sm-padding: var(--gap-6);
	--form-sm-radius: var(--radius-md);
	--form-sm-height: 32px;
	--form-sm-iconsize: var(--icon-sm);
	--form-sm-fontsize: var(--font-body-sm);
	--form-md-margin: var(--gap-4);
	--form-md-padding: var(--gap-12);
	--form-md-radius: var(--radius-lg);
	--form-md-height-md: 44px;
	--form-md-iconsize: var(--icon-md);
	--form-md-fontsize: var(--font-body-md);
	--button-md-fontsize: var(--font-body-md);
	--tag-lg-height: 32px;
	--tag-lg-font-size: var(--font-body-lg);
	--tag-lg-radius: 14px;
	--tag-lg-padding: var(--gap-12);
	--tag-lg-margin: var(--gap-6);
	--tag-md-height: 28px;
	--tag-md-font-size: var(--font-body-md);
	--tag-md-radius: 10px;
	--tag-md-padding: var(--gap-10);
	--tag-md-margin: var(--gap-4);
	--table-md-td-height: 40px;
	--list-sm-height: 32px;
	--list-sm-margin: var(--gap-4);
	--list-sm-padding: var(--gap-8);
	--list-md-height: 44px;
	--list-md-margin: var(--gap-6);
	--list-md-padding: var(--gap-12);
	--table-md-th-height: 40px;
	--table-md-padding: var(--gap-10);
	--tabs-lg-height: 48px;
	--tabs-lg-padding-lg: var(--gap-20);
	--tabs-lg-padding-sm: var(--gap-8);
	--tabs-lg-margin: var(--gap-12);
	--tabs-lg-radius: 12px;
	--navs-padding: var(--gap-7);
	--navs-height: 52px;
	--navs-margin: var(--gap-12);
	--menu-height: 40px;
	--menu-margin: var(--gap-16);
	--title-fontweight: 800px;
	--title-height: 52px;
	--title-page-fontsize: var(--font-title-lg);
	--title-content-fontsize: var(--font-title-md);
	--tree-height: 52px;
	--icon-xlg: 24px;
	--icon-lg: 20px;
	--icon-md: 16px;
	--icon-sm: 12px;
	--icon-min: 10px;
	--font-title-xlg: 26px;
	--font-title-lg: 20px;
	--font-title-md: 17px;
	--font-title-sm: 15px;
	--font-title-min: 12px;
	--font-body-xlg: 19px;
	--font-body-lg: 17px;
	--font-body-md: 15px;
	--font-body-sm: 12px;
	--font-body-min: 10px;
	--font-number-sm: 12px;
	--font-number-md: 16px;
	--font-number-lg: 20px;
	--font-number-xlg: 24px;
	--font-number-xxlg: 32px;
	--font-800: 800px;
	--font-400: 400px;
	--font-100: 100px;
	--font-semibold: semibold;
	--font-bold: bold;
	--font-regular: regular;
	--font-light: light;
	--font-thin: thin;
	--fix-sm-gap_less0: 4px;
	--fix-sm-gap_less1: 14px;
	--fix-sm-gap_less2: 28px;
	--fix-sm-gap_less3: 42px;
	--fix-sm-gap_less4: 56px;
	--fix-sm-gap_less5: 70px;
	--radius-circle: 9999px;
	--radius-round: 20px;
	--radius-xlg: 10px;
	--radius-lg: 8px;
	--radius-md: 6px;
	--radius-sm: 4px;
	--radius-min: 2px;
	--radius-none: 0px;
	--gap-null: 0px;
	--gap-2: 2px;
	--gap-4: 4px;
	--gap-6: 6px;
	--gap-7: 7px;
	--gap-8: 8px;
	--gap-10: 10px;
	--gap-12: 12px;
	--gap-16: 16px;
	--gap-20: 20px;
	--gap-24: 24px;
	--gap-28: 28px;
	--gap-32: 32px;
	--gap-36: 36px;
	--gap-40: 40px;
	--button-sm-padding: var(--gap-8);
	--button-sm-radius: var(--radius-sm);
	--button-sm-margin: var(--gap-2);
	--button-sm-height: 24px;
	--button-sm-iconsize: var(--icon-sm);
	--button-sm-fontsize: var(--font-body-sm);
	--tag-sm-height: 24px;
	--tag-sm-font-size: var(--font-body-sm);
	--tag-sm-radius: 8px;
	--tag-sm-padding: var(--gap-8);
	--tag-sm-margin: var(--gap-2);
	--tag-min-height: 20px;
	--tag-min-font-size: var(--font-body-min);
	--tag-min-radius: 6px;
	--tag-min-padding: var(--gap-7);
	--tag-min-margin: var(--gap-2);
	--border-bold: 3px;
	--border-normal: 1px;
	--border-none: 0px;
	--form-lg-margin: var(--gap-6);
	--form-lg-padding: var(--gap-16);
	--form-lg-radius: var(--radius-xlg);
	--form-lg-height-large: 56px;
	--form-lg-iconsize: var(--icon-lg);
	--form-lg-fontsize: var(--font-body-lg);
	--content-default-margin-xs: var(--gap-4);
	--content-default-margin-min: var(--gap-6);
	--content-default-margin-sm: var(--gap-8);
	--content-default-margin-md: var(--gap-12);
	--content-default-margin-lg: var(--gap-16);
	--content-default-margin-xlg: var(--gap-24);
	--content-default-padding-xs: var(--gap-4);
	--content-default-padding-min: var(--gap-6);
	--content-default-padding-sm: var(--gap-8);
	--content-default-padding-md: var(--gap-12);
	--content-default-padding-lg: var(--gap-16);
	--content-default-padding-xlg: var(--gap-24);
	--content-default-padding-xxlg: var(--gap-32);
	--content-default-radius-min: 4px;
	--content-default-radius-sm: 6px;
	--content-default-radius-md: 10px;
	--content-default-radius-lg: 16px;
	--content-default-radius-xlg: 20px;
	--tabs-md-height: 40px;
	--tabs-md-padding-lg: var(--gap-16);
	--tabs-md-padding-sm: var(--gap-6);
	--tabs-md-margin: var(--gap-8);
	--tabs-md-radius: 8px;
	--tabs-sm-height: 32px;
	--tabs-sm-padding-lg: var(--gap-12);
	--tabs-sm-padding-sm: var(--gap-4);
	--tabs-sm-margin: var(--gap-4);
	--tabs-sm-radius: 6px;
	--list-lg-height: 56px;
	--list-lg-margin: var(--gap-8);
	--list-lg-padding: var(--gap-16);
	--search-md-height: 44px;
	--search-lg-height: 56px;
	--dropdown-sm-height: 32px;
	--dropdown-sm-margin: var(--gap-2);
	--dropdown-sm-padding: var(--gap-8);
	--dropdown-sm-radius: var(--radius-sm);
	--dropdown-md-height: 40px;
	--dropdown-md-margin: var(--gap-4);
	--dropdown-md-padding: var(--gap-12);
	--dropdown-md-radius: var(--radius-md);
	--dropdown-lg-height: 48px;
	--dropdown-lg-margin: var(--gap-6);
	--dropdown-lg-padding: var(--gap-16);
	--dropdown-lg-radius: var(--radius-lg);
	--light-button-default-normal-text: var(--light-text-primary);
	--light-button-default-normal-background: var(--light-fill-blank);
	--light-button-default-normal-border: var(--light-border-darker);
	--light-button-default-normal-icon: var(--light-icon-primary);
	--light-button-default-primary-text: var(--dark-text-title);
	--light-button-default-primary-background: var(--light-primary-default);
	--light-button-default-primary-border: var(--light-primary-default);
	--light-button-default-primary-icon: var(--dark-icon-primary);
	--light-button-default-link-text: var(--light-primary-dark);
	--light-button-default-link-background: var(--dark-fill-transparent);
	--light-button-default-link-border: var(--dark-border-transparent);
	--light-button-default-link-icon: var(--light-primary-dark);
	--light-button-default-secondary-text: var(--dark-text-primary);
	--light-button-default-secondary-background: var(--dark-fill-darker);
	--light-button-default-secondary-border: var(--dark-border-transparent);
	--light-button-default-secondary-icon: var(--dark-icon-primary);
	--light-button-default-transparent-text: var(--light-text-primary);
	--light-button-default-transparent-background: var(--light-fill-transparent);
	--light-button-default-transparent-border: var(--light-border-transparent);
	--light-button-default-transparent-icon: var(--light-icon-secondary);
	--light-form-normal-default-text: var(--light-text-title);
	--light-form-normal-default-text-light: var(--light-text-light);
	--light-form-normal-default-label: var(--light-text-primary);
	--light-form-normal-default-secondary: var(--light-text-secondary);
	--light-form-normal-default-background: var(--light-fill-blank);
	--light-form-normal-default-background-input: var(--light-fill-lighter);
	--light-form-normal-default-border: var(--light-border-light);
	--light-form-normal-default-underline: var(--light-border-light);
	--light-form-normal-default-icon-light: var(--light-icon-light);
	--light-form-normal-default-icon-normal: var(--light-icon-primary);
	--light-form-normal-default-icon-primary: var(--light-primary-l5);
	--light-form-normal-default-icon-transparent: var(--light-fill-transparent);
	--light-form-normal-default-icon_switch: var(--light-primary-l4);
	--light-form-normal-default-radio_position_x: 5px;
	--light-form-placeholder: var(--light-text-placeholder);
	--light-list-placeholder: var(--light-text-placeholder);
	--light-list-normal-default-text: var(--light-text-title);
	--light-list-normal-default-text-light: var(--light-text-light);
	--light-list-normal-default-label: var(--light-text-primary);
	--light-list-normal-default-secondary: var(--light-text-secondary);
	--light-list-normal-default-background: var(--light-fill-blank);
	--light-list-normal-default-border: var(--light-border-light);
	--light-list-normal-default-underline: var(--light-border-light);
	--light-list-normal-default-icon-light: var(--light-icon-light);
	--light-list-normal-default-icon-normal: var(--light-icon-primary);
	--light-list-normal-default-icon-primary: var(--light-primary-l5);
	--light-list-normal-press-text: var(--light-text-title);
	--light-list-normal-press-text-light: var(--light-text-light);
	--light-list-normal-press-label: var(--light-text-primary);
	--light-list-normal-press-secondary: var(--light-text-secondary);
	--light-list-normal-press-background: var(--light-fill-lighter);
	--light-list-normal-press-border: var(--light-border-light);
	--light-list-normal-press-underline: var(--light-border-light);
	--light-list-normal-press-icon-light: var(--light-icon-light);
	--light-list-normal-press-icon-normal: var(--light-icon-secondary);
	--light-list-normal-press-icon-primary: var(--light-primary-l5);
	--light-list-normal-press-icon-transparent: var(--light-fill-transparent);
	--light-list-normal-press-icon_switch: var(--light-primary-l4);
	--light-list-normal-disable-text: var(--light-text-placeholder);
	--light-list-normal-disable-text-light: var(--light-text-disabled);
	--light-list-normal-disable-label: var(--light-text-disabled);
	--light-list-normal-disable-secondary: var(--light-text-disabled);
	--light-list-normal-disable-background: var(--light-fill-blank);
	--light-list-normal-disable-border: var(--light-border-light);
	--light-list-normal-disable-icon-light: var(--light-icon-disabled);
	--light-list-normal-disable-icon-normal: var(--light-icon-disabled);
	--light-list-normal-disable-icon-primary: var(--light-icon-disabled);
	--light-tabs-default-fill-dark-text: var(--light-text-secondary);
	--light-tabs-default-fill-dark-text-sub: var(--light-text-secondary);
	--light-tabs-press-fill-dark-text: var(--light-text-title);
	--light-tabs-press-fill-dark-text-sub: var(--light-text-secondary);
	--light-tabs-press-fill-dark-background: var(--light-fill-lighter);
	--light-tabs-press-fill-dark-border: var(--light-border-transparent);
	--light-tabs-press-fill-dark-icon: var(--light-icon-secondary);
	--light-tabs-press-page-text: var(--light-text-title);
	--light-tabs-press-page-text-sub: var(--light-text-secondary);
	--light-tabs-press-page-background: var(--light-fill-transparent);
	--light-tabs-press-page-border: var(--light-primary-split);
	--light-tabs-press-page-icon: var(--light-icon-primary);
	--light-tabs-press-text-text: var(--light-text-title);
	--light-tabs-press-text-text-sub: var(--light-text-secondary);
	--light-tabs-press-text-background: var(--light-fill-transparent);
	--light-tabs-press-text-border: var(--light-primary-dark);
	--light-tabs-press-text-icon: var(--light-icon-secondary);
	--light-tabs-press-fill-light-text: var(--light-text-title);
	--light-tabs-press-fill-light-text-sub: var(--light-text-secondary);
	--light-tabs-press-fill-light-background: var(--light-fill-lighter);
	--light-tabs-press-fill-light-border: var(--light-border-transparent);
	--light-tabs-press-fill-light-icon: var(--light-icon-secondary);
	--light-tabs-press-card-text: var(--light-text-title);
	--light-tabs-press-card-text-sub: var(--light-text-secondary);
	--light-tabs-press-card-background: var(--light-fill-light);
	--light-tabs-press-card-border: var(--light-primary-split);
	--light-tabs-press-card-icon: var(--light-icon-primary);
	--light-tabs-default-fill-dark-background: var(--light-fill-lighter);
	--light-tabs-default-fill-dark-border: var(--light-border-transparent);
	--light-tabs-default-fill-dark-icon: var(--light-icon-secondary);
	--light-tabs-default-page-text: var(--light-text-secondary);
	--light-tabs-default-page-text-sub: var(--light-text-secondary);
	--light-tabs-default-page-background: var(--light-fill-transparent);
	--light-tabs-default-page-border: var(--light-primary-split);
	--light-tabs-default-page-icon: var(--light-icon-primary);
	--light-tabs-default-text-text: var(--light-text-secondary);
	--light-tabs-default-text-text-sub: var(--light-text-secondary);
	--light-tabs-default-text-background: var(--light-fill-transparent);
	--light-tabs-default-text-border: var(--light-primary-dark);
	--light-tabs-default-text-icon: var(--light-icon-secondary);
	--light-tabs-default-fill-light-text: var(--light-text-secondary);
	--light-tabs-default-fill-light-text-sub: var(--light-text-secondary);
	--light-tabs-default-fill-light-background: var(--light-fill-lighter);
	--light-tabs-default-fill-light-border: var(--light-border-transparent);
	--light-tabs-default-fill-light-icon: var(--light-icon-secondary);
	--light-table-default-background-td: var(--light-fill-transparent);
	--light-top-bar-default-text: var(--light-text-title);
	--light-top-bar-default-background: var(--light-fill-transparent);
	--light-top-bar-default-background-fill: var(--light-fill-blank);
	--light-top-bar-border: var(--light-primary-l3);
	--light-top-bar-background: var(--light-fill-transparent);
	--light-link-text: var(--light-text-link);
	--light-tag-default-text: var(--light-text-primary);
	--light-tag-default-icon: var(--light-icon-secondary);
	--light-tag-default-button: var(--light-icon-secondary);
	--light-tag-default-border: var(--light-border-dark);
	--light-tag-default-background: var(--light-primary-l1);
	--light-tag-primary-text: var(--light-primary-dark);
	--light-tag-primary-icon: var(--light-primary-default);
	--light-tag-primary-button: var(--light-primary-l6);
	--light-tag-primary-border: var(--light-primary-l2);
	--light-tag-primary-background: var(--light-primary-l1);
	--light-tag-success-text: var(--light-success-dark);
	--light-tag-success-icon: var(--light-success-default);
	--light-tag-success-button: var(--light-success-l6);
	--light-tag-success-border: var(--light-success-l2);
	--light-tag-success-background: var(--light-success-l1);
	--light-tag-error-text: var(--light-error-dark);
	--light-tag-error-icon: var(--light-error-default);
	--light-tag-error-button: var(--light-error-l6);
	--light-tag-error-border: var(--light-error-l2);
	--light-tag-error-background: var(--light-error-l1);
	--light-tag-warning-text: var(--light-warning-dark);
	--light-tag-warning-icon: var(--light-warning-default);
	--light-tag-warning-button: var(--light-warning-l6);
	--light-tag-warning-border: var(--light-warning-l2);
	--light-tag-warning-background: var(--light-warning-l1);
	--light-tag-info-text: var(--light-info-dark);
	--light-tag-info-icon: var(--light-info-default);
	--light-tag-info-button: var(--light-info-l6);
	--light-tag-info-border: var(--light-info-l2);
	--light-tag-info-background: var(--light-info-l1);
	--light-tag-otherflow-text: var(--light-otherflow-dark);
	--light-tag-otherflow-icon: var(--light-otherflow-default);
	--light-tag-otherflow-button: var(--light-otherflow-l6);
	--light-tag-otherflow-border: var(--light-otherflow-l2);
	--light-tag-otherflow-background: var(--light-otherflow-l1);
	--light-tag-accent-text: var(--light-accent-dark);
	--light-tag-accent-icon: var(--light-accent-default);
	--light-tag-accent-button: var(--light-accent-l6);
	--light-tag-accent-border: var(--light-accent-l2);
	--light-tag-accent-background: var(--light-accent-l1);
	--light-tag-doing-text: var(--light-doing-dark);
	--light-tag-doing-icon: var(--light-doing-default);
	--light-tag-doing-button: var(--light-doing-l6);
	--light-tag-doing-border: var(--light-doing-l2);
	--light-tag-doing-background: var(--light-doing-l1);
	--light-form-normal-press-text: var(--light-text-title);
	--light-form-normal-press-text-light: var(--light-text-light);
	--light-form-normal-press-label: var(--light-text-primary);
	--light-form-normal-press-secondary: var(--light-text-secondary);
	--light-form-normal-press-background: var(--light-fill-blank);
	--light-form-normal-press-background-input: var(--light-fill-lighter);
	--light-form-normal-press-border: var(--light-primary-l6);
	--light-form-normal-press-underline: var(--light-border-light);
	--light-form-normal-press-icon-light: var(--light-icon-light);
	--light-form-normal-press-icon-normal: var(--light-icon-primary);
	--light-form-normal-press-icon-primary: var(--light-primary-l5);
	--light-form-normal-press-icon-transparent: var(--light-fill-transparent);
	--light-form-normal-press-icon_switch: var(--light-primary-l4);
	--light-form-normal-press-radio_position_x: 5px;
	--light-form-normal-focus-text: var(--light-text-title);
	--light-form-normal-focus-text-light: var(--light-text-light);
	--light-form-normal-focus-label: var(--light-text-primary);
	--light-form-normal-focus-secondary: var(--light-text-secondary);
	--light-form-normal-focus-background: var(--light-fill-blank);
	--light-form-normal-focus-background_choice: var(--light-primary-l2);
	--light-form-normal-focus-background_input: var(--light-fill-blank);
	--light-form-normal-focus-border: var(--light-primary-light);
	--light-form-normal-focus-icon-light: var(--light-icon-light);
	--light-form-normal-focus-icon-normal: var(--light-icon-primary);
	--light-form-normal-focus-icon-primary: var(--light-primary-light);
	--light-form-normal-focus-icon-transparent: var(--light-primary-light);
	--light-form-normal-focus-icon_switch: var(--light-primary-default);
	--light-form-normal-focus-radio_position_x: 21px;
	--light-form-normal-disable-text: var(--light-text-disabled);
	--light-form-normal-disable-text-light: var(--light-text-disabled);
	--light-form-normal-disable-label: var(--light-text-light);
	--light-form-normal-disable-secondary: var(--light-text-disabled);
	--light-form-normal-disable-background: var(--light-fill-disabled);
	--light-form-normal-disable-border: var(--light-border-lighter);
	--light-form-normal-disable-icon-light: var(--light-icon-disabled);
	--light-form-normal-disable-icon-normal: var(--light-icon-disabled);
	--light-form-normal-disable-icon-primary: var(--light-icon-disabled);
	--light-form-normal-disable-icon-transparent: var(--light-fill-transparent);
	--light-form-normal-disable-icon_switch: var(--light-icon-disabled);
	--light-form-normal-disable-radio_position_x: 5px;
	--light-button-press-normal-text: var(--light-text-primary);
	--light-button-press-normal-background: var(--light-primary-l2);
	--light-button-press-normal-border: var(--light-primary-l6);
	--light-button-press-normal-icon: var(--light-icon-primary);
	--light-button-press-link-text: var(--light-primary-dark);
	--light-button-press-link-background: var(--light-primary-l2);
	--light-button-press-link-border: var(--light-border-transparent);
	--light-button-press-link-icon: var(--light-primary-dark);
	--light-button-press-primary-text: var(--dark-text-title);
	--light-button-press-primary-background: var(--light-primary-dark);
	--light-button-press-primary-border: var(--light-primary-dark);
	--light-button-press-primary-icon: var(--dark-icon-primary);
	--light-button-press-secondary-text: var(--dark-text-primary);
	--light-button-press-secondary-background: var(--dark-fill-dark);
	--light-button-press-secondary-border: var(--dark-neutral-08);
	--light-button-press-secondary-icon: var(--dark-icon-primary);
	--light-button-press-transparent-text: var(--light-text-primary);
	--light-button-press-transparent-background: var(--light-primary-l2);
	--light-button-press-transparent-border: var(--light-border-transparent);
	--light-button-press-transparent-icon: var(--light-icon-secondary);
	--light-button-disable-normal-text: var(--light-text-disabled);
	--light-button-disable-normal-background: var(--light-fill-disabled);
	--light-button-disable-normal-border: var(--light-border-disabled);
	--light-button-disable-normal-icon: var(--light-icon-disabled);
	--light-button-disable-link-text: var(--light-text-disabled);
	--light-button-disable-link-background: var(--light-fill-transparent);
	--light-button-disable-link-border: var(--light-border-transparent);
	--light-button-disable-link-icon: var(--light-icon-disabled);
	--light-button-disable-primary-text: var(--light-text-disabled);
	--light-button-disable-primary-background: var(--light-fill-disabled);
	--light-button-disable-primary-border: var(--light-border-disabled);
	--light-button-disable-primary-icon: var(--light-icon-disabled);
	--light-button-disable-secondary-text: var(--light-text-disabled);
	--light-button-disable-secondary-background: var(--light-fill-disabled);
	--light-button-disable-secondary-border: var(--light-border-disabled);
	--light-button-disable-secondary-icon: var(--light-icon-disabled);
	--light-button-disable-transparent-text: var(--light-text-disabled);
	--light-button-disable-transparent-background: var(--light-fill-transparent);
	--light-button-disable-transparent-border: var(--light-border-transparent);
	--light-button-disable-transparent-icon: var(--light-icon-disabled);
	--light-tabs-focus-fill-dark-text: var(--dark-text-title);
	--light-tabs-focus-fill-dark-background: var(--light-primary-default);
	--light-tabs-focus-fill-dark-border: var(--light-border-transparent);
	--light-tabs-focus-fill-dark-icon: var(--dark-icon-primary);
	--light-tabs-focus-page-text: var(--light-text-title);
	--light-tabs-focus-page-text-sub: var(--light-text-secondary);
	--light-tabs-focus-page-background: var(--light-fill-blank);
	--light-tabs-focus-page-border: var(--light-primary-split);
	--light-tabs-focus-page-icon: var(--light-icon-primary);
	--light-tabs-focus-text-text: var(--light-text-title);
	--light-tabs-focus-text-background: var(--light-fill-transparent);
	--light-tabs-focus-text-border: var(--light-border-transparent);
	--light-tabs-focus-text-icon: var(--light-icon-secondary);
	--light-tabs-focus-fill-light-text: var(--light-text-link);
	--light-tabs-focus-fill-light-background: var(--light-primary-l2);
	--light-tabs-focus-fill-light-border: var(--light-border-transparent);
	--light-tabs-focus-fill-light-icon: var(--light-icon-secondary);
	--light-table-background-th: var(--light-fill-lighter);
	--light-table-border: var(--light-border-darker);
	--light-table-text: var(--light-text-primary);
	--light-table-icon: var(--light-icon-primary);
	--light-table-icon-light: var(--light-icon-light);
	--light-table-secondary: var(--light-text-secondary);
	--light-table-focus-background-td: var(--light-primary-l2);
	--light-form-text-default-text: var(--light-text-title);
	--light-form-text-press-text: var(--light-text-title);
	--light-form-text-press-label: var(--light-text-primary);
	--light-form-text-press-secondary: var(--light-text-secondary);
	--light-form-text-press-background: var(--light-fill-blank);
	--light-form-text-press-border: var(--light-primary-l6);
	--light-form-text-press-icon: var(--light-fill-transparent);
	--light-form-text-press-icon_switch: var(--light-primary-l4);
	--light-form-text-press-radio_position_x: 5px;
	--light-form-text-default-label: var(--light-text-primary);
	--light-form-text-default-secondary: var(--light-text-secondary);
	--light-form-text-default-background: var(--light-fill-blank);
	--light-form-text-default-border: var(--light-primary-split);
	--light-form-text-default-icon: var(--light-fill-transparent);
	--light-form-text-default-icon_switch: var(--light-primary-l4);
	--light-form-text-default-radio_position_x: 5px;
	--light-form-text-focus-text: var(--light-text-title);
	--light-form-text-focus-label: var(--light-text-primary);
	--light-form-text-focus-secondary: var(--light-text-secondary);
	--light-form-text-focus-background_choice: var(--light-primary-l2);
	--light-form-text-focus-background_input: var(--light-fill-blank);
	--light-form-text-focus-border: var(--light-primary-light);
	--light-form-text-focus-icon: var(--light-primary-default);
	--light-form-text-focus-icon_switch: var(--light-primary-default);
	--light-form-text-focus-radio_position_x: 21px;
	--light-form-text-disable-text: var(--light-text-disabled);
	--light-form-text-disable-label: var(--light-text-disabled);
	--light-form-text-disable-secondary: var(--light-text-disabled);
	--light-form-text-disable-background: var(--light-fill-disabled);
	--light-form-text-disable-border: var(--light-border-disabled);
	--light-form-text-disable-icon: var(--light-icon-disabled);
	--light-form-text-disable-icon_switch: var(--light-icon-disabled);
	--light-form-text-disable-radio_position_x: 5px;
	--light-dropdown-default-normal-text: var(--light-text-primary);
	--light-dropdown-default-normal-icon: var(--light-icon-secondary);
	--light-dropdown-default-normal-background: var(--light-fill-transparent);
	--light-dropdown-default-normal-border: var(--light-border-lighter);
	--dark-button-default-normal-text: var(--light-text-title);
	--dark-button-default-normal-background: var(--light-fill-blank);
	--dark-button-default-normal-border: var(--light-primary-l4);
	--dark-button-default-normal-icon: var(--light-icon-primary);
	--dark-button-default-primary-text: var(--dark-text-title);
	--dark-button-default-primary-background: var(--light-primary-default);
	--dark-button-default-primary-border: var(--light-primary-default);
	--dark-button-default-primary-icon: var(--dark-icon-primary);
	--dark-button-default-link-text: var(--dark-text-link);
	--dark-button-default-link-background: var(--dark-fill-transparent);
	--dark-button-default-link-border: var(--dark-border-transparent);
	--dark-button-default-link-icon: var(--dark-icon-link);
	--dark-button-default-secondary-text: var(--dark-text-primary);
	--dark-button-default-secondary-background: var(--dark-fill-alpha-base);
	--dark-button-default-secondary-border: var(--dark-border-transparent);
	--dark-button-default-secondary-icon: var(--dark-icon-primary);
	--dark-button-default-transparent-text: var(--dark-text-title);
	--dark-button-default-transparent-background: var(--dark-fill-transparent);
	--dark-button-default-transparent-border: var(--dark-border-transparent);
	--dark-button-default-transparent-icon: var(--dark-icon-primary);
	--dark-top-bar-default-text: var(--dark-text-title);
	--dark-top-bar-default-background-none: var(--dark-fill-transparent);
	--dark-top-bar-default-background-fill: var(--dark-fill-blank);
	--dark-top-bar-border: var(--light-primary-l3);
	--dark-form-placeholder: var(--light-text-placeholder);
	--dark-form-normal-default-text: var(--dark-text-primary);
	--dark-form-normal-default-text-light: var(--dark-text-light);
	--dark-form-normal-default-label: var(--dark-text-title);
	--dark-form-normal-default-secondary: var(--dark-text-secondary);
	--dark-form-normal-default-background: var(--dark-fill-blank);
	--dark-form-normal-default-background-input: var(--dark-fill-lighter);
	--dark-form-normal-default-border: var(--dark-border-light);
	--dark-form-normal-default-underline: var(--dark-border-lighter);
	--dark-form-normal-default-icon-light: var(--dark-icon-light);
	--dark-form-normal-default-icon-normal: var(--dark-icon-primary);
	--dark-form-normal-default-icon-primary: var(--light-primary-l5);
	--dark-form-normal-default-icon-transparent: var(--dark-fill-transparent);
	--dark-form-normal-default-icon_switch: var(--light-primary-l4);
	--dark-form-normal-default-radio_position_x: 5px;
	--dark-form-normal-press-text: var(--dark-text-primary);
	--dark-form-normal-press-text-light: var(--dark-text-light);
	--dark-form-normal-press-label: var(--dark-text-primary);
	--dark-form-normal-press-secondary: var(--dark-text-secondary);
	--dark-form-normal-press-background: var(--dark-fill-blank);
	--dark-form-normal-press-background-input: var(--dark-fill-lighter);
	--dark-form-normal-press-border: var(--light-primary-l6);
	--dark-form-normal-press-underline: var(--dark-border-lighter);
	--dark-form-normal-press-icon-light: var(--dark-icon-light);
	--dark-form-normal-press-icon-normal: var(--dark-icon-primary);
	--dark-form-normal-press-icon-primary: var(--light-primary-l5);
	--dark-form-normal-press-icon-transparent: var(--dark-fill-transparent);
	--dark-form-normal-press-icon_switch: var(--light-primary-l4);
	--dark-form-normal-press-radio_position_x: 5px;
	--dark-form-normal-focus-text: var(--dark-text-primary);
	--dark-form-normal-focus-text-light: var(--dark-text-light);
	--dark-form-normal-focus-label: var(--dark-text-primary);
	--dark-form-normal-focus-secondary: var(--dark-text-secondary);
	--dark-form-normal-focus-background: var(--dark-fill-blank);
	--dark-form-normal-focus-background_choice: var(--light-primary-l2);
	--dark-form-normal-focus-background_input: var(--dark-fill-blank);
	--dark-form-normal-focus-border: var(--light-primary-light);
	--dark-form-normal-focus-icon-light: var(--dark-icon-light);
	--dark-form-normal-focus-icon-normal: var(--dark-icon-primary);
	--dark-form-normal-focus-icon-primary: var(--light-primary-light);
	--dark-form-normal-focus-icon-transparent: var(--light-primary-light);
	--dark-form-normal-focus-icon_switch: var(--light-primary-default);
	--dark-form-normal-focus-radio_position_x: 21px;
	--dark-form-normal-disable-text: var(--dark-text-placeholder);
	--dark-form-normal-disable-text-light: var(--dark-text-disabled);
	--dark-form-normal-disable-label: var(--dark-text-disabled);
	--dark-form-normal-disable-secondary: var(--dark-text-disabled);
	--dark-form-normal-disable-background: var(--dark-fill-disabled);
	--dark-form-normal-disable-border: var(--dark-border-lighter);
	--dark-form-normal-disable-icon-light: var(--dark-icon-disabled);
	--dark-form-normal-disable-icon-normal: var(--dark-icon-disabled);
	--dark-form-normal-disable-icon-primary: var(--dark-icon-disabled);
	--dark-form-normal-disable-icon-transparent: var(--dark-fill-transparent);
	--dark-form-normal-disable-icon_switch: var(--dark-icon-disabled);
	--dark-form-normal-disable-radio_position_x: 5px;
	--dark-form-text-default-text: var(--light-text-primary);
	--dark-form-text-default-label: var(--light-text-primary);
	--dark-form-text-default-secondary: var(--light-text-secondary);
	--dark-form-text-default-background: var(--light-fill-blank);
	--dark-form-text-default-border: var(--light-primary-split);
	--dark-form-text-default-icon: var(--light-fill-transparent);
	--dark-form-text-default-icon_switch: var(--light-primary-l4);
	--dark-form-text-default-radio_position_x: 5px;
	--dark-form-text-press-text: var(--light-text-primary);
	--dark-form-text-press-label: var(--light-text-primary);
	--dark-form-text-press-secondary: var(--light-text-secondary);
	--dark-form-text-press-background: var(--light-fill-blank);
	--dark-form-text-press-border: var(--light-primary-l6);
	--dark-form-text-press-icon: var(--light-fill-transparent);
	--dark-form-text-press-icon_switch: var(--light-primary-l4);
	--dark-form-text-press-radio_position_x: 5px;
	--dark-form-text-focus-text: var(--light-text-primary);
	--dark-form-text-focus-label: var(--light-text-primary);
	--dark-form-text-focus-secondary: var(--light-text-secondary);
	--dark-form-text-focus-background_choice: var(--light-primary-l2);
	--dark-form-text-focus-background_input: var(--light-fill-blank);
	--dark-form-text-focus-border: var(--light-primary-light);
	--dark-form-text-focus-icon: var(--light-primary-default);
	--dark-form-text-focus-icon_switch: var(--light-primary-default);
	--dark-form-text-focus-radio_position_x: 21px;
	--dark-form-text-disable-text: var(--light-text-disabled);
	--dark-form-text-disable-label: var(--light-text-disabled);
	--dark-form-text-disable-secondary: var(--light-text-disabled);
	--dark-form-text-disable-background: var(--light-fill-disabled);
	--dark-form-text-disable-border: var(--light-border-disabled);
	--dark-form-text-disable-icon: var(--light-icon-disabled);
	--dark-form-text-disable-icon_switch: var(--light-icon-disabled);
	--dark-form-text-disable-radio_position_x: 5px;
	--dark-top-bar-background: var(--light-fill-transparent);
	--dark-link-text: var(--light-text-link);
	--dark-button-press-normal-text: var(--light-text-primary);
	--dark-button-press-normal-background: var(--light-primary-l2);
	--dark-button-press-normal-border: var(--light-primary-l6);
	--dark-button-press-normal-icon: var(--light-icon-primary);
	--dark-button-press-primary-text: var(--dark-text-title);
	--dark-button-press-primary-background: var(--light-primary-dark);
	--dark-button-press-primary-border: var(--light-primary-dark);
	--dark-button-press-primary-icon: var(--dark-icon-primary);
	--dark-button-press-link-text: var(--dark-text-link);
	--dark-button-press-link-background: var(--dark-fill-alpha-lighter);
	--dark-button-press-link-border: var(--dark-border-transparent);
	--dark-button-press-link-icon: var(--dark-icon-link);
	--dark-button-press-secondary-text: var(--dark-text-primary);
	--dark-button-press-secondary-background: var(--dark-fill-alpha-light);
	--dark-button-press-secondary-border: var(--dark-border-transparent);
	--dark-button-press-secondary-icon: var(--dark-icon-primary);
	--dark-button-press-transparent-text: var(--dark-text-title);
	--dark-button-press-transparent-background: var(--dark-fill-alpha-lighter);
	--dark-button-press-transparent-border: var(--dark-border-transparent);
	--dark-button-press-transparent-icon: var(--dark-icon-primary);
	--dark-button-disable-normal-text: var(--light-text-disabled);
	--dark-button-disable-normal-background: var(--light-fill-disabled);
	--dark-button-disable-normal-border: var(--light-border-disabled);
	--dark-button-disable-normal-icon: var(--light-icon-disabled);
	--dark-button-disable-primary-text: var(--light-primary-l7);
	--dark-button-disable-primary-background: var(--light-primary-default);
	--dark-button-disable-primary-border: var(--light-primary-default);
	--dark-button-disable-primary-icon: var(--light-primary-l7);
	--dark-button-disable-link-text: var(--dark-text-disabled);
	--dark-button-disable-link-background: var(--light-fill-transparent);
	--dark-button-disable-link-border: var(--light-border-transparent);
	--dark-button-disable-link-icon: var(--dark-icon-disabled);
	--dark-button-disable-secondary-text: var(--dark-text-disabled);
	--dark-button-disable-secondary-background: var(--dark-fill-lighter);
	--dark-button-disable-secondary-border: var(--dark-border-transparent);
	--dark-button-disable-secondary-icon: var(--dark-icon-disabled);
	--dark-button-disable-transparent-text: var(--dark-text-disabled);
	--dark-button-disable-transparent-background: var(--dark-fill-transparent);
	--dark-button-disable-transparent-border: var(--dark-fill-transparent);
	--dark-button-disable-transparent-icon: var(--dark-icon-disabled);
	--light-tab-bar-default-text: var(--light-text-secondary);
	--light-tab-bar-default-icon: var(--light-icon-secondary);
	--light-tab-bar-default-background-none: var(--light-fill-transparent);
	--light-tab-bar-default-background-fill: var(--light-fill-blank);
	--light-tab-bar-focus-text: var(--light-text-primary);
	--light-tab-bar-focus-icon: var(--light-icon-primary);
	--light-tab-bar-focus-background-none: var(--light-fill-transparent);
	--light-tab-bar-focus-background-fill: var(--light-fill-blank);
	--light-button-default-lighter-text: var(--light-text-primary);
	--light-button-default-lighter-background: var(--light-primary-l1);
	--light-button-default-lighter-border: var(--light-border-transparent);
	--light-button-default-lighter-icon: var(--light-icon-secondary);
	--light-button-press-lighter-text: var(--light-text-primary);
	--light-button-press-lighter-background: var(--light-primary-l2);
	--light-button-press-lighter-border: var(--light-border-transparent);
	--light-button-press-lighter-icon: var(--light-icon-secondary);
	--light-button-disable-lighter-text: var(--light-text-disabled);
	--light-button-disable-lighter-background: var(--light-fill-light);
	--light-button-disable-lighter-border: var(--light-border-transparent);
	--light-button-disable-lighter-icon: var(--light-icon-disabled);
	--dark-button-default-lighter-text: var(--dark-text-title);
	--dark-button-default-lighter-background: var(--dark-fill-lighter);
	--dark-button-default-lighter-border: var(--dark-border-transparent);
	--dark-button-default-lighter-icon: var(--dark-text-title);
	--dark-button-press-lighter-text: var(--dark-text-title);
	--dark-button-press-lighter-background: var(--dark-fill-alpha-light);
	--dark-button-press-lighter-border: var(--dark-border-transparent);
	--dark-button-press-lighter-icon: var(--dark-text-title);
	--dark-button-disable-lighter-text: var(--dark-text-disabled);
	--dark-button-disable-lighter-background: var(--dark-fill-disabled);
	--dark-button-disable-lighter-border: var(--dark-fill-transparent);
	--dark-button-disable-lighter-icon: var(--dark-icon-disabled);
	--light-tabs-default-card-text: var(--light-text-secondary);
	--light-tabs-default-card-text-sub: var(--light-text-secondary);
	--light-tabs-default-card-background: var(--light-fill-light);
	--light-tabs-default-card-border: var(--light-primary-split);
	--light-tabs-default-card-icon: var(--light-icon-primary);
	--light-tabs-focus-card-text: var(--light-text-title);
	--light-tabs-focus-card-text-sub: var(--light-text-secondary);
	--light-tabs-focus-card-background: var(--light-general-blank);
	--light-tabs-focus-card-border: var(--light-primary-split);
	--light-tabs-focus-card-icon: var(--light-icon-primary);
	--light-tag-important-text: var(--light-orange-10);
	--light-tag-important-icon: var(--light-important-default);
	--light-tag-important-button: var(--light-important-l6);
	--light-tag-important-border: var(--light-important-l2);
	--light-tag-important-background: var(--light-important-l1);
	--light-dropdown-default-focus-text: var(--light-text-link);
	--light-dropdown-default-focus-icon: var(--light-icon-link);
	--light-dropdown-default-focus-background: var(--light-fill-transparent);
	--light-dropdown-default-focus-border: var(--light-border-lighter);
	--light-dropdown-default-disable-text: var(--light-text-disabled);
	--light-dropdown-default-disable-icon: var(--light-icon-disabled);
	--light-dropdown-default-disable-background: var(--light-fill-transparent);
	--light-dropdown-default-disable-border: var(--light-border-disabled);
	--light-dropdown-fill-normal-text: var(--light-text-primary);
	--light-dropdown-fill-normal-icon: var(--light-icon-secondary);
	--light-dropdown-fill-normal-background: var(--light-fill-blank);
	--light-dropdown-fill-normal-border: var(--light-border-dark);
	--light-dropdown-fill-focus-text: var(--light-text-link);
	--light-dropdown-fill-focus-icon: var(--light-icon-link);
	--light-dropdown-fill-focus-background: var(--light-primary-l1);
	--light-dropdown-fill-focus-border: var(--light-primary-light);
	--light-dropdown-fill-disable-text: var(--light-text-disabled);
	--light-dropdown-fill-disable-icon: var(--light-icon-disabled);
	--light-dropdown-fill-disable-background: var(--light-fill-disabled);
	--light-dropdown-fill-disable-border: var(--light-border-disabled);
}
