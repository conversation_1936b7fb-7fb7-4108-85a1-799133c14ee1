<template>
	<view class="ui-layout" :style="layoutStyle" :class="{ 'safe-bottom': safeAreaInsetBottom }">
		<view class="ui-layout-header">
			<slot name="header"></slot>
		</view>
		<view class="ui-layout-body">
			<slot name="default"></slot>
		</view>
		<view class="ui-layout-footer">
			<slot name="footer"></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import type { LayoutProps } from './types'

defineOptions({
	name: 'UiLayout',
})

const props = withDefaults(defineProps<LayoutProps>(), {
	safeAreaInsetTop: false,
	safeAreaInsetBottom: false,
})

const layoutStyle = ref({})

onMounted(() => {
	layoutStyle.value = initStyle(props.safeAreaInsetTop)
})

const initStyle = (safeAreaInsetTop: boolean) => {
	if (!safeAreaInsetTop) {
		return {
			background: props.background,
		}
	}
	const paddingTop = uni.getMenuButtonBoundingClientRect
		? `${uni.getMenuButtonBoundingClientRect().bottom * 2}rpx`
		: 'env(safe-area-inset-top)'

	return {
		paddingTop: paddingTop,
		background: props.background,
	}
}
</script>
<style scoped lang="scss">
@use './ui-layout.scss';
</style>
