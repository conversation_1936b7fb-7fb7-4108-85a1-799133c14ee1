import { sendCode<PERSON>pi } from '@/api/login.api'
import Cache from '../cache'
import Config from './config'
import Auth from './auth'
import WechatSDK from './wechat'

export default class {
	public static info = Cache.getUser() || {}

	public static config = Config
	public static requestReady = false
	public static auth = Auth
	public static loginStatus = Auth.loginStatus // 登录状态 : 1.未登录服务器或登录失败  2.登录服务成功  3.手机登录成功

	public static async login() {
		this.auth.loginSuccess = () => {
			this.requestReady = true
			this.config.init().then(() => {
				new WechatSDK() // 初始化微信sdk
			})
		}
		await this.auth.login()
	}

	public static async logOut() {
		this.auth.phone = ''
		this.auth.verifyCode = ''
		return await this.auth.logOut()
	}

	// 发送手机验证码
	public static sendCode(phone: string) {
		return new Promise((resolve, reject) => {
			sendCode<PERSON><PERSON>({ phone })
				.then(res => {
					console.log('验证码发送成功！')
					resolve(res)
				})
				.catch(err => {
					console.log('验证码发送失败！')
					reject(err)
				})
		})
	}

	public static setMobileLoginParams(phone: string, verifyCode: string) {
		this.auth.phone = phone
		this.auth.verifyCode = verifyCode
	}
}
