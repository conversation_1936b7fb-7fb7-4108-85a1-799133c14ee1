<template>
	<view :class="[
		'ui-input-wrap',
		...getWrapClass(),
		inputFocus ? getFocusClass(true) : '',
		background || !bottomLine ? '' : 'ui-input-border-bottom'
	]" >
		<view class="ui-input-label-inner" :style="{ width: labelWidth }">
			<slot name="labelLeft"></slot>
			<view
				:class="[
					'ui-input-label',
					`ui-input-height-${size}`,
					`ui-input-label-${size}`,
					disabled ? 'ui-input-label-disabled' : '',
					getReauiredClass(),
				]"
				v-if="label"
			>
				{{ label}}
			</view>
			<slot name="labelRight"></slot>
		</view>
		
		<view :class="[
			'ui-input-inner',
			label && !vertical ? 'ui-input-padding-left' : '',
			background && vertical ? 'ui-input-background' : '',
			inputFocus ? getFocusClass(false) : '',
		]">
			<textarea
				v-if="type === 'textarea'"
				:class="[
					'ui-input-input',
					'ui-input-textarea',
					disabled ? 'ui-input-input-disabled' : '',
				]"
				placeholder-class="ui-input-placeholder"
				v-model="bindValue"
				:placeholder="placeholder"
				:disabled="disabled"
				:auto-height="true"
				:maxlength="maxlength"
			/>
			<input
				v-else
				:class="[
					'ui-input-input',
					`ui-input-height-${size}`,
					`ui-input-input-${size}`,
					disabled ? 'ui-input-input-disabled' : '',
				]"
				placeholder-class="ui-input-placeholder"
				:type="type"
				v-model="bindValue"
				:placeholder="placeholder"
				:disabled="disabled"
				@focus="handleFocus"
				@blur="handleBlur"
			/>
			<view class="right-icon-wrap">
				<slot name="deleteIconLeft"></slot>
				<i v-if="clearable && bindValue.length" class="iconfont-uniapp icon-uniapp-input-delete" @click="clearClick"></i>
				<slot name="deleteIconRight"></slot>
			</view>
			
		</view>
		
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import type { InputProps } from './types'

defineOptions({
	name: 'UiInput',
	options: {
		styleIsolation: 'shared',
		virtualHost: true,
	},
})

const props = withDefaults(defineProps<InputProps>(), {
	modelValue: '',
	type: 'text',
	background: false,
	labelWidth: undefined,
	vertical: false,
	size: 'md',
	label: '',
	placeholder: '请输入',
	disabled: false,
	required: false,
	clearable: false,
	bottomLine: true,
})

const bindValue = ref(props.modelValue)
const inputFocus = ref(false)

const emits = defineEmits(['click', 'update:modelValue', 'blur', 'focus'])

onMounted(() => {
	
})

const handleFocus = () => {
	inputFocus.value = true
	emits('focus', bindValue.value)
}
const handleBlur = () => {
	inputFocus.value = false
	emits('blur', bindValue.value)
}
// 清除输入框内容
const clearClick = () => {
	bindValue.value = ''
}
// 整体布局样式
const getWrapClass = () => {
	const { vertical, background } = props
	const baseClass = [
		vertical ? '' : 'ui-input-direction-row',
		background && !vertical ? 'ui-input-background' : '',
	]
	return baseClass
}

// 必填样式
const getReauiredClass = () => {
	const { required, disabled, size } = props
	return disabled ? [required ? `required-disabled-${size}` : ''] : [required ? `required-${size}` : '']
}

// 获取焦点样式
const getFocusClass = (wrap: boolean) => {
	const { vertical, background, bottomLine } = props
	if (wrap) {
		if (background) {
			if (vertical) {
				return ''
			} else {
				if (bottomLine) {
					return 'ui-border-focus'
				} else {
					return ''
				}
			}
		} else {
			if (vertical) {
				return ''
			} else {
				if (bottomLine) {
					return 'ui-border-bottom-focus'
				} else {
					return ''
				}
				
			}
		}
	} else {
		if (background) {
			if (vertical) {
				return 'ui-border-focus'
			} else {
				return ''
			}
		} else {
			if (vertical) {
				if (bottomLine) {
					return 'ui-border-bottom-focus'
				} else {
					return ''
				}
			} else {
				return ''
			}
			
		}
	}
}

watch(
	() => bindValue.value,
	(newValue: string | number) => {
		emits('update:modelValue', newValue)
	},
)

watch(
	() => props.modelValue,
	(newValue: string | number) => {
		console.log('0000')
		bindValue.value = newValue
	},
)

</script>
<style lang="scss">
@use './ui-input.scss';
</style>
