<template>
	<Layout
		title=""
		:currentIndex="2"
		nav-bar-background="transparent"
		backgroundImageType="yuangong"
		left-text=""
		@left-click="navigateBack"
	>
		<div class="yuangong-content">
			<div class="yuangong-avatar">
				<UiAvatar style="width: 320rpx; height: 320rpx"></UiAvatar>
			</div>
			<div class="yuangong-list">
				<UiCell :dataItem="cellConfig1"></UiCell>
				<UiCell :dataItem="cellConfig2"></UiCell>
				<UiCell :dataItem="cellConfig3"></UiCell>
				<UiCell :dataItem="cellConfig4"></UiCell>
			</div>
		</div>
		<UiFooterBtns :btnList="btnList"></UiFooterBtns>
	</Layout>
</template>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import Layout from '@/layout/index.vue'
import type { CellConfig } from '@e-cloud/eslink-plus-uniapp/components/ui-cell/types'
import UiAvatar from '@e-cloud/eslink-plus-uniapp/components/ui-avatar/ui-avatar.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiFooterBtns from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/ui-footer-btns.vue'
import { showMsg } from '@/core/util'
import { onLoad } from '@dcloudio/uni-app'

const cellConfig1 = computed(() => {
	return {
		title: '公司',
		size: 'sm',
		value: '金卡智能集团',
	} as CellConfig
})
const cellConfig2 = computed(() => {
	return {
		title: '部门',
		size: 'sm',
		value: '金卡智能集团',
	} as CellConfig
})
const cellConfig3 = computed(() => {
	return {
		title: '姓名',
		size: 'sm',
		value: '金卡智能集团',
	} as CellConfig
})
const cellConfig4 = computed(() => {
	return {
		title: '工号',
		size: 'sm',
		value: '金卡智能集团',
	} as CellConfig
})

onLoad((options: any) => {
	showMsg(options.code)
})
const navigateBack = () => {
	uni.navigateBack()
}
const btnList = ref([
	{
		text: '确定',
		type: 'secondary',
		size: 'lg',
		click: () => {
			navigateBack()
		},
	},
])
</script>
<style lang="scss" scoped>
.yuangong-avatar {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-top: 58rpx;
	margin-bottom: 48rpx;
}
</style>
