.ui-popup-message {
	display: flex;
	flex-direction: row;
	justify-content: center;
}

.ui-popup-message-box {
	background-color: #e1f3d8;
	padding: 20rpx 30rpx;
	border-color: #eee;
	border-style: solid;
	border-width: 2rpx;
	flex: 1;
}

@media screen and (min-width: 1000rpx) {
	.fixforpc-width {
		max-width: 50%;
		margin-top: 40rpx;
		border-radius: 8rpx;
		flex: none;
		min-width: 760rpx;
	}
}

.ui-popup-message-text {
	font-size: 28rpx;
	padding: 0;
}

.ui-popup-success {
	background-color: #e1f3d8;
}

.ui-popup-success-text {
	color: #67C23A;
}

.ui-popup-warning {
	background-color: #faecd8;
}

.ui-popup-warning-text {
	color: #E6A23C;
}

.ui-popup-error {
	background-color: #fde2e2;
}

.ui-popup-error-text {
	color: #F56C6C;
}

.ui-popup-info {
	background-color: #F2F6FC;
}

.ui-popup-info-text {
	color: #909399;
}
