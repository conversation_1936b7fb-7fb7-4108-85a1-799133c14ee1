const fs = require('fs-extra')
const path = require('path')

;(async () => {
	console.log('🚀 开始拷贝 eslink-plus-uniapp 模块到 uni_modules 目录...')

	// const sourceDir = path.resolve(__dirname, '../../eslink-plus-uniapp')
	const sourceDir = path.resolve(__dirname, '../node_modules/@e-cloud/eslink-plus-uniapp')
	const targetDir = path.resolve(__dirname, '../src/uni_modules/eslink-plus-uniapp')
	console.log('🚀 拷贝目录：', sourceDir, targetDir)
	try {
		await fs.ensureDir(targetDir)
		await fs.copy(`${sourceDir}/packages`, targetDir)
	} catch (error) {
		console.log('❌ 拷贝失败：', error)
	}

	// 为以后遵循uni_modules规范实现easycom做准备

	const sourcePkgPath = `${sourceDir}/package.json`
	const sourcePkg = await fs.readJson(sourcePkgPath)

	// 暂时先读取这两个字段内容
	const newPkg = {
		name: sourcePkg.name,
		version: sourcePkg.version,
	}

	const targetPkgPath = `${targetDir}/package.json`
	await fs.writeJson(targetPkgPath, newPkg, { spaces: 2 })

	console.log('✅ Copy and setup completed.')
})()
