export enum PageOpenType {
	Navigate = 'navigate',
	Redirect = 'redirect',
	ReLaunch = 'reLaunch',
	SwitchTab = 'switchTab',
	NavigateBack = 'navigateBack',
	None = 'none',
}

export interface TabbarListItem {
	pagePath: string
	text: string
	iconPath?: string
	selectedIconPath?: string
	visible?: boolean
	iconfont?: string
	selectedIconfont?: string
	openType?: keyof typeof PageOpenType
	dot?: boolean
}

export interface TabbarProps {
	/**
	 * @description tabbar的list属性
	 */
	list: TabbarListItem[]
	/**
	 * 当前下标
	 */
	current?: number
	/**
	 * @description 是否固定底部，为true时自动开启安全区域
	 */
	fixed?: boolean
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
	/**
	 * @description 切换时拦截事件(有兼容性,部分平台不支持)
	 */
	beforeChange?: null | ((func: Function, index: number) => void)
	/**
	 * @description 图标字体名
	 */
	iconfontClass?: string
	/**
	 * @description 自定义tabbar的name,方便读取默认tabbar
	 */
	name?: string
	/**
	 * @description 是否开启底部安全区域
	 */
	safeAreaInsetBottom?: boolean
}
