<template>
	<view class="btn-list-wrap">
		<view class="btn-item-wrap" v-for="btn in btnList" :key="btn.title">
			<UiButton
				:type="btn.btnType"
				:size="btn.btnSize"
				width="100%"
				:style-name="btn.btnStyle"
				:text="btn.title"
				@click="btnClick(btn)"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { PropType } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'

defineOptions({
	name: 'FootBtn',
})

type ButtonConfig = {
	title: string
	btnType: string
	btnSize: string
	btnStyle: string
	click: () => void
}

const props = defineProps({
	// 绑定的用户
	btnList: {
		type: Array as PropType<ButtonConfig[]>,
		default: [],
	},
})

// 按钮点击
const btnClick = (btn: ButtonConfig) => {
	props.btnList.forEach(item => {
		if (item.title === btn.title) {
			item.click && item.click()
		}
	})
}
</script>

<style lang="scss" scoped>
.btn-list-wrap {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: var(--light-bg-base);
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: var(--content-default-padding-md);
	gap: var(--button-xlg-margin);
	.btn-item-wrap {
		flex: 1;
	}
}
</style>
