<template>
	<view class="ui-tabbar-wrapper" :style="tabbarBoxStyle" :class="{ fixed: fixed }">
		<view
			id="ui-tabbar"
			class="ui-tabbar"
			:class="{
				fixed: fixed,
				'ui-tabbar-safe': fixed || safeAreaInsetBottom,
			}"
		>
			<view
				v-for="(item, index) in tabbarList"
				:key="index"
				class="ui-tabbar-item"
				:class="{ 'ui-tabbar-active': index === currentIndex }"
				@click="handleTabChange(index)"
			>
				<slot :name="`tabbar-index-${index}`" :item="item">
					<view class="ui-tabbar-item-icon">
						<i
							v-if="!!item.iconfont"
							class="ui-tabbar-icon-iconfont"
							:class="[
								props.iconfontClass,
								currentIndex === index ? item.selectedIconfont : item.iconfont,
							]"
						></i>
						<svg v-else class="ui-tabbar-icon-svg">
							<use :xlink:href="currentIndex === index ? item.selectedIconPath : item.iconPath"></use>
						</svg>
						<view class="ui-tabbar-badge" v-if="item.dot"></view>
					</view>
					<text class="ui-tabbar-item-label" :class="{ 'ui-tabbar-item-select': index === currentIndex }">
						{{ item.text }}
					</text>
				</slot>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, watch } from 'vue'
import type { Ref } from 'vue'
import { PageOpenType } from './types'
import type { TabbarProps, TabbarListItem } from './types'

// style转为string
const obj2strStyle = obj => {
	return Object.entries(obj).map(([key, value]) => `${key}:${value};`)
}

// 自动填充路径
const padFirstSymbol = (str: string, smb: string) => {
	if (str.startsWith(smb) || str.startsWith('http')) {
		return str
	}
	return `/${str}`
}

// 替换原生路径问题
const replaceTabbarList = (list: Array<TabbarListItem>) => {
	if (!(list.length > 0)) {
		return []
	}

	return list.map(item => ({
		...item,
		pagePath: padFirstSymbol(item.pagePath, '/'),
		iconPath: item.iconPath ? padFirstSymbol(item.iconPath, '/') : '',
		selectedIconPath: item.selectedIconPath ? padFirstSymbol(item.selectedIconPath, '/') : '',
	}))
}

// 设置缓存，方便重复调用方法
const setTabbarStorage = (key: string, value: any, name: string) => {
	try {
		uni.setStorageSync(`uiTab_${key}_${name}`, value)
	} catch (e) {
		console.error(e)
	}
}

// 读取缓存
const getTabbarStorage = (key: string, name: string) => {
	try {
		const value = uni.getStorageSync(`uiTab_${key}_${name}`)
		if (value) {
			return value
		}
		return ''
	} catch (e) {
		console.error(e)
	}
}

defineOptions({
	name: 'UiTabbar',
})

const props = withDefaults(defineProps<TabbarProps>(), {
	current: 0,
	fixed: false,
	zIndex: 99,
	beforeChange: null,
	name: 'custom',
	iconfontClass: 'iconfont',
	safeAreaInsetBottom: true,
})

const emits = defineEmits(['change', 'click'])

const currentIndex = ref(0)
const beforeData: Ref = ref({})
const reloading = ref(false) //uni不支持computed的get set，使用reload无感重置刷新
const jumpPage = ref(0)
const tabbar = ref({})

const tabbarList = computed(() => {
	const list = tabbar.value.list
	if (reloading.value) {
		// 无感重置刷新
	}
	if (!list) {
		console.error('list is null')
		return []
	}
	return replaceTabbarList(list)
})
const tabbarBoxStyle = computed(() => {
	if (reloading.value) {
		// 无感重置刷新
	}
	return obj2strStyle({ 'z-index': props.zIndex })
})

onBeforeMount(() => {
	tabbar.value = updateTabbar(props.list)
	_initTabbar()
})

watch(
	() => props.current,
	(newValue: number) => {
		currentIndex.value = newValue
	},
)

// 从[m-tabbar](https://ext.dcloud.net.cn/plugin?id=10080)逻辑修改而来;
// 将setTabBarBadge和setTabBarItem方法提炼为watch监听
watch(
	() => props.list,
	(newValue: Array<TabbarListItem>) => {
		tabbar.value = updateTabbar(newValue)
		_setTabbarStorage('tabbarConfig', newValue)
	},
)

const _initTabbar = () => {
	currentIndex.value = Number(props.current)
	_setTabbarStorage('tabbarConfig', tabbar.value)
}

const _setTabbarStorage = (key: string, value: any) => {
	setTabbarStorage(key, value, props.name)
	_setReload()
}

const updateTabbar = (list: Array<TabbarListItem>) => {
	if (!list) {
		return _getDefaultTabbar()
	}
	return { list: list }
}
const _getDefaultTabbar = () => {
	return getTabbarStorage('tabbarConfig', props.name)
}

const _setReload = () => {
	reloading.value = true
	setTimeout(() => {
		reloading.value = false
	})
}

const handleTabChange = (index: number) => {
	emits('click', index)
	if (index === currentIndex.value) {
		return
	}

	beforeData.value = {
		newIndex: index,
		oldIndex: currentIndex.value,
		next: jumpPage.value,
	}

	if (!props.beforeChange) {
		_jumpPage()
	} else {
		props.beforeChange(_jumpPage, index)
	}
}

const _jumpPage = () => {
	const list = tabbarList.value
	const index = beforeData.value.newIndex
	const { pagePath: url, openType } = list[index]
	if (!url) {
		emits('change', index)
		return
	}
	if (openType !== PageOpenType.Navigate) {
		currentIndex.value = index
	}

	// 允许用户自定义跳转
	if (openType === PageOpenType.None) {
		return
	}

	switch (openType) {
		case PageOpenType.Navigate:
			uni.navigateTo({ url })
			break
		case PageOpenType.Redirect:
			uni.redirectTo({ url })
			break
		case PageOpenType.ReLaunch:
			uni.reLaunch({ url })
			break
		case PageOpenType.SwitchTab:
			uni.switchTab({ url })
			break
		case PageOpenType.NavigateBack:
			uni.navigateBack({ delta: 1 })
			break
		default:
			uni.reLaunch({ url })
	}
}
</script>
<style scoped lang="scss">
@use './ui-tabbar.scss';
</style>
