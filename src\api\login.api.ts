import request from '@/core/request'

// 登录到服务器
export function loginService<PERSON><PERSON>(params: object): Promise<any> {
	return request({
		url: '/channel/login',
		data: params,
		errorMsg: '登录服务器失败',
	})
}

// 发送手机验证码
export function sendCodeApi(params: object): Promise<any> {
	return request({
		url: '/sms/sendCode',
		data: params,
		errorMsg: '发送验证码失败',
	})
}

// 手机号登录
export function loginMobileApi(params: object): Promise<any> {
	return request({
		url: '/sms/codeAuth',
		data: params,
		errorMsg: '手机号认证失败',
	})
}

// 退出登录
export function logOutApi(): Promise<any> {
	return request({
		url: '/channel/logout',
		errorMsg: '退出登录失败',
	})
}

// =====================以下接口暂未用到=============================

export function getChannelApi(): Promise<any> {
	return request({
		url: '/reserve/getChannelCode',
	})
}
