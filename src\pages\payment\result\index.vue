<template>
	<Layout show-footer-btns layout-background="paymentLinearGradient" :footerBtnList="btnList">
		<view class="payment-result-view">
			<UiResult title="缴费成功">
				<view class="payment-amount">
					<text class="amount-unit">￥</text>
					<UiNumber :value="amount" weight="normal" value-size="64rpx"></UiNumber>
				</view>
			</UiResult>
			<view class="payment-info">
				<text class="info-title">缴费信息</text>
				<view class="info-list">
					<view v-for="info in infoList" :key="info.prop" class="info-item">
						<text class="info-label">{{ info.label }}</text>
						<text class="info-value">{{ infoData[info.prop] }}{{ info.unit }}</text>
					</view>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { onBeforeMount, ref } from 'vue'
import UiNumber from '@e-cloud/eslink-plus-uniapp/components/ui-number/ui-number.vue'
import UiResult from '@e-cloud/eslink-plus-uniapp/components/ui-result/ui-result.vue'
import Layout from '@/layout/index.vue'

defineOptions({
	name: 'PaymentResult',
})

const amount = ref(300)
const infoData = ref({})
const infoList = ref([
	{ label: '订单编号', prop: '1', unit: '' },
	{ label: '缴费金额', prop: '2', unit: '元' },
	{ label: '账单缴费', prop: '3', unit: '元' },
	{ label: '充值表具', prop: '4', unit: '' },
	{ label: '充值金额', prop: '5', unit: '元' },
	{ label: '表具余额', prop: '6', unit: '元' },
])
const btnList = ref([{ label: '返回', type: 'normal', text: '完成', size: 'xlg' }])

onBeforeMount(() => {
	initInfoData()
})

const initInfoData = () => {
	infoData.value = {
		1: '5860000',
		2: '300.00',
		3: '218.40',
		4: 'JK586511002',
		5: '81.60',
		6: '81.60',
	}
}
</script>
<style scoped lang="scss">
.payment-result-view {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx 48rpx;
}

.payment-amount {
	display: flex;
	align-items: baseline;
	margin-bottom: 48rpx;
}

.amount-unit {
	color: var(--light-form-normal-default-text);
	font-weight: 600;
	font-size: var(--font-title-lg);
	line-height: 56rpx;
}

.payment-info {
	width: 100%;
	background: var(--light-fill-blank);
	border-radius: var(--content-default-radius-md);
	padding: var(--content-default-padding-xs) var(--content-default-padding-md);
}

.info-title {
	display: block;
	height: var(--list-md-height);
	color: var(--light-text-title);
	font-weight: 600;
	font-size: var(--font-title-md);
	line-height: var(--list-md-height);
}

.info-list {
	display: flex;
	flex-direction: column;
}

.info-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: var(--list-md-height);
	font-size: var(--font-body-md);
}

.info-label {
	color: var(--light-form-normal-default-label);
	font-weight: 400;
}

.info-value {
	color: var(--light-form-normal-default-text);
	font-weight: 600;
}
</style>
