<template>
	<view class="bill-detail-popup-content">
		<view class="bill-list-wrap">
			<view class="bill-detail-list-group" v-for="(item, index) in billList" :key="index">
				<view class="bill-detail-list-header">
					<view class="list-header-wrap">
						<view class="bill-list-header-label">
							{{ getFormatDate(item.billDate, 'YYYY年MM月') }}
						</view>
						<view class="bill-list-header-right">
							<view class="bill-list-header-value-icon">￥</view>
							<view class="bill-list-header-value">{{ item.billCostTotal || '0' }}</view>
						</view>
					</view>
				</view>
				<view
					class="bill-detail-list-item"
					v-for="billItem in item.groupList"
					:key="billItem.feeId"
					@click="billClick(billItem)"
				>
					<view class="bill-list-item-content">
						<view class="bill-list-item-left">
							<view class="bill-list-item-date">
								{{ getFormatDate(billItem.costProduceDate, 'YYYY/MM/DD') }}
							</view>
							<view class="bill-list-item-line">|</view>
							<view class="bill-list-item-desc">{{ billItem.feeTypeDes }}</view>
						</view>
						<view class="bill-list-item-right">
							<view class="bill-list-item-value-wrap">
								<view class="bill-list-item-value-icon">￥</view>
								<view class="bill-list-item-value">{{ billItem.costTotal }}</view>
							</view>
							<view>
								<UiButton type="transparent" size="sm" icon="icon-sysicon"></UiButton>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="popup-foot-btn" @click="cancelClick">确定</view>
	</view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import dayjs from 'dayjs'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import type { BillInfoGroup, BillInfo } from '../../paymentTypings'

defineOptions({
	name: 'BillListPopup',
})

const props = defineProps({
	// 绑定的用户
	billList: {
		type: Array as PropType<BillInfoGroup[]>,
		default: [],
	},
})
const emits = defineEmits(['cancel'])

// 格式化时间
const getFormatDate = (date: string, format: string) => {
	return dayjs(date).format(format)
}

const billClick = (billInfo: BillInfo) => {
	console.log('点击了账单')
}
const cancelClick = () => {
	emits('cancel')
}
</script>

<style lang="scss" scoped>
.bill-detail-popup-content {
	.bill-list-wrap {
		max-height: 744rpx;
		overflow: auto;
	}
	.bill-detail-list-header {
		height: var(--list-md-height);
		padding: 0 var(--content-default-padding-lg);
		display: flex;
		flex-direction: row;
		align-items: center;
		.list-header-wrap {
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			width: 100%;
			.bill-list-header-label {
				font-weight: 600;
				font-size: var(--font-body-md);
				color: var(--light-form-normal-default-text);
			}
			.bill-list-header-right {
				display: flex;
				flex-direction: row;
				align-items: flex-end;
				.bill-list-header-value-icon {
					font-weight: 600;
					font-size: var(--font-body-sm);
					color: var(--light-text-subcolor);
					margin-bottom: 4rpx;
				}
				.bill-list-header-value {
					font-size: var(--font-number-lg); // todo 变量不存在
					color: var(--light-text-subcolor);
				}
			}
		}
	}
	.bill-detail-list-item {
		padding-left: var(--content-default-padding-lg);
		padding-right: var(--content-default-padding-md);
		.bill-list-item-content {
			width: 100%;
			height: var(--form-md-height-md);
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.bill-list-item-left {
				display: flex;
				flex-direction: row;
				gap: var(--form-md-margin);
				.bill-list-item-date {
					font-size: var(--font-body-md);
					color: var(--light-form-normal-default-text);
				}
				.bill-list-item-line {
					color: var(--light-border-darker);
				}
				.bill-list-item-desc {
					font-size: var(--font-body-md);
					color: var(--light-form-normal-default-secondary);
				}
			}
			.bill-list-item-right {
				display: flex;
				flex-direction: row;
				align-items: center;
				.bill-list-item-value-wrap {
					display: flex;
					flex-direction: row;
					align-items: flex-end;
					.bill-list-item-value-icon {
						font-weight: 600;
						font-size: var(--font-body-sm);
						margin-bottom: 4rpx;
					}
					.bill-list-item-value {
						font-size: var(--font-number-md);
						color: var(--light-form-normal-default-text);
					}
				}
			}
		}
	}
	.popup-foot-btn {
		height: 160rpx;
		line-height: 160rpx;
		border-top: 1px solid var(--light-border-light);
		text-align: center;
	}
}
</style>
