import request from '@/core/request'

// =====================以下接口暂未用到=============================
// ---- 支付网关接口 ----

/**
 * 获取支付渠道列表
 * @param prepaymchorderno 预付订单号
 * @returns 支付渠道列表
 */
export function apiPaychannellist(prepaymchorderno: string): Promise<any> {
	return request({
		url: `/prepay/gateway/paychannellist/${prepaymchorderno}`,
		method: 'GET',
		data: {},
	})
}

/**
 * 获取支付详情
 * @param prepaymchorderno 预付订单号
 * @param code 渠道code
 * @returns 支付详情
 */
export function unifiedOrder(prepaymchorderno: string, code: string): Promise<any> {
	return request({
		url: `/prepay/gateway/unifiedOrder/${prepaymchorderno}/${code}`,
		method: 'GET',
		data: {},
	})
}

/**
 * 查询支付结果
 * @param appid APPID
 * @param mchOrderNo 订单号
 * @returns 支付结果
 */
export function apiQueryPayResult(appid: string, mchOrderNo: string): Promise<any> {
	return request({
		url: `/pay/gateway/queryPayResult/${appid}/${mchOrderNo}`,
		method: 'GET',
		timeout: 30000,
		data: {},
	})
}

/**
 * 查询支付完成订单
 * @param mchOrderNo 订单号
 * @returns 订单详情
 */
export function query(mchOrderNo: string): Promise<any> {
	return request({
		url: `/pay/gateway/query`,
		data: {
			mchOrderNo,
		},
	})
}
