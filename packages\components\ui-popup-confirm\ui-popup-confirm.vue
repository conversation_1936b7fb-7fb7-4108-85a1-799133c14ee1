<template>
	<UiPopupContainer ref="popupRef" position="center" :mask-show="maskShow" :z-index="zIndex" :is-mask-click="false">
		<view class="ui-popup-dialog" :style="{ width: width }">
			<view class="ui-dialog-title">
				<text class="ui-dialog-title-text">{{ _title }}</text>
			</view>
			<view class="ui-dialog-content">
				<slot>
					<text class="ui-dialog-content-text">{{ _message }}</text>
				</slot>
			</view>
			<view class="ui-dialog-button-group">
				<slot name="footer">
					<view v-if="_showCancalButton" class="botton-wrapper">
						<UiButton @click="handleCancelClick" :text="_cancelText" width="100%" size="xlg"></UiButton>
					</view>
					<view v-if="_showConfirmButton" class="botton-wrapper">
						<UiButton
							@click="handleConfirmClick"
							type="secondary"
							:text="_confirmText"
							size="xlg"
							width="100%"
						></UiButton>
					</view>
				</slot>
			</view>
		</view>
	</UiPopupContainer>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import UiPopupContainer from '../ui-popup-container/ui-popup-container.vue'
import UiButton from '../ui-button/ui-button.vue'
import { isEmpty } from '../../utils/validate'
import type { PopupConfirmProps } from './types'

defineOptions({
	name: 'UiPopupConfirm',
})

const props = withDefaults(defineProps<PopupConfirmProps>(), {
	modelValue: false,
	title: '提示',
	message: '',
	cancelText: '取消',
	confirmText: '确定',
	showCancalButton: true,
	showConfirmButton: true,
	beforeClose: null,
	width: '600rpx',
	maskShow: true,
})

const emits = defineEmits(['confirm', 'cancel', 'update:modelValue'])

const promise = {
	resolve: null as ((value?: unknown) => void) | null,
	reject: null as ((value?: unknown) => void) | null,
}
const popupRef = ref()
const currentValue = ref(props.modelValue)
const _title = ref(props.title)
const _message = ref(props.message)
const _cancelText = ref(props.cancelText)
const _confirmText = ref(props.confirmText)
const _showCancalButton = ref(props.showCancalButton)
const _showConfirmButton = ref(props.showConfirmButton)

watch(
	() => props.modelValue,
	(val: boolean) => {
		currentValue.value = val
		if (!val) {
			close('cancel')
			return
		}
		popupRef.value.open()
		_title.value = props.title
		_message.value = props.message
		_cancelText.value = props.cancelText
		_confirmText.value = props.confirmText
		_showCancalButton.value = props.showCancalButton
		_showConfirmButton.value = props.showConfirmButton
	},
)

const handleConfirmClick = () => {
	dealClose('confirm')
}

const handleCancelClick = () => {
	dealClose('cancel')
}

const dealClose = (event: string) => {
	const beforeClose = props.beforeClose
	if (!beforeClose) {
		close(event)
		settlePromise(event)
		return
	}
	Promise.resolve(beforeClose()).then(() => {
		close(event)
		settlePromise(event)
	})
}

const settlePromise = (event: string) => {
	if (event === 'confirm' && promise.resolve) {
		promise.resolve()
	} else if (event === 'cancel' && promise.reject) {
		promise.reject()
	}
}

const close = (event: string) => {
	currentValue.value = false
	popupRef.value.close()
	emits(event)
	emits('update:modelValue', false)
}

const show = (
	{
		title,
		message,
		cancelText,
		confirmText,
		showCancalButton,
		showConfirmButton,
	}: {
		title: string | undefined
		message: string | undefined
		cancelText: string | undefined
		confirmText: string | undefined
		showCancalButton: boolean | undefined
		showConfirmButton: boolean | undefined
	} = {} as any,
) => {
	currentValue.value = true
	popupRef.value.open()
	_title.value = isEmpty(title) ? props.title : title
	_message.value = isEmpty(message) ? props.message : message
	_cancelText.value = isEmpty(cancelText) ? props.cancelText : cancelText
	_confirmText.value = isEmpty(confirmText) ? props.confirmText : confirmText
	_showCancalButton.value = isEmpty(showCancalButton) ? props.showCancalButton : showCancalButton
	_showConfirmButton.value = isEmpty(showConfirmButton) ? props.showConfirmButton : showConfirmButton
	return new Promise((resolve, reject) => {
		promise.resolve = resolve
		promise.reject = reject
	})
}

defineExpose({
	show,
})
</script>

<style lang="scss">
@use './ui-popup-confirm.scss';
</style>
