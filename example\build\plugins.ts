import vue from '@vitejs/plugin-vue'
import type { PluginOption } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import legacy from '@vitejs/plugin-legacy'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'

export default () => {
	const vitePlugins: (PluginOption | PluginOption[])[] = [
		uni(),
		cssInjectedByJsPlugin(),
		// vue支持
		vue(),
		legacy({
			targets: ['ie >= 11'],
			additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
		}),
	]

	vitePlugins.push(
		AutoImport({
			imports: ['vue'],
			dts: 'typings/autoImport.d.ts',
			eslintrc: {
				enabled: true, // Default `false`
				filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
				globalsPropValue: 'readonly', // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
			},
		}),
	)

	vitePlugins.push(
		Components({
			dirs: ['uni_modules'],
			// ui库解析器
			extensions: ['vue'],
			// 配置文件生成位置
			dts: './typings/components.d.ts',
		}),
	)

	return vitePlugins
}
