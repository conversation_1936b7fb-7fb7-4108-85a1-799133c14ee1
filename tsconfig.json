{"extends": "@vue/tsconfig/tsconfig.json", "compilerOptions": {"target": "esnext", "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@e-cloud/eslink-plus-uniapp/*": ["../eslink-plus-uniapp/packages/*"]}, "lib": ["esnext", "dom"], "typeRoots": ["./node_modules/@types/", "./node_modules/vue/types"], "types": ["@dcloudio/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", ""], "exclude": ["node_modules"]}