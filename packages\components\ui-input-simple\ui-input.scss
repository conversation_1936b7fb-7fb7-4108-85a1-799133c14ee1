.ui-input {
	width: 100%;
	display: flex;
	flex-direction: row;


	.ui-input-wrapper {
		flex: 1;
		display: inline-flex;
		align-items: center;
		justify-content: center;
	}
	
	.ui-input-inner {
		flex: 1;
	}
	
	.ui-input-input {
		width: 100%;
		font-weight: 350;
		height: var(--form-md-height-md);
		color: var(--light-form-normal-default-text);
		font-size: var(--font-body-md);
		line-height: 22px;
	}
	
	.ui-input-input-placeholder {
		font-weight: 350;
		color: var(--light-form-placeholder);
		font-size: var(--font-body-md);
		line-height: 22px;
	}
	
	.ui-input-prepend,
	.ui-input-prefix {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: var(--form-md-height-md);
		line-height: 22px;
		margin-right: var(--form-md-margin);
	}
	
	.ui-input-prepend,
	.ui-input-append {
		color: var(--light-form-normal-default-text);
	}
	
	.ui-input-suffix,
	.ui-input-append {
		display: flex;
		flex-direction: row;
		align-items: center;
		height: var(--form-md-height-md);
		line-height: 22px;
	}
	
	.ui-input-suffix-icon,
	.ui-input-password,
	.ui-input-prefix-icon,
	.ui-input-clear {
		display: inline-flex;
		align-items: center;
		justify-content: center;
		width: var(--button-sm-height);
		height: var(--button-sm-height);
		margin-left: var(--form-md-margin);
		color: var(--light-button-default-transparent-icon);
	}
	
	.ui-input-prefix-icon {
		margin: 0;
	}


	&.ui-input-disabled {
		.ui-input-input,
		.ui-input-input-placeholder {
			color: var(--light-form-normal-disable-text);
		}
	}
	&.ui-input-md {
		.ui-input-input {
			height: var(--form-md-height-md);
		}
		.ui-input-input,
		.ui-input-input-placeholder {
			font-size: var(--font-body-md);
			line-height: 22px;
		}

		.ui-input-prepend,
		.ui-input-prefix,
		.ui-input-suffix,
		.ui-input-append {
			height: var(--form-md-height-md);
			line-height: 22px;
			margin-right: var(--form-md-margin);
		}

		.ui-input-suffix-icon,
		.ui-input-password,
		.ui-input-prefix-icon,
		.ui-input-clear {
			font-size: var(--icon-md);
			width: var(--button-sm-height);
			height: var(--button-sm-height);
			margin-left: var(--form-md-margin);
		}
	}

	&.ui-input-lg {
		.ui-input-input {
			height: var(--form-lg-height-large);
		}
		.ui-input-input,
		.ui-input-input-placeholder {
			font-size: var(--font-body-lg);
			line-height: 24px;
		}

		.ui-input-prepend,
		.ui-input-prefix,
		.ui-input-suffix,
		.ui-input-append {
			height: var(--form-lg-height-large);
			line-height: 24px;
			margin-right: var(--form-lg-margin);
		}

		.ui-input-suffix-icon,
		.ui-input-password,
		.ui-input-prefix-icon,
		.ui-input-clear {
			font-size: var(--icon-lg);
			width: var(--button-md-height);
			height: var(--button-md-height);
			margin-left: var(--form-lg-margin);
		}
	}
}


