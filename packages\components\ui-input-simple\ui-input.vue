<template>
	<view class="ui-input" :class="classNames">
		<view v-if="$slots.prepend" class="ui-input-prepend">
			<slot name="prepend"></slot>
		</view>
		<view class="ui-input-wrapper">
			<view v-if="$slots.prefix || prefix" class="ui-input-prefix">
				<slot name="prefix">
					<ui-icon
						v-if="prefix"
						class="ui-input-prefix-icon"
						customClass="ui-input-prefix-icon"
						:name="prefix"
					></ui-icon>
				</slot>
			</view>
			<view class="ui-input-inner">
				<input
					class="ui-input-input"
					:placeholder-class="placeholderClass"
					:placeholder="placeholder"
					:disabled="disabled"
					:type="trueType"
					:password="isPassword"
					:maxlength="maxlength"
					:value="modelValue"
					@input="handleInput"
					@focus="handleFocus"
					@blur="handleBlur"
					@confirm="handleConfirm"
				/>
			</view>
			<view
				class="ui-input-suffix"
				v-if="$slots.suffix || suffix || isShowPasswordVisableButton || isShowClearButton"
			>
				<slot name="suffix">
					<ui-icon
						v-if="suffix"
						class="ui-input-suffix-icon"
						customClass="ui-input-suffix-icon"
						:name="suffix"
					></ui-icon>
				</slot>
				<ui-icon
					v-if="isShowPasswordVisableButton"
					class="ui-input-password"
					customClass="ui-input-password"
					:name="passwordVisableIcon"
					@click="handlePasswordVisableButtonClick"
				></ui-icon>
				<ui-icon
					v-if="isShowClearButton"
					class="ui-input-clear"
					customClass="ui-input-clear"
					name="input-delete"
					@click="handleClarButtonClick"
				></ui-icon>
			</view>
		</view>
		<view v-if="$slots.append" class="ui-input-append">
			<slot name="append"></slot>
		</view>
	</view>
</template>
<script lang="ts" setup>
import { computed, ref } from 'vue'
import { useMpOptions } from '../../hooks/useMpOptions'
import { SizeEnum } from '../../shared/enum'
import UiIcon from '../ui-icon/ui-icon.vue'
import { InputType } from './types'
import type { InputProps } from './types'

defineOptions({
	name: 'uiInput',
	...useMpOptions(),
})

const props = withDefaults(defineProps<InputProps>(), {
	size: SizeEnum.md,
	placeholder: '',
	showPassword: true,
	type: InputType.text,
	maxlength: 140,
	disabled: false,
	suffix: '',
	prefix: '',
	placeholderClass: '',
	clearable: false,
	customClass: '',
})

const emits = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'confirm'])
const emitValue = value => {
	emits('update:modelValue', value)
	emits('input', value)
}

const isShowPassword = ref(false)

const classNames = computed(() => {
	const { disabled, size, customClass } = props
	const classNames = []

	if (size) {
		classNames.push(`ui-input-${size}`)
	}

	if (disabled) {
		classNames.push(`ui-input-disabled`)
	}

	if (customClass) {
		classNames.push(customClass)
	}

	return classNames
})

const placeholderClass = computed(() => {
	const { placeholderClass } = props
	return placeholderClass ? placeholderClass : 'ui-input-input-placeholder'
})

const isShowClearButton = computed(() => {
	const { modelValue, clearable, disabled } = props
	return !!modelValue && clearable && !disabled
})

const isShowPasswordVisableButton = computed(() => {
	const { modelValue, showPassword, disabled, type } = props
	return !!modelValue && showPassword && !disabled && type === InputType.password
})

const isPassword = computed(() => {
	// #ifndef  H5 || APP-PLUS
	const { type } = props
	return type === InputType.password
	// #endif
	return undefined
})
const trueType = computed(() => {
	const { type } = props

	if (type === InputType.password) {
		return isShowPassword.value ? InputType.text : InputType.password
	}

	return type
})
const passwordVisableIcon = computed(() => {
	return isShowPassword.value ? 'eye-fill' : 'eye-close-fill'
})

const handleInput = e => {
	const value = e.detail.value
	emitValue(value)
}
const handleFocus = e => {
	emits('focus', e)
}
const handleBlur = e => {
	emits('blur', e)
}
const handleConfirm = e => {
	emits('confirm', e)
}

const handleClarButtonClick = () => {
	emitValue('')
}

const handlePasswordVisableButtonClick = () => {
	isShowPassword.value = !isShowPassword.value
}
</script>
<style lang="scss">
@import url('./ui-input.scss');
</style>
