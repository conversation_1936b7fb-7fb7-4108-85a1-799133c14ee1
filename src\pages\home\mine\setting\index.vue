<template>
	<Layout
		title="设置"
		left-text=""
		nav-bar-background="transparent"
		layout-background="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<UiCell title="隐私协议" :arrow="true" @cellClick="privacyClick"></UiCell>
			<view class="about-us-cell">
				<UiCell title="第三方信息共享清单" :arrow="true" @cellClick="threeClick"></UiCell>
			</view>

			<view class="log-out-wrap">
				<view class="delete-btn">
					<UiButton
						type="normal"
						size="lg"
						width="100%"
						style-name="light"
						text="注销账号"
						@click="handleDeleteClick"
					></UiButton>
				</view>
				<view>
					<UiButton
						type="normal"
						size="lg"
						width="100%"
						style-name="light"
						text="退出登录"
						@click="handleLogoutClick"
					></UiButton>
				</view>
			</view>
			<UiPopupConfirm ref="popupConfirmRef"></UiPopupConfirm>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Layout from '@/layout/index.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiPopupConfirm from '@e-cloud/eslink-plus-uniapp/components/ui-popup-confirm/ui-popup-confirm.vue'
import User from '@/core/user'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'MinePage',
})

const popupConfirmRef = ref()
// 隐私协议点击
const privacyClick = () => {
	uni.navigateTo({ url: '/pages/agreement/privacy/index' })
}

// 第三共享清单点击
const threeClick = () => {
	uni.navigateTo({
		url: '/pages/agreement/three/index',
	})
}

const handleDeleteClick = () => {
	popupConfirmRef.value
		.show({ message: '确认注销账户吗？' })
		.then(() => {
			deleteBtnClick()
		})
		.catch(() => {})
}

const handleLogoutClick = () => {
	popupConfirmRef.value
		.show({ message: '确认退出吗？' })
		.then(() => {
			logOutBtnClick()
		})
		.catch(() => {})
}

// 注销账号点击
const deleteBtnClick = () => {
	// uni.navigateTo({
	// 	url: '/pages/home/<USER>/setting',
	// })
	showMsg('该功能暂未开通')
}

// 退出登录点击
const logOutBtnClick = () => {
	User.logOut()
		.then(() => {
			User.requestReady = false
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/home/<USER>/index',
				})
			}, 2000)
		})
		.catch(() => {})
}
// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.page-content {
	.about-us-cell {
		margin-top: 24rpx;
	}
	.log-out-wrap {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 0;
		width: 100%;
		padding: var(--content-default-padding-md);
		.delete-btn {
			margin-bottom: var(--content-default-margin-md);
		}
	}
}
</style>
