<template>
	<view class="card-item-wrap">
		<view v-if="title" @click="moreClick">
			<UiTitle :title="title" :noPadding="false" size="lg">
				<template #extra>
					<i class="iconfont icon-sysicon"></i>
				</template>
			</UiTitle>
		</view>
		<view class="card-item-content-wrap" :style="{ padding: noPadding ? 0 : undefined }">
			<slot></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'

defineOptions({
	name: 'CardItem',
})

const props = defineProps({
	title: {
		type: String,
		default: '',
	},
	more: {
		type: Boolean,
		default: false,
	},
	noPadding: {
		type: Boolean,
		default: false,
	},
})

const emits = defineEmits(['moreClick'])

const moreClick = () => {
	emits('moreClick')
}

defineExpose({})
</script>

<style lang="scss" scoped>
.card-item-wrap {
	background: var(--light-fill-blank);
	padding: var(--content-default-padding-sm) 0;
	border-radius: var(--content-default-radius-md);
	margin-top: 24rpx;
	.card-item-content-wrap {
		padding: 0 var(--content-default-padding-md);
	}
}
</style>
