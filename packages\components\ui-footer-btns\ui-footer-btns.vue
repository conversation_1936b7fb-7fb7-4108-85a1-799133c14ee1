<template>
	<view class="ui-footer-btns-wrapper" :style="{ 'z-index': zIndex }" :class="{ fixed: fixed }">
		<slot name="btnTop"></slot>
		<view
			class="ui-footer-btns"
			:class="{
				fixed: fixed,
				'ui-footer-btns-safe': fixed || safeAreaInsetBottom,
			}"
		>
			<slot>
				<view class="btn-list">
					<view v-for="(item, index) in btnList" :key="index" class="btn-wrapper">
						<UiButton
							v-bind="item"
							width="100%"
							size="xlg"
							:loading="loading"
							@click="handleClick(item, index)"
							@getphonenumber="(...data) => handleGetPhoneNumber(index, ...data)"
							@getuserinfo="(...data) => handleGetUserInfo(index, ...data)"
							@error="(...data) => handleError(index, ...data)"
							@opensetting="(...data) => handleOpenSetting(index, ...data)"
							@launchapp="(...data) => handleLaunchApp(index, ...data)"
							@agreeprivacyauthorization="(...data) => handleAgreePrivacyAuthorization(index, ...data)"
							@chooseavatar="(...data) => handleChooseAvatar(index, ...data)"
						></UiButton>
					</view>
				</view>
			</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
import UiButton from '../ui-button/ui-button.vue'
import type { FooterBtnsProps, BtnItemProps } from './types'

defineOptions({
	name: 'UiBottomBtns',
})

withDefaults(defineProps<FooterBtnsProps>(), {
	fixed: true,
	zIndex: 99,
	safeAreaInsetBottom: true,
	loading: false,
})

const emits = defineEmits([
	'click',
	'chooseAvatar',
	'getPhoneNumber',
	'getUserInfo',
	'error',
	'openSetting',
	'launchApp',
	'agreePrivacyAuthorization',
])

const handleClick = (item: BtnItemProps, index: number) => {
	if (!item.click) {
		emits('click', index)
		return
	}
	item.click()
}

// 获取用户头像回调
const handleChooseAvatar = (index: number, data: any) => {
	emits('chooseAvatar', index, data)
}

// 获取用户手机号回调
const handleGetPhoneNumber = (index: number, data: any) => {
	emits('getPhoneNumber', index, data)
}

// 用户点击该按钮时，会返回获取到的用户信息
// 从返回参数的detail中获取到的值同uni.getUserInfo
const handleGetUserInfo = (index: number, data: any) => {
	emits('getUserInfo', index, data)
}

// 当使用开放能力时，发生错误的回调
const handleError = (index: number, data: any) => {
	emits('error', index, data)
}

// 在打开授权设置页并关闭后回调
const handleOpenSetting = (index: number, data: any) => {
	emits('openSetting', index, data)
}

// 从小程序打开 App 成功的回调
const handleLaunchApp = (index: number, data: any) => {
	emits('launchApp', index, data)
}

// 用户同意隐私协议事件回调
const handleAgreePrivacyAuthorization = (index: number, ...data: any) => {
	emits('agreePrivacyAuthorization', index, data)
}
</script>

<style scoped lang="scss">
@use './ui-footer-btns.scss';
</style>
