<script setup lang="ts">
import { onLaunch, onShow, onHide, onError } from '@dcloudio/uni-app'
import Debugger from '@/core/debugger'
import User from '@/core/user'
// import { CheckUpdate } from '@/core/check-update'
onLaunch(() => {
	Debugger.init()
	console.log('App Launch')
	// CheckUpdate.checkForUpdate()
	User.login()
		.then(res => {
			const resp = res as any
			if (resp && resp.accessToken) {
				// loginStatus.value = true
			}
		})
		.catch(err => {
			console.log(err)
		})

	// 错误信息捕获
	uni.onUnhandledRejection(function (res) {
		console.log('报错了：', res)
		// 可以在这里将错误发送到你的错误日志系统
	})
})
onShow(() => {
	console.log('App Show')
})
onHide(() => {
	console.log('App Hide')
})
onError(err => {
	console.log('App Error', err)
})
</script>
<style lang="scss">
body {
	overflow: hidden;
}
</style>
