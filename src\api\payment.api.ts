import request from '@/core/request'

// ---- 缴费接口 ----

/**
 * 缴费查询
 * @param data 查询信息 meterNo?: string, userNo?: string
 * @returns 用户信息
 */
export function usmartAndPbApi(data: any): Promise<any> {
	return request({
		url: '/utility/rechargePayment/usmartAndPb',
		data,
		errorMsg: '查询用户信息失败',
	})
}

/**
 * 查询表具列表
 * @param data 查询信息 userNo?: string
 * @returns 表具列表
 */
export function queryMeterList(data: any): Promise<any> {
	return request({
		url: '/utility/rechargePayment/queryMeterInfo',
		data,
		errorMsg: '查询表具信息失败',
	})
}

/**
 * 查询表具详情
 * @param data 查询信息 userNo?: string
 * @returns 表具列表
 */
export function queryMeterDetail(data: any): Promise<any> {
	return request({
		url: '/utility/rechargePayment/queryMeterInfo',
		data,
		errorMsg: '查询表具信息失败',
	})
}

/**
 * 下单接口 支付金额大于临界值
 * @param data payMoney: string, userNo: string, orgNo: string, bizTradeTypeCode: string, bizProcessMsgJson: string, payCallBackUrl: string, assignTradeChannel: string
 * bizProcessMsgJson(JSON字符串): mobile payMoney userNo userTypeNo accountBalance feeIdCount organizationNo feesIds userName userAddress
 * @returns 支付路径
 */
export function payUnifiedOrder(data: any): Promise<any> {
	return request({
		url: '/utility/unifiedOrder',
		data,
		errorMsg: '支付下单失败',
	})
}

// =====================以下接口暂未用到=============================

/**
 * 按Route获取配置信息
 * @param attrKey 配置参数
 * @returns 配置信息
 */
export function getConfigByRoutePayment(attrKey: string): Promise<any> {
	return request({
		url: '/config/getConfigByRoute',
		timeout: 30000,
		headers: {
			'Content-Type': 'application/json',
			oAuthType: 'AUTH_MOBILE',
		},
		data: {
			route: attrKey,
		},
	})
}

/**
 * 页面初始化
 * @param token 鉴权密钥
 * @returns 初始化结果
 */
export function pageInit(token: string): Promise<any> {
	return request({
		url: '/utility/personalCenter/pageInit',
		timeout: 30000,
		data: {
			token,
		},
	})
}

/**
 * 预下单 准备统一支付
 * @param data payMoney: string, userNo: string, orgNo: string, bizTradeTypeCode: string, bizProcessMsgJson: string, payCallBackUrl: string
 * bizProcessMsgJson(JSON字符串): mobile payMoney userNo userTypeNo accountBalance feeIdCount organizationNo feeIds feesTotal userName userAddress meterNo acctOrgId payAmt
 * @returns 支付路径
 */
export function preUnifiedOrder(data: any): Promise<any> {
	return request({
		url: '/utilityPay/preUnifiedOrder',
		timeout: 30000,
		data,
	})
}

/**
 * 下单接口 支付金额大于临界值
 * @param data payMoney: string, userNo: string, orgNo: string, bizTradeTypeCode: string, bizProcessMsgJson: string, payCallBackUrl: string, assignTradeChannel: string
 * bizProcessMsgJson(JSON字符串): mobile payMoney userNo userTypeNo accountBalance feeIdCount organizationNo feesIds userName userAddress
 * @returns 支付路径
 */
export function gdPreUnifiedOrder(data: any): Promise<any> {
	return request({
		url: '/utilityPay/gdPreUnifiedOrder',
		timeout: 30000,
		data,
	})
}

/**
 * 查询欠费(普表)
 * @param userNo 用户号
 * @returns 费用信息
 */
export function feeQuery(userNo: string): Promise<any> {
	return request({
		url: '/utilityPay/feeQuery',
		timeout: 30000,
		data: { userNo },
	})
}

/**
 * 登陆
 * @returns 登陆结果
 */
export function login(): Promise<any> {
	return request({
		url: '/cloudselfhelp/login',
		data: {},
	})
}

/**
 * 校验用户信息是否完善
 * @param meterNo 表号/用户号
 * @returns 校验结果
 */
export function checkUserInfoComplete(meterNo: string): Promise<any> {
	return request({
		url: '/utility/personalCenter/checkUserInfoComplete',
		data: {
			meterNo,
		},
		timeout: 30000,
	})
}

/**
 * 计算应缴金额
 * @param data 气量数据 measurement gas
 * @returns 金额数据
 */
export function gasToAmount(data: any) {
	return request({
		url: '/api/usmart/v1.0/iot/gasToAmount',
		data,
		sucessCode: '0000',
		sucessKey: 'echoCode',
	})
}

/**
 * 查询设备信息
 * @param userNo 用户号
 * @returns 设备信息
 */
export function meterInfoQuery(userNo: string): Promise<any> {
	return request({
		url: '/api/utility/v2.0/um/meterInfoQuery',
		timeout: 30000,
		data: {
			userNo,
		},
		sucessCode: '0000',
		sucessKey: 'echoCode',
	})
}

/**
 * 按手机号获取列表
 * @param userPhone 手机号
 * @param type 列表类型 工程编码列表:engineeringCode 合同编码列表:contractCode
 * @returns 列表
 */
export function getCodeListByPhone(userPhone: string, type: string): Promise<any> {
	return request({
		url: '/reserve/getCodeListByPhone',
		data: {
			userPhone,
			type,
		},
		timeout: 30000,
	})
}

/**
 * 获取当前渠道用户信息
 * @returns 当前渠道用户信息
 */
export function getCurrentChannelUserInfo(): Promise<any> {
	return request({
		url: '/reserve/getCurrentChannelUserInfo',
		data: {},
		timeout: 30000,
	})
}

/**
 * 创建工程预付订单
 * @param data payMoney: string, userNo: string, orgNo: string, bizTradeTypeCode: string, bizProcessMsgJson: string
 * bizProcessMsgJson(JSON字符串): payMoney userNo userTypeNo accountBalance feeIdCount organizationNo feesTotal feesIds
 * @returns 支付路径
 */
export function unifiedOrderJsPayForEngineering(data: any): Promise<any> {
	return request({
		url: '/utilityPay/unifiedOrderJsPayForEngineering',
		timeout: 30000,
		data,
	})
}

/**
 * 创建预付订单
 * @param data payMoney: string, userNo: string, orgNo: string, bizTradeTypeCode: string, bizProcessMsgJson: string
 * bizProcessMsgJson(JSON字符串): payMoney userNo userTypeNo accountBalance feeIdCount organizationNo feesTotal feesIds
 * @returns 支付路径
 */
export function unifiedOrderJsPay(data: any): Promise<any> {
	return request({
		url: '/utilityPay/unifiedOrderJsPay',
		timeout: 30000,
		data,
	})
}

/**
 * 费用类型 合同或工程
 */
type CostClassType = 'contract' | 'engineering'

/**
 * 获取费用明细
 * @param type 费用类型
 * @param code 标识
 * @returns 费用明细
 */
export function getEngineeringCostRecord(type: CostClassType, code: string, userPhone?: string): Promise<any> {
	let data = {}
	if (type === 'contract') {
		data = {
			contractCode: code,
		}
	} else {
		data = {
			engineeringCode: code,
		}
	}
	if (userPhone) {
		data = {
			...data,
			userPhone,
		}
	}
	return request({
		url: '/reserve/getEngineeringCostRecord',
		timeout: 30000,
		data,
	})
}

/**
 * 获取商品列表
 * @returns 商品列表
 */
export function getGoods(): Promise<any> {
	return request({
		url: '/reserve/cqrq/getGoods',
		timeout: 30000,
		data: {},
	})
}

/**
 * 查询用户信息(物联网表)
 * @param meterNo 表号/用户号
 * @returns 用户信息
 */
export function userInfoQuery(meterNo: string): Promise<any> {
	return request({
		url: '/utilityPay/userInfoQuery',
		timeout: 30000,
		data: {
			meterNo,
		},
	})
}

/**
 * 初始化微信SDK
 * @param url 当前路径
 * @returns 初始化结果
 */
export function utilsWxConfig(url: string): Promise<any> {
	return request({
		url: '/utility/utils/wxConfig',
		timeout: 30000,
		headers: {
			'Content-Type': 'application/json',
			oAuthType: 'AUTH_MOBILE',
		},
		data: {
			url,
		},
	})
}
