<template>
	<view class="ui-tabs" :class="chooseTypeClass(type)">
		<scroll-view
			scroll-x
			scroll-with-animation
			enable-flex
			:show-scrollbar="false"
			class="scroll-view"
			:scroll-into-view="viewId"
		>
			<view
				v-for="(item, index) in list"
				:key="item.value || index"
				class="ui-tab"
				:id="`ui-tab-${item.value || index}`"
				:class="[chooseTypeClass(type), `ui-tab-${size}`, chooseTextTypeClass(type, size), chooseTabClass(item, index), chooseMargin(type, size)]"
				@click="handleClick(item.value, index)"
			>
				<view class="ui-tab-inner">
					<slot name="default">
						<text class="ui-tab-text" :class="chooseTabTextClass(item)">
							<slot name="text">
								{{ typeof item === 'string' ? item : item.label }}
							</slot>
						</text>
						<view v-if="!!item.dot" class="ui-tab-dot" :class="[item.dot]"></view>
						<UiTag
							v-if="!!item.badge && isActive(item, index)"
							:size="SizeEnum.sm"
							:text="item.badge"
						></UiTag>
						<view
							v-if="type === TabType.default"
							class="ui-tabs-line"
							:class="[
								`ui-tabs-line-${size}`,
								chooseTabClass(item, index),
								isActive(item, index) ? 'show' : '',
							]"
						></view>
					</slot>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import UiTag from '../ui-tag/ui-tag.vue'
import type { TabsProps, TabProps } from './types'
import { TabType } from './types'
import { SizeEnum } from '../../shared/enum'

defineOptions({
	name: 'UiTabs',
})

const props = withDefaults(defineProps<TabsProps>(), {
	type: TabType.default,
	size: SizeEnum.lg,
	list: [],
	backgroundColor: '',
})

const emits = defineEmits(['click', 'update:modelValue'])

const viewId = ref('')
const activeTab = ref<string | number>(props.modelValue)

onMounted(() => {
	activeTab.value = props.modelValue
})

watch(
	() => props.modelValue,
	(newValue: string | number) => {
		activeTab.value = newValue
	},
)

const handleClick = (value: string | number, index: number) => {
	const finalValue = value || index
	const id = `ui-tab-${finalValue}`
	viewId.value = id
	activeTab.value = finalValue
	emits('update:modelValue', finalValue)
	emits('click', finalValue)
}

const chooseTypeClass = (type: TabType) => {
	switch (type) {
		case TabType.fillLight:
			return 'fill-light'
		case TabType.fillDark:
			return 'fill-dark'
		default:
			return type
	}
}

// 纯文字样式的padding比较特殊，要小一点
const chooseTextTypeClass = (type: TabType, size: SizeEnum) => {
	switch (type) {
		// 纯文本的两种类型padding特殊
		case TabType.default:
		case TabType.blank:
			return `ui-tab-text-size-${size}`
		default:
			return ''
	}
}

const chooseMargin = (type: TabType, size: SizeEnum) => {
	switch (type) {
		case TabType.fillLight:
		case TabType.fillDark:
			return `margin-left-${size}`
		default:
			return ''
			break;
	}
}

const isActive = (item: TabProps, index: number) => {
	// 当value不存在时，使用下标用于激活判断
	if (!item.value && item.value !== 0) {
		return activeTab.value === index
	}
	return item.value === activeTab.value
}

const chooseTabClass = (item: TabProps, index: number) => {
	return isActive(item, index) ? 'active' : ''
}

const chooseTabTextClass = (item: TabProps) => {
	if (!!item.badge) {
		return `mr`
	}
	return ''
}
</script>
<style scoped lang="scss">
@use './ui-tabs.scss';
@use '../../styles/scrollView.scss';
</style>
