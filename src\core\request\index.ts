import Loop from './loop'
import Task from './task'
import User from '../user'
import request from './request'

export default (params: any) => {
	return new Promise((resolve, reject) => {
		// if (User.requestReady) {
		// Loop.stopLoop()
		// resolve(request(params))
		// }
		const task = new Task(params)
		Loop.pushOne(task)
		console.log('添加清求到队列：', params.url)
		const timer = setInterval(() => {
			if (task.status === Task.STATUS_FINISH) {
				if (task.success) {
					resolve(task.res)
				} else {
					reject(task.res)
				}
				clearInterval(timer)
			}
		}, 100)
	})
}
