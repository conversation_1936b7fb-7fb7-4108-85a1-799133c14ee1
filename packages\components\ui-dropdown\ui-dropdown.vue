<template>
	<view class="ui-dropdown">
		<picker
			:range="options"
			range-key="label"
			:value="currentValue"
			@change="handlerChange"
			@cancel="handlerCancel"
		>
			<view class="ui-dropdown-value" :class="[`ui-dropdown-size-${size}`]">
				<view class="uni-dropdown-current">{{ currentLabel }}</view>
				<UiIcon name="arrow-down-s-fill"></UiIcon>
			</view>
		</picker>
	</view>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import { type DropdownProps } from './types'

const props = withDefaults(defineProps<DropdownProps>(), {
	options: [],
	modelValue: '',
	size: 'md',
})
const emit = defineEmits(['update:modelValue', 'cancel', 'change'])
const handlerCancel = () => {
	emit('cancel')
}
const handlerChange = e => {
	const item = props.options[e.detail.value]
	currentValue.value = e.detail.value
	currentLabel.value = item.label
	emit('update:modelValue', item.value)
	emit('change', item.value)
}
const currentValue = ref()
const currentLabel = ref()

props.options.forEach((item: any, index: number) => {
	if (item.value === props.modelValue) {
		currentValue.value = index
		currentLabel.value = item.label
	}
})
</script>
<style lang="scss" scoped>
@use './ui-dropdown.scss';
</style>
