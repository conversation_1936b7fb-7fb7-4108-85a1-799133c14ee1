<template>
	<view @click="handlerClick(dataItem)" class="service-hall-info-wrap" :class="{ 'wrap-page': type == 'page' }">
		<view class="service-hall-content-wrap">
			<div class="header-wrap">
				<view class="service-hall-icon"></view>
				<div class="name-label">{{ dataItem.name || '--' }}</div>
				<UiTag v-if="dataItem.distance" type="info" size="sm" :text="dataItem.distance + 'km'"></UiTag>
			</div>
			<view class="address-label">{{ dataItem.address }}</view>
		</view>
		<div>
			<UiButton type="transparent" size="md" icon="icon-sysicon"></UiButton>
		</div>
	</view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { ServiceHallInfo } from '../../homeTypings'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
defineOptions({
	name: 'AddressItem',
})

const handlerClick = (dataItem: ServiceHallInfo) => {
	const [longitude, latitude] = dataItem.coordinates.split(',')
	uni.openLocation({
		latitude: Number(latitude),
		longitude: Number(longitude),
		name: dataItem.description,
		address: dataItem.description,
		scale: 18,
	})
}

defineProps({
	dataItem: {
		type: Object as PropType<ServiceHallInfo>,
		default: {},
	},
	type: {
		type: String,
		default: 'default',
	},
})
</script>

<style lang="scss" scoped>
.wrap-page {
	margin-top: 24rpx;
	background: var(--light-fill-blank);
	border-radius: var(--content-default-radius-md);
	padding: var(--content-default-padding-sm) var(--content-default-padding-xs) var(--content-default-padding-sm)
		var(--content-default-padding-lg) !important;
}
.service-hall-info-wrap {
	padding: var(--content-default-padding-xs) var(--content-default-padding-xs) var(--content-default-padding-sm)
		var(--content-default-padding-lg);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-top: 16rpx;
	&:first-child {
		margin-top: 0;
	}
	.service-hall-content-wrap {
		.header-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: var(--content-default-margin-xs);
			height: 112rpx;
			.service-hall-icon {
				width: 20rpx;
				height: 20rpx;
				border-radius: 5px;
				border: 4rpx solid var(--light-primary-dark);
			}
			.name-label {
				font-size: var(--font-body-lg);
				color: var(--light-form-normal-default-label);
				font-weight: 500;
			}
		}
		.address-label {
			line-height: 48rpx;
			font-size: var(--font-body-md);
			color: var(--light-text-light);
			padding-left: 28rpx;
		}
	}
}
</style>
