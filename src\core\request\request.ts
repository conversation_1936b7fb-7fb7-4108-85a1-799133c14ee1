import Cache from '@/core/cache'
import Crypto from './crypto'
import { showMsg } from '@/core/util'
import { getLoginPageUrl } from '@/core/util'

let BASE_URL = '/selfHelpGateway/en'

// #ifdef MP
if (import.meta.env.DEV) {
	BASE_URL = import.meta.env.VITE_MP_API_URL_TEST + BASE_URL
} else {
	// BASE_URL = import.meta.env.VITE_MP_API_URL_PRO;
	BASE_URL = import.meta.env.VITE_MP_API_URL_TEST + BASE_URL
}
// #endif

console.log('调用的url：', BASE_URL)

const TIMEOUT = 10000 // 默认超时时间
// 默认配置
const defaultConfig: RequestConfig = {
	method: 'POST',
	baseURL: BASE_URL,
	timeout: TIMEOUT,
	url: '',
	headers: {
		'Content-Type': 'application/json',
	},
	successCode: '100000',
	successKey: 'responseCode',
	errorMsg: '请求失败',
}

// 请求拦截器
const requestInterceptors = (config: RequestConfig) => {
	// 在这里可以添加请求头、修改配置等
	// if (Crypto.isJSONEncryptUrl(config.url)) {
	console.log('请求拦截器处理开始:', config.data)
	config.data = { param: Crypto.encrypt(JSON.stringify(config.data), 1) }
	// } else if (Crypto.isStrEncryptUrl(config.url)) {
	// 	config.data = Crypto.encrypt(JSON.stringify(config.data))
	// }
	config.headers['X-SelfHelp-Token'] = Cache.getChannelToken() // 渠道token
	config.headers['X-SelfHelp-AccessToken'] = Cache.getToken() // 全局token
	config.headers['oauthtype'] = 'AUTH_MOBILE'
	console.log('请求拦截器处理完成:', config)
	return config
}

// 响应拦截器
const responseInterceptors = (response: any, processedConfig: RequestConfig) => {
	// 在这里可以统一处理响应数据
	let result = response
	if (response.result) {
		result = Crypto.decrypt(response.result)
	}
	if (typeof result === 'string' && result.length) {
		result = JSON.parse(result)
	} else {
		result = response
	}
	console.log('响应拦截器url:', processedConfig.url, 'response:', result)
	return result
}

/**
 * 请求配置
 * @param url 请求路径
 * @param method 请求方法 可选 默认 POST
 * @param baseURL 请求地址 可选 默认 env.VITE_H5_API_URL
 * @param data 请求数据 可选
 * @param headers 请求头 可选 默认 'Content-Type': 'application/json'
 * @param timeout 请求超时时间单位ms 可选 默认 10000
 * @param successCode 成功状态码 可选 默认 100000
 * @param successKey 成功状态码键名 可选 默认 responseCode
 */
type RequestConfig = {
	url: string
	method?: any
	baseURL?: string
	data?: any
	headers?: any
	timeout?: number
	successCode?: string
	successKey?: string
	errorMsg: string
}

// 封装请求方法
const request = (config: RequestConfig) => {
	// 合并默认配置和用户传入的配置
	const finalConfig = { ...defaultConfig, ...config }
	// 请求拦截
	const processedConfig = requestInterceptors(finalConfig)

	return new Promise((resolve, reject) => {
		uni.request({
			url: `${processedConfig.baseURL}${processedConfig.url}`,
			method: processedConfig.method.toUpperCase(),
			data: processedConfig.data,
			header: processedConfig.headers,
			timeout: processedConfig.timeout,
			success: res => {
				if (res.statusCode === 200) {
					// 响应拦截
					const processedResponse = responseInterceptors(res.data, processedConfig)
					if (processedResponse.responseCode == '100000') {
						if (processedResponse) {
							resolve(processedResponse.result)
						} else {
							showMsg('未知异常，请稍后再试!')
						}
					} else if (processedResponse.responseCode == '100007') {
						const pages = getCurrentPages()
						let backUrl = ''
						if (pages.length > 0) {
							backUrl = '/' + pages[0].route
						}
						// 未登录，弹出登录页面
						uni.navigateTo({
							url: getLoginPageUrl(backUrl),
						})
					} else {
						showMsg(processedConfig.errorMsg, processedResponse)
						reject(processedResponse)
					}
				} else {
					showMsg('系统异常，请稍后再试!')
					reject(res.data)
				}
			},
			fail: error => {
				showMsg('系统繁忙，请稍后再试!')
				reject({
					responseCode: '900000',
					message: '系统繁忙，请稍后再试!',
				})
			},
		})
	})
}

// 导出请求方法
export default request
