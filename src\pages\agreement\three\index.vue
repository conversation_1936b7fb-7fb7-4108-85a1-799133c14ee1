<template>
	<Layout title="第三方信息共享清单" left-text="" @left-click="navigateBack">
		<view class="layout-agreement">
			<h1>第三方信息共享清单</h1>
			<p>
				当你使用第三方提供的服务时，我们会在获得或确保第三方获得你的授权同意后，以及其他符合法律法规规定的情形下共享对应信息。你可以通过本清单中列举的相关信息了解第三方会如何处理你的个人信息。我们也会对第三方获取个人信息的行为进行严格约束，以保护你的个人信息安全。
			</p>
			<p>
				为保障企业微信稳定运行或实现相关功能，我们还可能会接入由第三方提供的软件开发包（SDK）实现前述目的。我们对接入的相关第三方SDK同时在以下清单中列明。你可以通过目录中提供的链接或者路径查看第三方的数据使用和保护规则。请注意，第三方SDK可能由于版本升级、策略调整等原因导致其个人信息处理类型发生变化，请以其公示的官方说明为准。
			</p>
			<p><strong>微信开放平台</strong></p>
			<p><strong>第三方名称：深圳市腾讯计算系统有限公司</strong></p>
			<p>
				<strong>使用目的与场景：</strong>
				<span>微信头像、昵称、微信openID</span>
			</p>
			<p>
				<strong>涉及个人信息种类：</strong>
				<span>存储的个人文件、网络信息</span>
			</p>
			<p>
				<strong>共享方式：</strong>
				<span>SDK本机采集</span>
			</p>
			<p>
				<strong>第三方个人信息处理规则：</strong>
				<a href="https://weixin.qq.com/cgi-bin/readtemplate?lang=zh_CN&amp;t=weixin_agreement&amp;s=privacy">
					链接
				</a>
			</p>
			<br />
			<p><strong>高德地图</strong></p>
			<p><strong>第三方名称：高德软件有限公司</strong></p>
			<p>
				<strong>使用目的与场景：</strong>
				<span>当前定位</span>
			</p>
			<p>
				<strong>涉及个人信息种类：</strong>
				<span>
					位置信息（经纬度、精确位置、粗略位置）【通过IP地址、GNSS信息、WiFi状态、WiFi参数、WiFi列表、基站信息、信号强度的信息、蓝牙信息、传感器信息（矢量、加速度、压力）、设备信号强度信息获取】、设备标识信息（IMEI、IDFA、IDFV、AndroidID、MEID、MAC地址、OAID、IMSI、硬件序列号）、当前应用信息（应用名、应用版本号）、设备参数及系统信息（系统属性、设备型号、操作系统）
				</span>
			</p>
			<p>
				<strong>共享方式：</strong>
				<span>SDK本机采集</span>
			</p>
			<p>
				<strong>第三方个人信息处理规则：</strong>
				<a href="https://lbs.amap.com/pages/privacy/">链接</a>
			</p>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import Layout from '@/layout/index.vue'

defineOptions({
	name: 'AgreementThreePage',
})
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style scoped lang="scss">
.layout-agreement {
	padding: 20px;
	h1 {
		text-align: center;
		font-size: 2em;
		margin-block-start: 0.67em;
		margin-block-end: 0.67em;
	}
	h2 {
		font-size: 1.5em;
		margin-block-start: 0.83em;
		margin-block-end: 0.83em;
	}
	h3 {
		font-size: 1.17em;
		margin-block-start: 1em;
		margin-block-end: 1em;
	}
	p {
		margin-block-start: 1em;
		margin-block-end: 1em;
	}
	ol {
		list-style-type: decimal;
		margin-block-start: 1em;
		margin-block-end: 1em;
		padding-inline-start: 40px;
		padding: 0;
	}
	li {
		margin-left: 2em;
	}
	a {
		color: var(--light-text-link);
		text-decoration: none;
		cursor: pointer;
	}
}
</style>
