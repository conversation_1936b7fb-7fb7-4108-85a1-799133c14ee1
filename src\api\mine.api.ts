import request from '@/core/request'

// ---- 我的页面接口 ----

/**
 * 加密请求 path: /loginUser/getUserInfo
 * @returns 查询当前登录的用户信息
 */
export function getUserInfoEnApi(): Promise<any> {
	return request({
		url: '/loginUser/getUserInfo',
		method: 'GET',
		errorMsg: '获取当前用户信息失败',
	})
}

/**
 * 加密请求 path: /column/getList
 * @returns 查询服务记录列表
 */
export function getServiceRecordListEnApi(): Promise<any> {
	return request({
		url: '/column/getList',
		errorMsg: '获取服务记录列表失败',
	})
}
