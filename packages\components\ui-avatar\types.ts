import { SizeEnum } from '../../shared/enum'

export enum ImageFitEnum {
	scaleToFill = 'scaleToFill',
	aspectFit = 'aspectFit',
	aspectFill = 'aspectFill',
	widthFix = 'widthFix',
	heightFix = 'heightFix',
	top = 'top',
	bottom = 'bottom',
	center = 'center',
	left = 'left',
	right = 'right',
	'top left' = 'top left',
	'top right' = 'top right',
	'bottom left' = 'bottom left',
	'bottom right' = 'bottom right',
}

export interface AvatarProps {
	size?: SizeEnum
	icon?: string
	fit?: ImageFitEnum
	src?: string
	text?: string
	customClass?: string
}
