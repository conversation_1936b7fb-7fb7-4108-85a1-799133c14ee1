import type { MeterInfo } from '@/pages/commonTypes'
export interface IPrePayParams {
	payMoney: string
	orgNo: string
	userNo: string
	bizTradeTypeCode: string
	bizProcessMsgJson: string
	payCallBackUrl: string
}

// 账单信息接口返回数据
export interface BillInfoRes {
	userNo: string // 户号
	userName: string // 户名
	userAddress: string // 地址
	addressId: string // 地址id
	accountBalance: string // 账户余额
	accoutnExpenditure: string // 账户支出
	discountAmt: string // 优惠账户余额
	payableLateFee: string // 违约金
	payableTotalAmt: string // 应交金额(账单总额)
	orgNo: string // 机构码
	orgName: string // 机构名称
	feeRecordCount: string // 欠费笔数
	discountAmtExp: string // 优惠账户余额
	feeRecordList: BillInfoGroup[] // 欠费明细(账单明细)
}

// 账单信息分组
export interface BillInfoGroup {
	billDate: string // 汇总账期
	billCostTotal: string // 分组账单总额
	groupList: BillInfo[] // 欠费汇总集合
}

// 账单信息
export interface BillInfo {
	feeId: string // 费用id
	feeTypeNo: string // 费用类型
	feeTypeDes: string // 费用描述
	billDate: string //账期
	feePrice: string // 单价
	feeQuantity: string // 数量
	costTotal: string // 费用合计
	feeLate: string // 违约金
	feeTotal: string // 应缴金额
	amount1: string // 一阶金额
	amount2: string // 二阶金额
	amount3: string // 三阶金额
	amount4: string // 四阶金额
	amount5: string // 五阶金额
	billingQty1: string // 一阶气量
	billingQty2: string // 二阶气量
	billingQty3: string // 三阶气量
	billingQty4: string // 四阶气量
	billingQty5: string // 五阶气量
	price1: string // 一阶单价
	price2: string // 二阶单价
	price3: string // 三阶单价
	price4: string // 四阶单价
	price5: string // 五阶单价
	scbd: string // 上次表底
	bcbd: string // 本次表底
	costProduceDate: string // 费用产生时间
}

// 账单信息接口返回数据
export interface MeterListRes {
	userNo: string // 户号
	userName: string // 户名
	userAddress: string // 地址
	addressId: string // 地址id
	meterList: MeterInfo[] // 表具列表
	custMobile: string // 手机号
	meterMessageCount: string // 表具数量
	accountBalance: string // 账户余额
}
