const fs = require('fs')
const path = require('path')

const routesDir = path.join(__dirname, '../src')
const pagesJson = path.join(__dirname, '../src/pages.json')
const key = '.route.json'
function readRouteFiles(dir) {
	return fs.readdirSync(dir).reduce((acc, file) => {
		const fullPath = path.join(dir, file)
		if (fs.statSync(fullPath).isDirectory()) {
			acc.push(...readRouteFiles(fullPath))
		} else if (file.endsWith(key)) {
			acc.push(fullPath)
		}
		return acc
	}, [])
}

const routeFiles = readRouteFiles(routesDir)
const pages = []

routeFiles.forEach(filePath => {
	try {
		const routePath = path.relative(routesDir, filePath).replace(key, '').split(path.sep).join('/')
		const module = require(filePath)
		pages.push({ path: routePath, order: module.order || 0, ...module })
	} catch (err) {
		console.error(`Failed to load module: ${filePath}`, err)
	}
})
pages.sort((a, b) => b.order - a.order)
pages.map(item => {
	delete item.order
	return item
})
const route = {
	pages,
	globalStyle: {
		navigationStyle: 'custom',
		transparentTitle: 'always',
	},
}
fs.writeFileSync(pagesJson, JSON.stringify(route, null, 2))
