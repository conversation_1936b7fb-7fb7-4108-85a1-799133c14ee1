<template>
	<Layout title="缴费开票" layout-background="paymentLinearGradient">
		<view class="payment-result-view">
			<UiResult title="开票成功"></UiResult>
			<view style="width: 480rpx; margin-top: 48rpx">
				<UiButton @click="order" type="secondary" style="width: 100%">发票首页</UiButton>
			</view>
			<view style="width: 480rpx; margin-top: 32rpx">
				<UiButton @click="history" style="width: 100%">开票历史</UiButton>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import Layout from '@/layout/index.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiResult from '@e-cloud/eslink-plus-uniapp/components/ui-result/ui-result.vue'

defineOptions({
	name: 'InvoiceSuccess',
})

const order = () => {
	uni.navigateTo({
		url: '/pages/invoice/order/index',
	})
}
const history = () => {
	uni.navigateTo({
		url: '/pages/invoice/history/index',
	})
}
</script>
<style scoped lang="scss">
.payment-result-view {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx 48rpx;
}
</style>
