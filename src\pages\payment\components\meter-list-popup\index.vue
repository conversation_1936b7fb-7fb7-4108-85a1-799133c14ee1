<template>
	<view class="meter-list-popup-content">
		<view class="meter-list-group" v-for="group in meterGroupList" :key="group.title">
			<view class="meter-list-group-title">{{ group.title }}</view>
			<view class="meter-list-wrap" v-for="item in group.list" :key="item.meterId" @click="itemClick(item)">
				<view class="meter-list-item" :class="{ 'meter-selected': item.selected }">
					<view class="meter-list-item-left">
						<view class="meter-no">{{ item.meterNo || '--' }}</view>
						<view class="meter-left-line">|</view>
						<view class="meter-money">{{ '(' + (item.tableSurplusAmt || '0.00') + ')' }}</view>
						<UiTag v-if="item.meterTypeDes" type="success" size="sm" :text="item.meterTypeDes"></UiTag>
					</view>
					<i class="iconfont icon-meter-selecte select-icon" v-if="item.selected"></i>
				</view>
			</view>
		</view>
		<view class="popup-foot-btn" @click="selectMeterClick">确定</view>
	</view>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { PropType } from 'vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import type { MeterInfo } from '@/pages/commonTypes'
import type { MeterListRes } from '../../paymentTypings'

defineOptions({
	name: 'BillListPopup',
})

const props = defineProps({
	// 表具列表返回值
	meterListRes: {
		type: Object as PropType<MeterListRes>,
		default: {},
	},
	currentMeter: {
		type: Object as PropType<MeterInfo>,
		default: {},
	},
})

const meterGroupList = computed(() => {
	// 只有meterType == 13的表具才是物联网表
	let result = [] as any[]
	if (props.meterListRes && Array.isArray(props.meterListRes.meterList)) {
		const iotList = props.meterListRes.meterList.filter(item => item.meterType == '13')
		const index = props.meterListRes.meterList.findIndex(item => item && item.meterType != '13')
		if (index > -1) {
			// 如果有普表
			let meterInfo = ref({
				title: '账户预存',
				list: [
					{
						meterId: '-9999', // 普表
						meterNo: props.meterListRes.userNo,
						meterType: '11',
						tableSurplusAmt: props.meterListRes.accountBalance,
						purchaseCount: '',
						meterBalanceAmt: 0,
						isIotMeter: false,
						meterTypeDes: '户号',
						purchaseAmtTotal: '',
						selected: props.currentMeter.meterType != '13', // 自己加的
					},
				],
			})
			result.push(meterInfo.value)
		}
		if (iotList.length) {
			result.push({
				title: '表具充值',
				list: iotList,
			})
		}
	}
	return result
})

const itemClick = (item: MeterInfo) => {
	meterGroupList.value.forEach((group: any) => {
		group.list.forEach((item: MeterInfo) => {
			item.selected = false
		})
	})
	item.selected = true
}

const emits = defineEmits(['selectedMeter'])

// 选表具时，如果选的是物联网表需要查表具详情，然后以表具详情返回的字段为准
const selectMeterClick = () => {
	const currentMeterGroup = meterGroupList.value.find(group => group.list.find((item: MeterInfo) => item.selected))
	const currentMeter = currentMeterGroup.list.find((item: MeterInfo) => item.selected)
	if (currentMeter) {
		emits('selectedMeter', currentMeter)
	}
}
</script>

<style lang="scss" scoped>
.meter-list-popup-content {
	// max-height: 744rpx;
	.meter-list-group-title {
		height: var(--form-sm-height);
		font-weight: 600;
		font-size: var(--font-title-sm);
		color: var(--light-form-normal-default-text);
		padding: calc(var(--content-default-padding-md) / 2) var(--content-default-padding-lg);
	}
	.meter-list-wrap {
		gap: var(--content-default-margin-md);
		padding: calc(var(--content-default-padding-md) / 2) var(--content-default-padding-lg);
		.meter-list-item {
			height: var(--form-md-height-md);
			border-radius: var(--content-default-radius-sm);
			padding: 0 var(--content-default-padding-lg);
			border: 1px solid var(--light-border-dark);
			color: var(--light-list-normal-default-background);
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.meter-list-item-left {
				display: flex;
				flex-direction: row;
				align-items: center;
				height: var(--list-md-height);
				gap: var(--list-md-margin);

				.meter-no {
					font-size: var(--font-body-md);
					color: var(--light-list-normal-default-text);
				}
				.meter-left-line {
					color: var(--light-border-darker);
				}
				.meter-money {
					font-size: var(--font-body-md);
					color: var(--light-list-normal-default-secondary);
				}
			}
		}
		.meter-selected {
			border: 1px solid var(--light-primary-default);
		}
		.select-icon {
			color: var(--light-button-default-link-icon);
		}
	}
	.popup-foot-btn {
		height: 160rpx;
		line-height: 160rpx;
		border-top: 1px solid var(--light-border-light);
		text-align: center;
	}
}
</style>
