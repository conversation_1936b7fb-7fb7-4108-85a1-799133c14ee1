<template>
	<view class="service-record-item-wrap" @click="itemClick()">
		<view class="item-left">
			<text class="item-title">{{ dataItem.title }}</text>
			<text class="item-desc">{{ dataItem.desc }}</text>
		</view>
		<image class="menu-image" :src="'/static/images/mine/' + dataItem.icon + '.png'"></image>
	</view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { ServiceRecordItemConfig } from '../mineTypings'

defineOptions({
	name: 'ServiceRecordItem',
})

const props = defineProps({
	// 服务记录
	dataItem: {
		type: Object as PropType<ServiceRecordItemConfig>,
		default: () => ({}),
	},
})

const emits = defineEmits(['itemClick'])
// item 点击
const itemClick = () => {
	emits('itemClick', props.dataItem)
}
</script>

<style lang="scss" scoped>
.service-record-item-wrap {
	width: 100%;
	height: 152rpx;
	background-color: white;
	border: 1px solid var(--light-border-light);
	border-radius: var(--content-default-radius-md);
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 24rpx;
	.item-left {
		display: flex;
		flex-direction: column;
		.item-title {
			font-size: var(--font-body-md);
			color: var(--light-text-title);
			font-weight: 600;
			line-height: 36rpx;
		}
		.item-desc {
			font-size: var(--font-body-sm);
			color: var(--light-text-secondary);
			line-height: 32rpx;
			margin-top: 8rpx;
		}
	}
	.menu-image {
		width: 80rpx;
		height: 80rpx;
	}
}
</style>
