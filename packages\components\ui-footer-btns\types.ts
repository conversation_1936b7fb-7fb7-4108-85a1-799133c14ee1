import type { ButtonProps } from '../ui-button/types'

export interface BtnItemProps extends ButtonProps {
	click?: () => void
}

export interface FooterBtnsProps {
	/**
	 * @description 按钮列表
	 */
	btnList?: Array<BtnItemProps>
	/**
	 * @description 是否固定底部，为true时自动开启安全区域
	 */
	fixed?: boolean
	/**
	 * @description 是否开启底部安全区域
	 */
	safeAreaInsetBottom?: boolean
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
	/**
	 * @description 加载状态
	 */
	loading?: boolean
}
