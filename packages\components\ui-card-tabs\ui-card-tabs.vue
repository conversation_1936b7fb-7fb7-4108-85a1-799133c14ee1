<template>
	<view class="ui-card-tabs">
		<scroll-view
			scroll-x
			scroll-with-animation
			enable-flex
			:show-scrollbar="false"
			class="ui-card-tab-header-list"
			:scroll-into-view="viewId"
		>
			<view class="ui-card-tab-header-list-inner">
				<view
					v-for="(item, index) in options"
					:key="item.value || index"
					:id="`ui-card-tab-header-item-${item.value || index}`"
					class="ui-card-tab-header-item"
					:class="{ active: isActive(item, index) }"
					@click="handleClick(item.value, index)"
				>
					<view class="ui-card-tab-header-item-background"></view>
					<view class="ui-card-tab-header-item-active-background"></view>
					<view class="ui-card-tab-header-item-inner">
						{{ typeof item === 'string' ? item : item.label }}
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="ui-card-tabs-content">
			<view class="ui-card-tabs-linear"></view>
			<slot></slot>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { CardTabsProps, TabProps } from './types'

defineOptions({
	name: 'UiCardTabs',
})

const props = withDefaults(defineProps<CardTabsProps>(), {
	modelValue: '',
	options: [],
})

const emits = defineEmits(['change', 'update:modelValue'])

const viewId = ref('')
const activeTab = ref<string | number>(props.modelValue)
const handleClick = (value: string | number, index: number) => {
	const finalValue = value || index
	const id = `ui-card-tab-header-item-${finalValue}`
	viewId.value = id
	activeTab.value = finalValue
	emits('update:modelValue', finalValue)
	emits('change', finalValue)
}

const isActive = (item: TabProps, index: number) => {
	// 当value不存在时，使用下标用于激活判断
	if (!item.value && item.value !== 0) {
		return activeTab.value === index
	}
	return item.value === activeTab.value
}
</script>
<style scoped lang="scss">
@use './ui-card-tabs.scss';
@use '../../styles/scrollView.scss';
</style>
