.ui-tabbar-wrapper {
	box-sizing: border-box;
	position: relative;

	&.fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
	}
}

.ui-tabbar {
	box-sizing: border-box;
	position: relative;
	display: flex;
	flex-direction: row;
	align-items: center;
	min-height: 96rpx;
	background-color: var(--light-tab-bar-default-background-fill);

	&.fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100vw;
	}

	&.ui-tabbar-safe {
		min-height: calc(96rpx + env(safe-area-inset-bottom));
		padding-bottom: env(safe-area-inset-bottom);
	}
}

.ui-tabbar-item {
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	height: 100%;
}

.ui-tabbar-item-icon {
	box-sizing: border-box;
	position: relative;
	width: 40rpx;
	height: 40rpx;
	margin-top: 12rpx;
	margin-bottom: 4rpx;
}

.ui-tabbar-icon-svg,
.ui-tabbar-icon-iconfont {
	box-sizing: border-box;
	width: 40rpx;
	height: 40rpx;
	font-size: 40rpx;
}

.ui-tabbar-active {
	.ui-tabbar-icon-iconfont {
		color: var(--light-tab-bar-focus-icon);
	}
}

.ui-tabbar-icon-iconfont {
	color: var(--light-tab-bar-default-icon);
}

.ui-tabbar-item-label {
	box-sizing: border-box;
	font-size: var(--font-body-min);
	color: var(--light-tab-bar-default-text);
	line-height: 28rpx;
}

.ui-tabbar-item-label-select {
	font-weight: 600;
	box-sizing: border-box;
	color: var(--light-tab-bar-focus-text);
}

.ui-tabbar-badge {
	box-sizing: border-box;
	position: absolute;
	top: 0;
	left: 100%;
	transform: translate(-50%, -50%);
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	background-color: var(--light-error-light);
}
