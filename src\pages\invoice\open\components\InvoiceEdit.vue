<template>
	<view class="invoice-edit">
		<!-- 开票填报内容 -->
		<view class="edit-content">
			<view class="edit-title">开票填报</view>
			<view class="edit-form">
				<!-- 这里是开票填报表单内容 -->
				<view class="form-item">
					<view class="form-label">开票内容示例</view>
					<view class="form-value">这里是开票填报的表单内容</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<UiButton 
				type="normal" 
				size="lg" 
				text="上一步" 
				@click="prevStep"
			></UiButton>
			<UiButton 
				type="primary" 
				size="lg" 
				text="下一步" 
				@click="nextStep"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'

defineOptions({
	name: 'InvoiceEdit',
})

const emit = defineEmits(['prev-step', 'next-step'])

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 下一步
const nextStep = () => {
	emit('next-step')
}
</script>

<style scoped lang="scss">
.invoice-edit {
	padding: 24rpx 0;
	
	.edit-content {
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		
		.edit-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #171928;
			margin-bottom: 24rpx;
		}
		
		.edit-form {
			.form-item {
				margin-bottom: 24rpx;
				
				.form-label {
					font-size: 28rpx;
					color: #5F677D;
					margin-bottom: 12rpx;
				}
				
				.form-value {
					font-size: 30rpx;
					color: #171928;
				}
			}
		}
	}
	
	.footer-buttons {
		display: flex;
		justify-content: space-between;
		padding: 0 32rpx;
		margin-top: 48rpx;
		
		:deep(.ui-button) {
			width: 45%;
		}
	}
}
</style>