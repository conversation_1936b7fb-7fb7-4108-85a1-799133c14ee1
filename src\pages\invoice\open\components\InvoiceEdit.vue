<template>
	<view class="invoice-edit">
		<!-- 发票总额栏 -->
		<view class="invoice-amount">
			<UiCell title="发票总额" :value="`¥${totalAmount}`">
				<template #suffix>
					<view class="order-count">{{ selectedCount }}笔订单</view>
				</template>
			</UiCell>
		</view>

		<!-- 内容区域（可滚动） -->
		<scroll-view 
			class="content-scroll" 
			scroll-y="true"
			@scroll="onScroll"
			@scrolltolower="handleScrollToLower"
		>
			<!-- 发票类型选择 -->
			<UiCardTabs
				v-model="invoiceData.invoiceType"
				:options="invoiceTypeOptions"
				@change="handleInvoiceTypeChange"
			>
				<!-- 发票填写表单 -->
				<view class="invoice-form">
					<!-- 抬头类型 -->
					<view class="form-item title-type">
						<view class="form-row">
							<view class="item-label">
								抬头类型
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="radio-group">
									<view class="radio-item">
										<UiRadio
											:checked="invoiceData.titleType === 'company'"
											name="company"
											@change="() => updateTitleType('company')"
										></UiRadio>
										<text class="radio-label">企业单位</text>
									</view>
									<view class="radio-item">
										<UiRadio
											:checked="invoiceData.titleType === 'personal'"
											name="personal"
											@change="() => updateTitleType('personal')"
										></UiRadio>
										<text class="radio-label">个人/非企业</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 抬头名称 -->
					<view class="form-item">
						<view class="form-row">
							<view class="item-label">
								抬头名称
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="input-wrapper">
									<ui-input
										type="text"
										placeholder="必填"
										:bottomLine="false"
										:value="invoiceData.formData.companyName"
										@input="e => updateFormData('companyName', e.detail.value)"
									/>
									<ui-button class="select-btn" @click="selectCompany">选择</ui-button>
								</view>
							</view>
						</view>
					</view>

					<!-- 公司税号 -->
					<view class="form-item">
						<view class="form-row">
							<view class="item-label">
								公司税号
								<text class="required-mark">*</text>
							</view>
							<view class="form-content">
								<view class="input-wrapper">
									<ui-input
										type="text"
										placeholder="必填"
										:bottomLine="false"
										:value="invoiceData.formData.taxNumber"
										@input="e => updateFormData('taxNumber', e.detail.value)"
									/>
								</view>
							</view>
						</view>
						<view class="form-divider"></view>
					</view>

					<!-- 展开收起选填内容 -->
					<view class="expand-section">
						<view class="expand-content" v-if="isExpanded">
							<!-- 公司地址 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司地址</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												:value="invoiceData.formData.companyAddress"
												@input="e => updateFormData('companyAddress', e.detail.value)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 公司电话 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司电话</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												:value="invoiceData.formData.companyPhone"
												@input="e => updateFormData('companyPhone', e.detail.value)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 公司开户行 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">公司开户行</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												:value="invoiceData.formData.bankName"
												@input="e => updateFormData('bankName', e.detail.value)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 开户行账户 -->
							<view class="form-item">
								<view class="form-row">
									<view class="item-label">开户行账户</view>
									<view class="form-content">
										<view class="input-wrapper">
											<ui-input
												type="text"
												placeholder="请输入"
												:bottomLine="false"
												:value="invoiceData.formData.bankAccount"
												@input="e => updateFormData('bankAccount', e.detail.value)"
											/>
										</view>
									</view>
								</view>
								<view class="form-divider"></view>
							</view>

							<!-- 备注 -->
							<view class="remark-item">
								<view class="remark-label">备注</view>
								<view class="remark-content">
									<view class="input-wrapper">
										<ui-input
											type="textarea"
											placeholder="请输入"
											:bottomLine="false"
											:value="invoiceData.formData.remark"
											:maxlength="50"
											:rows="3"
											@input="e => updateFormData('remark', e.detail.value)"
										/>
									</view>
								</view>
							</view>
						</view>
						<view class="expand-header" @click="toggleExpand">
							<text class="expand-text">{{ isExpanded ? '收起信息（选填）' : '展开信息（选填）' }}</text>
							<view class="expand-icon">
								<image
									:src="
										isExpanded
											? '/static/images/invoice/arrow-up-s-fill.png'
											: '/static/images/invoice/arrow-down-s-fill.png'
									"
									class="arrow-icon"
								/>
							</view>
						</view>
					</view>
				</view>
			</UiCardTabs>
			<!-- 接收方式 -->
			<view class="receive-method">
				<UiTitle title="接收方式" size="sm"></UiTitle>
			</view>

			<!-- 电子邮箱 -->
			<view class="email-item">
				<view class="email-label">电子邮箱</view>
				<view class="email-content">
					<ui-input
						type="text"
						placeholder="请输入"
						:bottomLine="false"
						:value="invoiceData.formData.email"
						@input="e => updateFormData('email', e.detail.value)"
					/>
				</view>
			</view>

			<!-- 底部空白区域，为底部按钮留出空间 -->
			<view class="footer-placeholder"></view>
		</scroll-view>

		<!-- 底部按钮（固定在底部） -->
		<view class="footer-buttons">
			<UiButton type="normal" size="lg" text="上一步" @click="prevStep"></UiButton>
			<UiButton type="primary" size="lg" text="下一步" @click="nextStep"></UiButton>
		</view>
	</view>

	<!-- 公司选择器弹窗 -->
	<CustomPopup
		:show="showCompanySelector"
		type="center"
		:mask-click="false"
		@change="handlePopupChange"
		@close="closeCompanySelector"
	>
		<CompanySelector
			@select="handleCompanySelect"
			@add="handleAddCompany"
			@manage="handleManageCompanies"
			@close="closeCompanySelector"
		/>
	</CustomPopup>

	<!-- 公司表单弹窗 -->
	<CustomPopup
		:show="showCompanyForm"
		type="center"
		:mask-click="false"
		@change="handleFormPopupChange"
		@close="handleCancelCompany"
	>
		<CompanyForm
			:company="currentCompany"
			:isEdit="isEditCompany"
			@save="handleSaveCompany"
			@cancel="handleCancelCompany"
		/>
	</CustomPopup>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiCardTabs from '@e-cloud/eslink-plus-uniapp/components/ui-card-tabs/ui-card-tabs.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import CustomPopup from './CustomPopup.vue'
import CompanySelector from './CompanySelector.vue'
import CompanyForm from './CompanyForm.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'

defineOptions({
	name: 'InvoiceEdit',
})

// 接收从父组件传递的数据
const props = defineProps({
	invoiceData: {
		type: Object,
		required: true,
	},
})

const emit = defineEmits(['update:invoiceData', 'prev-step', 'next-step'])

// 计算选中的订单数量
const selectedCount = computed(() => props.invoiceData.selectedOrders?.length || 0)

// 计算总金额
const totalAmount = computed(() => props.invoiceData.totalAmount || '0.00')

// 发票类型选项
const invoiceTypeOptions = [
	{ value: 'normal', label: '普通发票' },
	{ value: 'special', label: '专用发票' },
]

// 处理发票类型变更
const handleInvoiceTypeChange = type => {
	updateInvoiceType(type)

	// 如果切换到专用发票，自动设置为企业抬头
	if (type === 'special' && props.invoiceData.titleType !== 'company') {
		updateTitleType('company')
	}
}

// 控制展开收起状态
const isExpanded = ref(false)
// 控制接收方式展开收起状态
const isReceiveExpanded = ref(false)

// 计算滚动视图高度
const scrollViewHeight = computed(() => {
	// 底部按钮高度 + 顶部发票总额栏高度 + 安全距离
	const bottomHeight = 120;
	const topHeight = 100;
	const safeDistance = 20;
	return `calc(100vh - ${bottomHeight + topHeight + safeDistance}px)`;
})

// 切换展开收起状态
const toggleExpand = () => {
	isExpanded.value = !isExpanded.value
}

// 处理滚动事件
const onScroll = (e) => {
	// 滚动事件处理，确保滚动正常工作
	console.log('滚动中', e)
}

// 处理滚动到底部
const handleScrollToLower = () => {
	// 可以在这里处理滚动到底部的逻辑
	console.log('滚动到底部')
}

// 切换接收方式展开收起状态
const toggleReceiveExpand = () => {
	isReceiveExpanded.value = !isReceiveExpanded.value
}

// 更新发票类型
const updateInvoiceType = type => {
	emit('update:invoiceData', {
		...props.invoiceData,
		invoiceType: type,
	})
}

// 更新发票抬头类型
const updateTitleType = type => {
	emit('update:invoiceData', {
		...props.invoiceData,
		titleType: type,
	})
}

// 更新表单数据
const updateFormData = (field, value) => {
	const newFormData = { ...props.invoiceData.formData }
	newFormData[field] = value

	emit('update:invoiceData', {
		...props.invoiceData,
		formData: newFormData,
	})
}

// 控制公司选择器显示
const showCompanySelector = ref(false)
// 控制公司表单显示
const showCompanyForm = ref(false)
// 当前编辑的公司
const currentCompany = ref({})
// 是否是编辑模式
const isEditCompany = ref(false)

// 选择公司
const selectCompany = () => {
	showCompanySelector.value = true
}

// 关闭公司选择器
const closeCompanySelector = () => {
	showCompanySelector.value = false
}

// 处理弹窗变化
const handlePopupChange = e => {
	if (!e.show) {
		setTimeout(() => {
			showCompanySelector.value = false
		}, 200)
	}
}

// 处理表单弹窗变化
const handleFormPopupChange = e => {
	if (!e.show) {
		setTimeout(() => {
			showCompanyForm.value = false
		}, 200)
	}
}

// 处理公司选择
const handleCompanySelect = company => {
	// 更新表单数据
	updateFormData('companyName', company.companyName)
	updateFormData('taxNumber', company.taxNumber)
	updateFormData('companyAddress', company.companyAddress)
	updateFormData('companyPhone', company.companyPhone)
	updateFormData('bankName', company.bankName)
	updateFormData('bankAccount', company.bankAccount)

	// 关闭选择器
	closeCompanySelector()
}

// 添加新公司
const handleAddCompany = () => {
	currentCompany.value = {}
	isEditCompany.value = false

	// 关闭选择器，打开表单
	showCompanySelector.value = false
	setTimeout(() => {
		showCompanyForm.value = true
	}, 200)
}

// 管理公司
const handleManageCompanies = () => {
	uni.showToast({
		title: '管理公司功能待实现',
		icon: 'none',
	})
}

// 保存公司
const handleSaveCompany = company => {
	// 这里应该调用API保存公司信息
	// 模拟保存成功
	handleCompanySelect(company)

	// 关闭表单
	showCompanyForm.value = false
}

// 取消添加/编辑公司
const handleCancelCompany = () => {
	// 关闭表单
	showCompanyForm.value = false

	// 如果是从选择器过来的，返回选择器
	if (showCompanySelector.value) {
		setTimeout(() => {
			showCompanySelector.value = true
		}, 200)
	}
}

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 下一步
const nextStep = () => {
	// 表单验证
	if (!props.invoiceData.formData.companyName) {
		uni.showToast({
			title: '抬头名称不能为空',
			icon: 'none',
		})
		return
	}

	if (props.invoiceData.titleType === 'company' && !props.invoiceData.formData.taxNumber) {
		uni.showToast({
			title: '公司税号不能为空',
			icon: 'none',
		})
		return
	}

	emit('next-step')
}
</script>

<style scoped lang="scss">
.invoice-edit {
	display: flex;
	flex-direction: column;
	height: 100vh;
	position: relative;
	overflow: hidden; /* 防止整体页面滚动 */

	.invoice-amount {
		background-color: #ffffff;
		border-radius: 20rpx;
		overflow: hidden;
		flex-shrink: 0;
		margin-bottom: 24rpx;

		:deep(.ui-cell) {
			padding: 32rpx;

			.ui-cell__title {
				font-size: 28rpx;
				color: #5f677d;
			}

			.ui-cell__value {
				font-size: 36rpx;
				color: #ff6b2c;
				font-weight: 500;
				font-family: TCloudNumber, sans-serif;
			}
		}

		.order-count {
			font-size: 24rpx;
			color: #5f677d;
			margin-top: 8rpx;
		}
	}

	.content-scroll {
		flex: 1;
		height: calc(100vh - 240rpx); /* 减去顶部和底部的高度 */
		overflow-y: auto;
		-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
	}

	.invoice-form {
		background-color: #ffffff;
		padding: 0 16rpx;
		border-radius: 20rpx;

		.required-mark {
			color: #ff0000;
			font-size: 28rpx;
			margin-left: 4rpx;
		}
		.remark-item {
			padding: 0 16rpx;
			display: flex;
			flex-direction: column;
			border-bottom: 2rpx solid #ebedf6;

			.remark-label {
				display: flex;
				align-items: center; // 垂直居中
				font-size: var(--font-body-md);
				min-height: 64rpx;
			}
		}
		.form-item {
			padding: 0 16rpx;
			min-height: 88rpx;
			display: flex;
			align-items: center;
			border-bottom: 2rpx solid #ebedf6;

			.form-row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.item-label {
					width: 150rpx;
					margin-right: 32rpx;
					font-size: 28rpx;
					color: #32364d;
				}

				.form-content {
					flex: 1;
				}
			}

			.required-tip {
				font-size: 24rpx;
				color: #ff4d4f;
				margin-top: 8rpx;
			}

			.input-wrapper {
				display: flex;
				align-items: center;
				position: relative;

				input,
				textarea {
					flex: 1;
					height: 80rpx;
					font-size: 28rpx;
					color: #333333;
					background-color: transparent;
					padding: 0;
					border: none;
				}

				textarea {
					height: 160rpx;
					padding: 0;
				}

				.select-btn {
					width: 108rpx;
					height: 64rpx;
					line-height: 60rpx;
					text-align: center;
					background-color: #ffffff;
					color: #333333;
					font-size: 28rpx;
					border-radius: 12rpx;
					border: 1px solid #cbd1e1;
				}
			}

			&.title-type {
				.radio-group {
					display: flex;

					.radio-item {
						display: flex;
						align-items: center;
						margin-right: 24rpx;

						&:last-child {
							margin-right: 0;
						}

						.radio-label {
							font-size: 28rpx;
							color: #333333;
							margin-left: 32rpx;
						}
					}
				}
			}
		}

		.expand-section {
			.expand-header {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 80rpx;
				background-color: transparent;

				.expand-text {
					color: #32364d;
					font-size: var(--font-body-sm);
				}

				.expand-icon {
					margin-left: 8rpx;
					display: flex;
					align-items: center;

					.arrow-icon {
						width: 32rpx;
						height: 32rpx;
					}
				}
			}
		}

	}
	
	.footer-placeholder {
		height: 160rpx; /* 为底部按钮留出空间 */
	}
	
	.receive-method {
		min-height: 64rpx;
		display: flex;
		align-items: center;
		padding: 0, 32rpx;
		margin-top: 20rpx;
		font-size: var(--font-body-md);
		color: #5F677D;
	}
	
	.email-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: var(--content-default-padding-lg);
		min-height: 152rpx;
		justify-content: space-between;
		background-color: #ffffff;
		border-radius: var(--content-default-radius-md);

		.email-label {
			width: 150rpx;
			margin-right: 32rpx;
			font-size: 28rpx;
			color: #32364d;
		}

		.email-content {
			flex: 1;
		}
	}

	.footer-buttons {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 24rpx 64rpx;
		background-color: #ffffff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 99;

		:deep(.ui-button) {
			width: 45%;
		}
	}
}
</style>
