<template>
	<view class="main" :class="theme">
		<image
			v-if="backgroundImageType"
			class="bg-img"
			:class="{ 'bg-img-h5': isH5 }"
			:src="getBackgroundImage()"
		></image>
		<UiLayout class="page-layout" :background="_layoutBackground" :safe-area-inset-bottom="safeAreaInsetBottom">
			<template #header>
				<UiNavBar
					v-if="showNavBar"
					:type="type"
					:left-text="leftText"
					:right-text="rightText"
					:left-icon="leftIcon"
					:right-icon="rightIcon"
					:title="title"
					:clickable="clickable"
					:background="navBarBackground"
					:titlePosition="titlePosition"
					@click-left="handleLeftClick"
					@click-right="handleRightClick"
				></UiNavBar>
			</template>
			<scroll-view
				v-if="scroll"
				class="page-content-wrap pad24"
				scroll-y="true"
				lower-threshold="10"
				:scroll-top="scrollTop"
				:show-scrollbar="false"
				refresher-background="transparent"
				@scrolltolower="handleScrollToLower"
				@refresherrefresh="handleRefresh"
				refresher-enabled
				:refresher-triggered="loading"
			>
				<slot></slot>
				<!-- <uni-load-more :status="status" /> -->
			</scroll-view>
			<scroll-view
				:scroll-top="scrollTop"
				:show-scrollbar="false"
				refresher-background="transparent"
				class="page-content-wrap pad24"
				scroll-y="true"
				v-else
			>
				<slot></slot>
			</scroll-view>
			<template #footer>
				<UiTabbar
					v-if="showTabbar"
					:current="currentIndex"
					:list="tabBarList"
					:before-change="beforeChange"
					@click="handleTabbarClick"
					@change="handleTabbarChange"
				></UiTabbar>
				<UiFooterBtns v-if="showFooterBtns" :fixed="false" :btn-list="footerBtnList"></UiFooterBtns>
			</template>
		</UiLayout>
	</view>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import UiLayout from '@e-cloud/eslink-plus-uniapp/components/ui-layout/ui-layout.vue'
import UiTabbar from '@e-cloud/eslink-plus-uniapp/components/ui-tabbar/ui-tabbar.vue'
import UiNavBar from '@e-cloud/eslink-plus-uniapp/components/ui-nav-bar/ui-nav-bar.vue'
import UiFooterBtns from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/ui-footer-btns.vue'
import imgDefault from '@/static/images/layout/background-default.png'
import imgMain from '@/static/images/layout/background-main.png'
import imgYuangong from '@/static/images/layout/background-yuangong.png'
import { tabBarList } from '@/config'
import { LayoutBodyProps, LayoutProps, NavBarProps, TabbarProps, FooterBtnsProps } from './props'
import '../style/index.scss'

defineOptions({
	name: 'PageLayout',
	styleIsolation: 'shared',
})

const props = defineProps({
	...LayoutBodyProps,
	...LayoutProps,
	...NavBarProps,
	...TabbarProps,
	...FooterBtnsProps,
})

const emits = defineEmits([
	'onReachBottom',
	'left-click',
	'right-click',
	'tabbar-click',
	'tabbar-change',
	'on-reach-bottom',
	'refresh',
])

// 主题变量
const theme = ref('light')
const status = ref('more')
const loading = ref(false)
const isH5 = ref(false)
// #ifdef H5
isH5.value = true
// #endif
const _layoutBackground = computed(() => {
	let backgroundMap = {
		default: 'var(--light-bg-base)',
		paymentLinearGradient: 'linear-gradient(180deg, #D3E1FF 0%, #F8F9FF 100%)',
		mineLinearGradient: 'linear-gradient(155.33deg, #C7DAFF 0%, #DFE9FF 25.56%, #EEF5FF 63.29%)',
		homeLinearGradient: 'linear-gradient(180deg, #9DBEFF 0%, rgba(225, 235, 255, 0) 58.97%)',
	}
	if (isH5.value) {
		// 公众号因为有微信的导航栏，所以渐变背景色要调整
		backgroundMap.homeLinearGradient = 'linear-gradient(180deg, #D0E0FF 0%, rgba(225, 235, 255, 0) 53.75%)'
	}
	return props.layoutBackground && !props.backgroundImageType ? backgroundMap[props.layoutBackground] : ''
})

const getBackgroundImage = () => {
	if (props.backgroundImageType == 'default') {
		return imgDefault
	} else if (props.backgroundImageType == 'main') {
		return imgMain
	} else if (props.backgroundImageType == 'yuangong') {
		return imgYuangong
	}
}

const endBottomLoading = (state: string) => {
	status.value = state
}

const handleLeftClick = () => {
	console.log('left click')
	emits('left-click')
}

const handleRightClick = () => {
	console.log('right click')
	emits('right-click')
}
const handleTabbarClick = (index: number) => {
	console.log('tabbar click', index)
	emits('tabbar-click', index)
}

const handleTabbarChange = (index: number) => {
	console.log('tabbar change', index)
	emits('tabbar-change', index)
}

const handleScrollToLower = () => {
	console.log('on-reach-bottom')
	emits('on-reach-bottom')
}

const handleRefresh = () => {
	loading.value = true
	emits('refresh')
	setTimeout(() => {
		loading.value = false
	}, 1000)
}

defineExpose({
	endBottomLoading,
})
</script>

<style lang="scss" scoped>
.main {
	background-color: var(--light-bg-base);
	position: relative;
}
.bg-img {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}
.bg-img-h5 {
	top: -88rpx;
}
.page-content-wrap {
	height: 100%;
	width: 100%;

	:deep(::-webkit-scrollbar) {
		display: none;
	}

	:deep(::-webkit-scrollbar-track) {
		display: none;
	}

	:deep(::-webkit-scrollbar-thumb) {
		display: none;
	}

	:deep(::-webkit-scrollbar-thumb:hover) {
		display: none;
	}
}
.pad24 {
	padding: 24rpx;
	box-sizing: border-box;
}
.pad24:has(.no-padding-wrap) {
	padding: 0 !important;
}
</style>
