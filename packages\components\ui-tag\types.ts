import { SizeEnum } from '../../shared/enum'

export enum TagType {
	default = 'default',
	primary = 'primary',
	warning = 'warning',
	success = 'success',
	error = 'error',
	accent = 'accent',
	doing = 'doing',
	info = 'info',
	otherflow = 'otherflow',
	important = 'important',
}

export interface TagProps {
	/**
	 * @description 标签类型
	 */
	type: keyof typeof TagType
	/**
	 * @description 标签尺寸
	 */
	size: Exclude<keyof typeof SizeEnum, 'xlg'>
	/**
	 * @description 标签文字
	 */
	text?: string | number
	/**
	 * @description 图标名称
	 */
	icon?: string
	/**
	 * @description 图标字体库
	 */
	iconfontClass?: string
	/**
	 * @description 图标颜色
	 */
	iconColor?: string
}
