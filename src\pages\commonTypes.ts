// 绑定的用户信息
export interface UserInfo {
	userName: string
	userNo: string
	userAddress: string
	addressId: string
	meterNo: string // 没返回
	userId: string // 没返回
	accountBalance: string
	balanceExp: string
	feesMoney: string
	feesTotal: string
	organizationNo: string
	userTypeDesc: string
	znjMoney: string
	userTypeNo: string
	meterList: MeterInfo[]
	userType: string
	accountExp: string
	defaultUser: number
	userRemark: string
	qrCode: string
	userInfoChangeType: string
	id: number
	isBind: boolean
	userMeterType: string // 用户表类型，PB：普表，IOT：物联网表
	selected: boolean // 自己添加的，标识是否选中
	industry: string // 青岛能源定制微厅用
	channelUserBindId: string // 青岛能源定制微厅用
	servicePointRuler: string // 青岛能源定制微厅用
	[key: string]: any
}

// 表具信息
export interface MeterInfo {
	meterId: string // 没返回
	meterNo: string
	meterType: string
	tableSurplusAmt: string
	purchaseCount: string
	meterBalanceAmt: number
	isIotMeter: boolean
	meterTypeDes: string
	purchaseAmtTotal: string
	selected: boolean // 自己加的
}
