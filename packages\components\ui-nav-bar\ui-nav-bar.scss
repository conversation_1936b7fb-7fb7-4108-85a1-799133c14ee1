.ui-nav-bar {
	box-sizing: border-box;
	position: relative;
}

.ui-nav-bar-container {
	box-sizing: border-box;
	padding: 0 24rpx;

	&.fixed {
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
	}

	&.light {
		background: var(--light-nav-bar-default-background-fill);
		.ui-nav-bar-title {
			color: var(--light-top-bar-default-text);
		}
		.ui-nav-bar-text {
			color: var(--light-button-default-transparent-text);
		}
		.ui-nav-bar-icon {
			color: var(--light-button-default-transparent-icon);
		}
	}

	&.dark {
		background: var(--dark-nav-bar-default-background-fill);
		.ui-nav-bar-title {
			color: var(--dark-top-bar-default-text);
		}
		.ui-nav-bar-text {
			color: var(--dark-button-default-transparent-text);
		}
		.ui-nav-bar-icon {
			color: var(--dark-button-default-transparent-icon);
		}
	}
}

.ui-nav-bar-content {
	box-sizing: border-box;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 100%;
}

.ui-nav-bar-title-center {
	padding: 0 var(--content-default-padding-md);
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	font-weight: 500;
	font-size: var(--font-body-xlg);
}

.ui-nav-bar-position-left {
	position: relative;
	flex: 1;
	text-align: left;
	padding: 0 var(--content-default-padding-md);
	font-weight: 500;
	font-size: var(--font-body-xlg);
}

.ui-nav-bar-left,
.ui-nav-bar-right {
	box-sizing: border-box;
	display: flex;
	align-items: center;
}

.ui-nav-bar-icon {
	font-size: 48rpx;
}

.ui-nav-bar-icon-left {
	margin-right: 8rpx;
}

.ui-nav-bar-icon-right {
	margin-left: 8rpx;
}

.ui-nav-bar-text {
	font-weight: 500;
	white-space: nowrap;
	font-size: var(--font-body-xlg);
}
