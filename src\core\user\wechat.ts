import wx from 'weixin-js-sdk' // 引入微信js-sdk
import { getChannelCode, wxConfig } from '@/api/common.api'

export default class {
	ready = false
	constructor() {
		this.wechatConfig()
	}

	// 当前是否是微信环境
	get isWeixin() {
		return navigator?.userAgent.toLowerCase().indexOf('micromessenger') !== -1
	}

	/**
	 * 初始化wechat
	 */
	wechatConfig() {
		if (!this.isWeixin) return false
		if (this.ready) return false
		const protocol: string = window.location.protocol
		const hostname: string = window.location.hostname
		const port: string = window.location.port
		getChannelCode({})
			.then((channelCode: string) => {
				if (channelCode == 'WECHAT') {
					const url = `${protocol}//${hostname}${port ? `:${port}` : ''}/`
					wxConfig({ url: window.location.href })
						.then((res: any) => {
							wx.config({
								debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
								appId: res.appId, // 必填，公众号的唯一标识
								timestamp: res.timestamp, // 必填，生成签名的时间戳
								nonceStr: res.nonceStr, // 必填，生成签名的随机串
								signature: res.signature, // 必填，签名
								jsApiList: [
									'chooseImage',
									'previewImage',
									'uploadImage',
									'downloadImage',
									'scanQRCode',
								], // 必填，需要使用的JS接口列表
								openTagList: ['wx-open-launch-weapp'],
							})
							wx.ready(() => {
								this.ready = true
								console.log('配置成功：' + JSON.stringify(res))
							})
							wx.error(err => {
								console.log('配置失败：' + err)
							})
						})
						.catch(err => {
							console.log('配置报错：' + err)
						})
				}
			})
			.catch(err => {
				console.log(err)
			})
	}
}
