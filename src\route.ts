import User from './core/user/index.js'
import { getLoginPageUrl } from '@/core/util'
import { showMsg } from '@/core/util'

const whiteUrl = ['/pages/home/<USER>/index', '/pages/home/<USER>/index', '/pages/agreement/index'] // 白名单，不需要登录就能看的页面
// tab切换监听
const addTabChangeEventListener = () => {
	uni.addInterceptor('reLaunch', {
		invoke(args) {
			// request 触发前拼接 url
			const url = args.url.split('?')[0]
			if (whiteUrl.indexOf(url) == -1) {
				if (User.loginStatus.value == 2) {
					console.log('未登录，跳转登录界面')
					args.url = getLoginPageUrl(args.url)
				}
			}
		},
	})
	addEventListener
}

const jumpYuanGong = () => {
	// #ifdef H5
	showMsg('请前往小程序打开此功能')
	// #endif
	// #ifndef H5
	uni.scanCode({
		scanType: ['qrCode'],
		onlyFromCamera: true,
		success: res => {
			uni.navigateTo({
				url: '/pages/home/<USER>/index?code=' + res.result,
			})
		},
	})
	// #endif
}

// 普通页面切换监听
const addPageChangeEventListener = () => {
	uni.addInterceptor('navigateTo', {
		invoke(args) {
			console.log(1)
			if (args.url.indexOf('camera://qrCode') > -1) {
				jumpYuanGong()
				return
			}
			const url = args.url.split('?')[0]
			// request 触发前拼接 url
			if (whiteUrl.indexOf(url) == -1) {
				if (User.loginStatus.value == 2) {
					console.log('未登录，跳转登录界面')
					uni.navigateTo({
						url: getLoginPageUrl(args.url),
					})
				}
			}
		},
	})
}

addTabChangeEventListener()
addPageChangeEventListener()
