import request from '@/core/request'

// ---- 公共接口 ----

/**
 * 获取渠道标识
 * @param url 页面地址
 * @returns 配置结果
 */
export function getChannelCode(data: any): Promise<any> {
	return request({
		url: '/channelCenter/getChannelCode',
		data,
		errorMsg: '获取渠道配置失败',
	})
}

/**
 * 微信wxConfig
 * @param url 页面地址
 * @returns 配置结果
 */
export function wxConfig(data: any): Promise<any> {
	return request({
		url: '/channelCenter/wxConfig',
		data,
		errorMsg: '获取微信配置失败',
	})
}

/**
 * 加密请求 path: /utility/en/config/tenantAndChannel
 * @returns 获取租户和渠道信息
 */
export function tenantAndChannelEnApi(): Promise<any> {
	return request({
		url: '/utility/config/tenantAndChannel',
		errorMsg: '获取配置信息失败',
	})
}

// =====================以下接口暂未用到=============================

/**
 * 根据页面获取主题配置
 * @param pageCode 页面code
 * @returns 主题配置信息
 */
export function getThemeConfig(pageCode: string): Promise<any> {
	return request({
		url: 'themeConfig/getThemeConfig',
		data: {
			pageCode,
		},
	})
}

/**
 * 获取栏目列表
 * @param showArea 显示区域
 * @param isShow 是否显示
 * @param needPage 是否需要分页
 * @returns 栏目列表
 */
export function columnGetList(showArea: number, isShow: number, needPage: boolean): Promise<any> {
	return request({
		url: 'column/getList',
		data: {
			showArea,
			isShow,
			needPage,
		},
	})
}

/**
 * 获取自定义字
 * @returns 自定义字
 */
export function getExtDataConfig(): Promise<any> {
	return request({
		url: '/reserveMenu/getExtDataConfig',
	})
}

/**
 * 获取菜单
 * @param data 请求数据 needPage: boolean, isShowInHomePage?: number, isShow?: number, groupId?: string, menuName?: string
 * @returns 菜单信息
 */
export function menuSelectList(data: any): Promise<any> {
	return request({
		url: '/menu/selectList',
		data,
	})
}

/**
 * 获取信息公告列表
 * @param columnId 栏目id
 * @returns 信息公告列表
 */
export function columnMenuRelGetList(columnId: any): Promise<any> {
	return request({
		url: '/columnMenuRel/getList',
		data: {
			columnId,
		},
	})
}

/**
 * 获取轮播图列表
 * @param needPage 是否需要分页
 * @param isShow 是否显示
 * @param columnId 栏目id
 * @returns 轮播图列表
 */
export function sowingMapGetList(needPage: boolean, isShow: number, columnId: string): Promise<any> {
	return request({
		url: '/sowingMap/getList',
		data: {
			needPage,
			isShow,
			columnId,
		},
	})
}

/**
 * 获取滚动通知列表
 * @param needPage 是否需要分页
 * @returns 通知列表
 */
export function getNoReadNoticeList(needPage: boolean): Promise<any> {
	return request({
		url: '/notice/getNoReadNoticeList',
		data: { needPage },
	})
}

/**
 * 获取底部导航栏
 * @returns 底部导航栏
 */
export function getFooterNavigationByOwnership(): Promise<any> {
	return request({
		url: '/footerNavigation/getFooterNavigationByOwnership',
	})
}
