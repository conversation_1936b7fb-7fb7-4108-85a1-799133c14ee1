<template>
	<div class="ui-sidebar">
		<div
			@click="handlerClick(item, index)"
			class="ui-sidebar-item"
			v-for="(item, index) in options"
			:key="item.value"
			:class="{
				active: active === item.value,
				'ui-sidebar-item-prev': index === currentIndex - 1,
				'ui-sidebar-item-next': index === currentIndex + 1,
			}"
		>
			<div v-if="active === item.value" class="ui-sidebar-item-tip"></div>
			{{ item.label }}
		</div>
	</div>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps, watch } from 'vue'
import { type SidebarProps, type SidebarItem } from './types'

const props = withDefaults(defineProps<SidebarProps>(), {
	options: [],
	modelValue: '',
})
watch(
	() => props.modelValue,
	value => {
		if (value !== active.value) active.value = value
	},
)
const emits = defineEmits(['change', 'update:modelValue'])
const handlerClick = (item: SidebarItem, index: number) => {
	active.value = item.value
	currentIndex.value = index
	emits('update:modelValue', item.value)
	emits('change', item.value)
}
const active = ref(props.modelValue)
const currentIndex = ref(-1)
if (props.modelValue && props.options.length) {
	currentIndex.value = props.options.findIndex(item => item.value === props.modelValue)
}
</script>
<style lang="scss" scoped>
@use './ui-sidebar.scss';
</style>
