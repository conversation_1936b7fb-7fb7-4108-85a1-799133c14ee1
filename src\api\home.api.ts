import request from '@/core/request'

/**
 * 获取首页菜单
 */
export function getMenuList(data: any): Promise<any> {
	return request({
		url: '/menu/getIndexMenu',
		data,
		errorMsg: '获取菜单失败',
	})
}

/**
 * 获取消息通知/活动推荐的列表
 */
export function getNoticeList(data: any): Promise<any> {
	return request({
		url: '/notice/getList',
		data,
		errorMsg: '获取消息通知/活动推荐列表失败',
	})
}

/**
 * 获取消息通知/活动推荐的详情
 */
export function getNoticeDetail(data: any): Promise<any> {
	return request({
		url: '/notice/getNoticeById',
		data,
		errorMsg: '获取消息通知/活动推荐详情失败',
	})
}

/**
 * 消息通知和活动推荐全部设为已读
 */
export function allRead(data: any): Promise<any> {
	return request({
		url: '/notice/allRead',
		data,
		errorMsg: '更新失败',
	})
}

/**
 * 获取首页附近营业厅列表
 */
export function getServiceHallList(data: any): Promise<any> {
	return request({
		url: '/businessHall/bussinessList',
		data,
		errorMsg: '获取菜单失败',
	})
}
