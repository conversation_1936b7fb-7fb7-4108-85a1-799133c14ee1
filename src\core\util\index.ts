// 获取登录页面的url
export const getLoginPageUrl = (url: string) => {
	return '/pages/home/<USER>/index?redirect_url=' + url
}

// 显示错误提示
export const showMsg = (defaultMessage: string, err?: any) => {
	let message = ''
	if (err) {
		if (err.message) {
			message = err.message
		} else if (err.echoDes) {
			message = err.echoDes
		} else {
			message = err
		}
	}
	uni.showToast({ title: message || defaultMessage, icon: 'none' })
}
