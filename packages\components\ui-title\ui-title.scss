.ui-title {
	width: 100%;
	overflow: hidden;
	padding: 0 var(--content-default-padding-lg);
}
.no-padding {
	padding: 0;
}

.ui-title-inner {
	width: 100%;
	overflow: hidden;
	height: var(--list-sm-height);
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.ui-title-left {
	width: 100%;
	overflow: hidden;
	display: flex;
	flex-direction: row;
	align-items: center;
	margin-right: var(--list-sm-margin);
	flex: 1;
}

.ui-title-prefix,
.ui-title-suffix {
	display: flex;
	flex-direction: row;
	align-items: center;
	font-size: var(--num-16);
	color: var(--light-text-secondary);
	margin-right: var(--list-sm-margin);
}

.ui-title-prefix-icon,
.ui-title-suffix-icon {
	font-size: var(--num-16);
}

.ui-title-title {
	font-size: var(--font-title-sm);
	font-weight: 700;
	margin-right: var(--list-sm-margin);
	color: var(--light-text-title);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.ui-title-sub-title {
	font-size: var(--font-body-sm);
	font-weight: 400;
	margin-right: var(--list-sm-margin);
	color: var(--light-text-secondary);
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.ui-title-sm {
	.ui-title-inner {
		height: var(--list-sm-height);
	}
	.ui-title-left,
	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-title,
	.ui-title-sub-title {
		margin-right: var(--list-sm-margin);
	}

	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-prefix-icon,
	.ui-title-suffix-icon {
		font-size: var(--num-16);
	}

	.ui-title-title {
		font-size: var(--font-title-sm);
	}

	.ui-title-sub-title {
		font-size: var(--font-body-sm);
	}
}

.ui-title-md {
	.ui-title-inner {
		height: var(--list-md-height);
	}
	.ui-title-left,
	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-title,
	.ui-title-sub-title {
		margin-right: var(--list-md-margin);
	}

	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-prefix-icon,
	.ui-title-suffix-icon {
		font-size: var(--num-16);
	}

	.ui-title-title {
		font-size: var(--font-title-md);
	}

	.ui-title-sub-title {
		font-size: var(--font-body-md);
	}
}

.ui-title-lg {
	.ui-title-inner {
		height: var(--list-lg-height);
	}
	.ui-title-left,
	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-title,
	.ui-title-sub-title {
		margin-right: var(--list-lg-margin);
	}

	.ui-title-prefix,
	.ui-title-suffix,
	.ui-title-prefix-icon,
	.ui-title-suffix-icon {
		font-size: var(--num-20);
	}

	.ui-title-title {
		font-size: var(--font-title-lg);
	}

	.ui-title-sub-title {
		font-size: var(--font-body-lg);
	}
}
