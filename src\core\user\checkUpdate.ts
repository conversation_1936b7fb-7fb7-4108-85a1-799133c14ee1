import CryptoJS from 'crypto-js'
import Cache from '../cache'

/**
 * 检查是否需要版本更新
 *  H5检查根页面的MD5指纹
 *  小程序调用uni的小程序版本检查
 */
export class CheckUpdate {
	private static showLog: boolean = true

	private static logCheck(msg: string) {
		if (this.showLog) {
			const now = new Date()
			const nowStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`
			console.log(nowStr, msg)
		}
	}

	/**
	 * 初始化 项目入口调用
	 */
	public static init(): void {
		this.logCheck('CheckUpdate init')
		// #ifdef H5
		this.needUpdateWeb()
		// #endif
	}

	/**
	 * 检查是否需要升级
	 * true 需要升级
	 */
	public static async checkForUpdate(): Promise<boolean> {
		let needUpdate: boolean | PromiseLike<boolean>
		// #ifdef H5
		needUpdate = this.needUpdateWeb()
		// #endif
		// #ifdef MP
		needUpdate = this.needUpdateMP()
		// #endif
		return needUpdate
	}

	// #ifdef H5
	private static async needUpdateWeb(): Promise<boolean> {
		const html = await fetch('/?_timestamp=' + Date.now()).then(resp => resp.text())
		const newMD5 = CryptoJS.MD5(html).toString()
		const webFingerprint = Cache.getWebFingerprint()
		this.logCheck('CheckUpdate check webFingerprint=' + webFingerprint + ', newMD5= ' + newMD5)
		if (!webFingerprint) {
			Cache.saveWebFingerprint(newMD5)
			this.logCheck('CheckUpdate checked result=no changed')
			return false
		}
		if (webFingerprint !== newMD5) {
			Cache.saveWebFingerprint(newMD5)
			this.logCheck('CheckUpdate checked result=needRefresh')
			return true
		}
		this.logCheck('CheckUpdate checked result=false')
		return false
	}
	// #endif

	// #ifdef MP
	private static async needUpdateMP(): Promise<boolean> {
		this.logCheck('CheckUpdate needUpdateMP')
		// todo remove reject or add reject
		return new Promise((resolve, reject) => {
			const updateManager = uni.getUpdateManager()
			updateManager.onCheckForUpdate(function (res) {
				CheckUpdate.logCheck('CheckUpdate needUpdateMP result=' + res.hasUpdate)
				resolve(res.hasUpdate)
			})
		})
	}
	// #endif
}
