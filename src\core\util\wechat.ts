import wx from 'weixin-js-sdk' // 引入微信js-sdk

export default class WechatSDK {
	// 当前是否是微信环境
	get isWeixin() {
		return navigator?.userAgent.toLowerCase().indexOf('micromessenger') !== -1
	}

	// 扫一扫
	static scanQRCode() {
		return new Promise<{ resultStr: string }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ resultStr: string }>(wx.scanQRCode, {
					needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
					scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}

	// 拍照或从手机相册中选图接口并上传微信服务器
	static chooseImage(config = {}) {
		return new Promise<{ localIds: string[] }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ localIds: string[] }>(wx.chooseImage, {
					count: 1, // 默认9
					sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
					...config,
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}
	//本地图片通过localId 上传 微信服务器
	static uploadImageToWx(localId: any) {
		return new Promise<{ serverId: string }>((resolve, reject) => {
			wx.ready(() => {
				this.toPromise<{ serverId: string }>(wx.uploadImage, {
					localId,
				})
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			})
		})
	}

	static toPromise<T>(fn: Function, config = {}) {
		return new Promise<T>((resolve, reject) => {
			fn({
				...config,
				success(res: any) {
					resolve(res)
				},
				fail(err: any) {
					reject(err)
				},
				complete(err: any) {
					reject(err)
				},
				cancel(err: any) {
					reject(err)
				},
			})
		})
	}
	static closeWindow() {
		try {
			wx.closeWindow()
		} catch (e) {
			/* empty */
		}
	}
}
