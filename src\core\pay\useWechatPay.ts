import { showMsg } from '@/core/util'
export default function useWechatPay() {
	let errorType = '0'
	let reCount = 0

	// 构建跳转URL
	const getJumpUrl = async (orderReqData: any, payResultPageUrl: string) => {
		// 获取支付成功跳转地址
		if (!payResultPageUrl) {
			return ''
		}

		const { transactionId, totalFee, accountNo } = orderReqData || {}
		const url = new URL(payResultPageUrl)
		url.searchParams.append('orderNo', transactionId)
		url.searchParams.append('money', String(Number(totalFee) / 100))
		url.searchParams.append('openId', accountNo)

		return url.toString()
	}

	// 跳转支付结果页面
	const navigateToPayResultPage = (orderReqData: any) => {
		const queryString = Object.entries({
			orderNo: orderReqData?.transactionId,
			money: orderReqData?.totalFee / 100,
			openId: orderReqData?.accountNo,
		})
			.map(([key, value]) => `${key}=${value}`)
			.join('&')
		uni.navigateTo({
			url: '/pages/payment/result/index?' + queryString,
		})
	}
	// 判断是否是 IOS
	const isIOS = () => {
		return !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
	}

	// 判断是否微信浏览器
	const isWeiXin = (flag: boolean) => {
		const ua = window.navigator.userAgent.toLowerCase()
		const sysInfo = ua.match(/openharmony ([\d\.]+)/)
		if (sysInfo && sysInfo.length > 1) {
			// 鸿蒙5以上系统不判断微信版本号，因为鸿蒙5以上微信是1.0.0版本，从第一个版本就支持微信支付
			const sysVersion = sysInfo[1].substring(0, 1)
			if (Number(sysVersion) * 1 >= 5) return true
		}

		const wechatInfo = ua.match(/MicroMessenger\/([\d\.]+)/i)
		if (/micromessenger/i.test(ua)) {
			if (!wechatInfo || !wechatInfo[1] || parseInt(wechatInfo[1].charAt(0)) < 5) {
				if (flag) {
					showMsg('微信支付：请升级您的客户端到5.0以上版本。')
				}
				return false
			}
			return true
		} else {
			if (flag) {
				showMsg('请使用微信内置浏览器进行支付。')
			}
			return false
		}
	}

	// 微信支付主函数
	const weixinPay = (data: string, orderReqData: any) => {
		console.log('开始支付')
		// #ifdef MP
		wxMiniPay(data, orderReqData)
		// #endif

		// H5、微信公众号直接调后端的登录接口
		// #ifdef H5 || WEB
		wxWebPay(data, orderReqData)
		// #endif
	}

	// 微信小程序支付
	const wxMiniPay = (data: any, orderReqData: any) => {
		let payData = JSON.parse(data)
		uni.requestPayment({
			provider: 'wxpay',
			timeStamp: payData.timeStamp,
			nonceStr: payData.nonceStr,
			package: payData.package,
			signType: payData.signType,
			paySign: payData.paySign,
			success: function (res) {
				console.log('success:' + JSON.stringify(res))
				// 跳转支付结果页面
				navigateToPayResultPage(orderReqData)
			},
			fail: function (err) {
				console.log('fail:' + JSON.stringify(err))
				showMsg(JSON.stringify(err))
			},
		} as UniNamespace.RequestPaymentOptions)
	}

	// 微信公众号支付
	const wxWebPay = (data: any, orderReqData: any) => {
		if (!isWeiXin(true)) {
			return
		}
		reCount = 0
		if (typeof window.WeixinJSBridge === 'undefined' || typeof window.WeixinJSBridge.invoke === 'undefined') {
			if (document.addEventListener) {
				if (isIOS()) {
					errorType = '2'
					document.addEventListener('WeixinJSBridgeReady', () => onBridgeReady(data, orderReqData))
				} else {
					errorType = '3'
					setTimeout(() => {
						document.addEventListener('WeixinJSBridgeReady', () => onBridgeReady(data, orderReqData))
					}, 1000)
				}
			} else if (document.attachEvent) {
				errorType = '4'
				document.attachEvent('WeixinJSBridgeReady', () => onBridgeReady(data, orderReqData))
				document.attachEvent('onWeixinJSBridgeReady', () => onBridgeReady(data, orderReqData))
			}
		} else {
			errorType = '1'
			onBridgeReady(data, orderReqData)
		}
	}

	// 执行微信支付
	const onBridgeReady = (payInfoJson: string, orderReqData: any) => {
		console.log('调起支付参数：', payInfoJson)
		if (window.WeixinJSBridge && window.WeixinJSBridge.invoke) {
			try {
				window.WeixinJSBridge.invoke('getBrandWCPayRequest', JSON.parse(payInfoJson), async (res: any) => {
					if (
						res.err_msg !== 'get_brand_wcpay_request:ok' &&
						res.err_msg !== 'get_brand_wcpay_request:cancel'
					) {
						showMsg(`支付异常，取消支付[${res && res.err_msg ? res.err_msg : '暂无'}]`)
					}
					if (res.err_msg === 'get_brand_wcpay_request:cancel') {
						return
					}

					// const jumpUrl = await getJumpUrl(orderReqData, payResultPageUrl)
					// if (jumpUrl) {
					// 	window.location.href = jumpUrl
					// 	return
					// }
					// 跳转支付结果页面
					navigateToPayResultPage(orderReqData)
				})
			} catch (error) {
				handleRetry(
					() => onBridgeReady(payInfoJson, orderReqData),
					'支付异常，取消支付[' + error + ']' + errorType,
				)
			}
		} else {
			handleRetry(() => onBridgeReady(payInfoJson, orderReqData), '支付异常:[' + errorType + ']')
		}
	}

	const handleRetry = (fn: Function, error: string) => {
		if (reCount < 3) {
			reCount++
			setTimeout(fn, 1000)
		} else {
			showMsg(error)
		}
	}

	return {
		weixinPay,
	}
}
