<template>
	<view class="pay-content-wrap">
		<view class="pay-input-wrap">
			<view class="pay-input-content">
				<view class="pay-input-title">缴费金额</view>
				<view class="input-view">
					<ui-input v-model="payMoney" class="pay-input" type="number" placeholder="输入金额" clearable>
						<template #labelRight>
							<view class="pay-input-icon">¥</view>
						</template>
						<template #deleteIconRight>
							<view class="pay-input-fill-btn">
								<UiButton
									v-if="Math.abs(Number(totalMoney)) > 0"
									type="lighter"
									size="md"
									style-name="light"
									text="自动填入"
									@click="inputFill(totalMoney)"
								></UiButton>
							</view>
						</template>
					</ui-input>
				</view>
			</view>
		</view>
		<view class="input-tag-wrap">
			<view class="input-tag-btn" v-for="tag in tagList" :key="tag">
				<UiButton
					type="lighter"
					size="lg"
					width="100%"
					style-name="light"
					:text="tag + '元'"
					@click="inputFill(tag)"
				></UiButton>
			</view>
		</view>
		<view class="pay-detail-list-wrap">
			<view class="pay-detail-cell-wrap" v-if="payType.startsWith('bill')">
				<view class="pay-detail-cell-line-wrap">
					<view class="pay-detail-cell-title">账单缴费</view>
					<view class="pay-detail-cell-content-wrap">{{ (billMoney || '0.00') + '元' }}</view>
				</view>
			</view>
			<view class="pay-detail-cell-wrap" v-if="payType.includes('iot') || payType.includes('account')">
				<view class="pay-detail-cell-line-wrap last-cell">
					<view class="pay-detail-cell-title">{{ accountPayDesc.label }}</view>
					<view class="pay-detail-cell-content-wrap">{{ (accountMoney || '0.00') + '元' }}</view>
				</view>
			</view>
			<view
				class="pay-meter-cell"
				v-if="payType.includes('iot') || payType.includes('account')"
				@click="meterChangeClick"
			>
				<view class="pay-meter-cell-content">
					<view class="pay-meter-cell-title">{{ accountPayDesc.label }}</view>
					<view class="pay-meter-cell-right">
						<view class="pay-meter-cell-meter-no">{{ accountPayDesc.value }}</view>
						<UiTag v-if="meter.meterTypeDes" type="default" size="sm" :text="accountPayDesc.tag"></UiTag>
						<view>
							<UiButton type="transparent" size="sm" icon="icon-sysicon"></UiButton>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, watch, defineExpose, computed } from 'vue'
import type { PropType } from 'vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import User from '@/core/user'
import { getPayType, getPayAmount } from '@/core/pay/payUtils'
import type { MeterInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'PaymentInput',
	options: {
		styleIsolation: 'shared',
	},
})

const props = defineProps({
	// 默认表具
	meter: {
		type: Object as PropType<MeterInfo>,
		default: {},
	},
	// 账单总金额
	totalMoney: {
		type: String,
		default: '',
	},
	// 户号
	userNo: {
		type: String,
		default: '',
	},
})

const payMoney = ref('')
const payType = ref('')
const billMoney = ref('')
const accountMoney = ref('')
const tagList = User.config.moneySwiftItem

const accountPayDesc = computed(() => {
	if (payType.value == 'bill') {
		return {
			label: '账单缴费',
			value: props.userNo || '--',
			tag: props.meter.meterTypeDes,
		}
	} else if (payType.value.includes('iot')) {
		return {
			label: '表具充值',
			value: props.meter.meterNo || '--',
			tag: props.meter.meterTypeDes,
		}
	} else {
		return {
			label: '账户预存',
			value: props.userNo || '--',
			tag: '户号',
		}
	}
})

const emits = defineEmits(['meterChange', 'scrollToBottom'])

// 自动填入按钮点击
const inputFill = (money: string) => {
	// 小数点后面最多显示2位，为整数时不显示小数点
	payMoney.value = Number(Number(money).toFixed(2)).toString()
}

// 表具选择点击
const meterChangeClick = () => {
	emits('meterChange')
}

defineExpose({
	payMoney,
})

watch(
	() => payMoney.value,
	newValue => {
		payType.value = getPayType(props.meter, props.totalMoney, Number(newValue))
		const res = getPayAmount(props.totalMoney, newValue)
		billMoney.value = res.billMoney
		accountMoney.value = res.accountMoney
		emits('scrollToBottom')
	},
)
watch(
	() => props.meter,
	() => {
		payType.value = getPayType(props.meter, props.totalMoney, Number(payMoney.value))
		const res = getPayAmount(props.totalMoney, payMoney.value)
		billMoney.value = res.billMoney
		accountMoney.value = res.accountMoney
	},
)
watch(
	() => props.totalMoney,
	() => {
		payType.value = getPayType(props.meter, props.totalMoney, Number(payMoney.value))
		const res = getPayAmount(props.totalMoney, payMoney.value)
		billMoney.value = res.billMoney
		accountMoney.value = res.accountMoney
	},
)
</script>

<style lang="scss" scoped>
.pay-content-wrap {
	border-radius: var(--content-default-radius-md);
	padding-top: var(--content-default-padding-xs);
	padding-bottom: var(--content-default-padding-sm);
	background-color: var(--light-fill-blank);
	margin-top: 24rpx;
	.pay-input-wrap {
		padding: 0 var(--content-default-padding-md);
		.pay-input-content {
			border-bottom: 1px solid var(--light-form-normal-default-underline);
			.pay-input-title {
				height: var(--form-lg-height-large);
				font-weight: 600;
				font-size: var(--font-body-md);
				line-height: var(--form-lg-height-large);
				color: var(--light-form-normal-default-label);
			}
			.input-view {
				gap: var(--form-lg-margin);
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				.pay-input-icon {
					font-size: var(--font-title-lg);
					color: var(--light-form-normal-default-text);
					margin-right: 24rpx;
				}
				.pay-input {
					flex: 1;
					font-weight: 600;
				}
				.pay-input-fill-btn {
					margin-left: 24rpx;
				}
				:deep(.ui-input-placeholder) {
					font-size: var(--font-number-xxlg);
					font-weight: 350;
				}

				:deep(.ui-input-input) {
					font-size: var(--font-number-xxlg);
				}

				:deep(.ui-input-inner) {
					height: 72px;
				}
			}
		}
	}
	.input-tag-wrap {
		padding: calc(var(--content-default-padding-sm)) var(--content-default-padding-md);
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		.input-tag-btn {
			width: calc((100% - var(--content-default-margin-md) * 4) / 3);
			margin-left: var(--content-default-margin-md);
			&:nth-child(n + 4) {
				margin-top: var(--content-default-margin-md);
			}
		}
	}
	.pay-detail-list-wrap {
		.pay-detail-cell-wrap {
			padding: 0 var(--content-default-padding-md);
			.pay-detail-cell-line-wrap {
				height: var(--form-md-height-md);
				border-bottom: 1px solid var(--light-form-normal-default-underline);
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				.pay-detail-cell-title {
					font-weight: 600;
					font-size: var(--font-body-md);
					color: var(--light-form-normal-default-label);
				}
				.pay-detail-cell-content-wrap {
					flex: 1;
					font-weight: 600;
					font-size: var(--font-body-md);
					text-align: right;
					color: var(--light-form-normal-default-text-light);
				}
			}
			.last-cell {
				border-bottom: none;
			}
		}
	}
	.pay-meter-cell {
		padding: var(--content-default-padding-xs) var(--content-default-padding-sm);
		.pay-meter-cell-content {
			height: var(--form-md-height-md);
			border-radius: var(--content-default-radius-sm);
			padding-right: var(--content-default-padding-xs);
			padding-left: var(--content-default-padding-md);
			background-color: var(--light-fill-lighter);
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			align-items: center;
			.pay-meter-cell-title {
				font-size: var(--font-body-md);
				color: var(--light-form-normal-default-label);
			}
			.pay-meter-cell-right {
				gap: var(--form-md-margin);
				display: flex;
				flex-direction: row;
				align-items: center;
			}
		}
	}
}
</style>
