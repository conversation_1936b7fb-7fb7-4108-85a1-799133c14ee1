<template>
	<view class="picker-view-wrapper" :style="{ height: height }">
		<picker-view
			:value="currentValue"
			:indicator-style="indicatorStyle"
			:indicator-class="indicatorClass"
			class="picker-view"
			@change="handleChange"
		>
			<picker-view-column>
				<view v-for="item in formatedOptions" :key="item.value" class="select-item">
					{{ item.label }}
				</view>
			</picker-view-column>
		</picker-view>
	</view>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import type { SelectionType, DataSelectProps } from './types'
import { isString, isNumber, isObject } from '../../utils/validate'

defineOptions({
	name: 'UiDataSelect',
})

const props = withDefaults(defineProps<DataSelectProps>(), {
	modelValue: '',
	type: 'single',
	options: [],
	valueKey: undefined,
})

const emits = defineEmits(['update:modelValue', 'change'])

const currentValue = ref([])

const formatedOptions = computed(() => {
	return props.options.map(item => {
		if (isObject(item)) {
			return item
		}
		return { label: item, value: item }
	})
})
onMounted(() => {
	// 如果传值为空，则默认选中第一个
	if (!props.modelValue && props.options.length > 0) {
		handleChange({ detail: { value: [0] } })
		currentValue.value = [0]
		return
	}
	currentValue.value = initValue(props.modelValue)
})

watch(
	() => [props.modelValue, props.options],
	() => {
		currentValue.value = initValue(props.modelValue)
		console.log('currentValue.value', currentValue.value)
	},
)

const handleChange = ({ detail }) => {
	const selectedIndex = detail.value[0]
	const data = props.options[selectedIndex]
	const newValue = !!props.valueKey ? data[props.valueKey] : data.value
	emits('change', newValue)
	emits('update:modelValue', newValue)
}

const initValue = (value: SelectionType) => {
	const options = props.options
	if (options.length < 1) {
		return []
	}
	if (isString(options[0]) || isNumber(options[0])) {
		return [options.findIndex(item => item === value)]
	}
	if (isObject(value)) {
		return [options.findIndex(item => item[props.valueKey] === value[props.valueKey])]
	}
	return [options.findIndex(item => item.value === value)]
}

// const isActive = (item: SelectionType, currentValue: SelectionType) => {
// 	if (isString(item) || isNumber(item)) {
// 		return item === currentValue
// 	}
// 	if (isObject(currentValue)) {
// 		return item[props.valueKey] === currentValue[props.valueKey]
// 	}
// 	return item.value === currentValue
// }
</script>
<style scoped lang="scss">
@use './ui-data-select.scss';
</style>
