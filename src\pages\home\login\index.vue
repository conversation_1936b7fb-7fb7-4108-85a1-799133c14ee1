<template>
	<Layout title="登录" backgroundImageType="main" left-text="" :left-icon="leftIcon" @left-click="navigateBack">
		<view class="page-content">
			<view class="content-background">
				<view class="mobile-input-wrap">
					<view class="mobile-input-title">
						<i class="iconfont icon-phone-icon phone-icon"></i>
						<view class="input-item-label">手机号：</view>
					</view>
					<UiInput
						v-model="mobile"
						type="number"
						size="lg"
						clearable
						background
						placeholder="请输入手机号"
					></UiInput>
				</view>
				<view class="mobile-input-wrap">
					<view class="mobile-input-title">
						<i class="iconfont icon-lock-icon phone-icon"></i>
						<view class="input-item-label">验证码：</view>
					</view>
					<UiInput v-model="code" type="number" size="lg" clearable background placeholder="请输入验证码">
						<template #deleteIconRight>
							<view class="code-button">
								<UiButton
									type="link"
									size="md"
									style-name="light"
									:text="codeBtnText"
									:loading="codeBtnLoading"
									:disabled="codeBtnDisabled"
									@click="handleSendCode"
								></UiButton>
							</view>
						</template>
					</UiInput>
				</view>
				<view class="remint-wrap">
					<UiTips type="info" content="若您的手机号未注册，将为您自动注册"></UiTips>
				</view>
				<view class="read-agree-select-wrap">
					<UiRadio :checked="selected" @change="readAgreeClick"></UiRadio>
					<view class="read-agree-label" @click="readAgreeClick">阅读并同意</view>
					<view class="read-agree-value" @click="handleAgreementClick">《用户隐私协议》</view>
				</view>
			</view>
			<UiFooterBtns :btnList="btnList"></UiFooterBtns>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import User from '@/core/user'
import regExp from '@/core/util/regExp.ts'
import UiTips from '@e-cloud/eslink-plus-uniapp/components/ui-tips/ui-tips.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiFooterBtns from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/ui-footer-btns.vue'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'MobileLogin',
})
const selected = ref(false)
const codeBtnText = ref('获取验证码')
const codeBtnDisabled = ref(false)
const count = 120 // 倒计时时间，默认120秒
let timeCount = count
const btnList = ref([
	{
		text: '登录',
		type: 'secondary',
		size: 'lg',
		click: () => {
			handleLogin()
		},
	},
])

const mobile = ref('')
const code = ref('')
const codeBtnLoading = ref(false)
let backUrl = ''

onLoad((options: any) => {
	if (options.redirect_url) {
		backUrl = options.redirect_url
	}
})

// 返回按钮是否显示
const leftIcon = computed(() => {
	const pages = getCurrentPages()
	if (pages.length > 1) {
		return undefined
	} else {
		return ''
	}
})

// 阅读并同意点击事件
const readAgreeClick = () => {
	selected.value = !selected.value
}

// 发送验证码点击事件
const handleSendCode = () => {
	if (!mobile.value) {
		showMsg('请输入手机号码')
		return
	}
	if (!regExp.PHONE.test(mobile.value)) {
		showMsg('请输入正确的手机格式')
		return
	}
	codeBtnLoading.value = true
	User.sendCode(mobile.value)
		.then(() => {
			showMsg('发送成功')
			codeBtnLoading.value = false
			// 第一次显示倒计时时间
			codeBtnDisabled.value = true
			codeBtnText.value = String(timeCount) + '秒后重新获取'
			// 开始倒计时
			setCodeBtnText()
		})
		.catch(() => {
			codeBtnLoading.value = false
		})
}

// 获取验证码倒计时
const setCodeBtnText = () => {
	setTimeout(() => {
		timeCount--
		if (timeCount < 1) {
			codeBtnText.value = '获取验证码'
			timeCount = count
			codeBtnDisabled.value = false
		} else {
			codeBtnText.value = String(timeCount) + '秒后重新获取'
			codeBtnDisabled.value = true
			setCodeBtnText() // 递归调用
		}
	}, 1000)
}

// 登录按钮点击事件
const handleLogin = () => {
	if (!mobile.value) {
		showMsg('请输入手机号码')
		return
	}
	if (!regExp.PHONE.test(mobile.value)) {
		showMsg('请输入正确的手机格式')
		return
	}
	if (!code.value || code.value.length != 6) {
		showMsg('请输入6位验证码')
		return
	}
	if (!selected.value) {
		showMsg('请勾选阅读并同意')
		return
	}
	User.setMobileLoginParams(mobile.value, code.value)
	User.auth
		.login()
		.then(() => {
			showMsg('登录成功')
			setTimeout(() => {
				User.requestReady = true
				if (backUrl) {
					uni.reLaunch({ url: backUrl })
				}
			}, 2000)
		})
		.catch(() => {})
}

// 蓝色协议点击查看
const handleAgreementClick = () => {
	uni.navigateTo({ url: '/pages/agreement/privacy/index' })
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	.content-background {
		gap: var(--content-default-margin-md);
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-padding-md) var(--content-default-padding-lg);
		background-color: var(--light-fill-blank);
		.mobile-input-wrap {
			margin-bottom: var(--content-default-margin-md);
			.mobile-input-title {
				height: var(--form-lg-height-large);
				gap: var(--form-lg-margin);
				display: flex;
				flex-direction: row;
				align-items: center;
				.phone-icon {
					font-size: 40rpx;
					color: var(--light-form-normal-default-icon-normal);
				}
				.input-item-label {
					font-weight: 600;
					font-size: var(--font-body-xlg);
					color: var(--light-form-normal-default-label);
				}
			}
		}
		.remint-wrap {
			margin-bottom: var(--content-default-margin-md);
		}
		.read-agree-select-wrap {
			padding: var(--content-default-padding-md) 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			flex-wrap: wrap;
			.read-agree-icon-wrap {
				display: flex;
				flex-direction: row;
				align-items: center;
				.select-image {
					width: 40rpx;
					height: 40rpx;
				}
			}
			.read-agree-label {
				font-size: var(--font-body-md);
				color: var(--light-text-primary);
				margin-left: var(--content-default-margin-sm);
			}

			.read-agree-value {
				font-size: var(--font-body-md);
				color: var(--light-text-link);
			}
		}
	}
}
</style>
