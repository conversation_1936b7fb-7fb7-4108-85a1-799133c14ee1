@mixin set-tab-size($size) {
	height: var(--tabs-#{$size}-height);
	padding: 0 var(--tabs-#{$size}-padding-lg);
	font-size: var(--font-body-#{$size});
	border-radius: var(--tabs-#{$size}-radius);

	& + & {
		margin-left: var(--tabs-#{$size}-margin);
	}

	.ui-tab-text.mr {
		margin-right: var(--tabs-#{$size}-margin);
	}
}

// 纯文字样式的padding比较特殊，要小一点
@mixin set-txt-tab-size($size) {
	padding: 0 var(--tabs-#{$size}-padding-sm);
}

@mixin set-tab-type($type) {
	color: var(--light-tabs-default-#{$type}-text);

	&:active {
		background: var(--light-tabs-default-#{$type}-background);
		color: var(--light-tabs-press-#{$type}-text);
	}

	&.active {
		background: var(--light-tabs-focus-#{$type}-background);
		color: var(--light-tabs-focus-#{$type}-text);
	}
}

@mixin set-tab-margin($size) {
	& + & {
		margin-left: var(--content-default-margin-#{$size});
	}
}

.ui-tabs {
	width: fit-content;
	max-width: 100%;
}

.scroll-view {
	width: 100%;
	white-space: nowrap;
}

.ui-tabs {
	box-sizing: border-box;
	padding: calc(var(--num-4) - 2rpx);

	&.round {
		border: 2rpx solid var(--light-border-dark, #dce1ee);
		border-radius: var(--radius-circle);
		background: var(--light-blue-01, #eef1fa);
	}
}

.ui-tab {
	box-sizing: border-box;
	display: inline-block;
	position: relative;
	font-weight: 400;
	white-space: nowrap;
	transition: all 0.3s ease-out;

	&.active {
		font-weight: 600;
	}
}

.ui-tab-lg {
	@include set-tab-size('lg');
		font-size: var(--font-body-xlg);
}

.ui-tab-md {
	@include set-tab-size('md');
}

.ui-tab-sm {
	@include set-tab-size('sm');
}

.ui-tab.fill-dark {
	@include set-tab-type('fill-dark');
}

// 纯文字样式的padding比较特殊，要小一点
.ui-tab-text-size-lg {
	@include set-txt-tab-size('lg');
}
.ui-tab-text-size-md {
	@include set-txt-tab-size('md');
}
.ui-tab-text-size-sm {
	@include set-txt-tab-size('sm');
}

.ui-tab.blank,
.ui-tab.default {
	@include set-tab-type('text');
}

.ui-tab.round {
	@include set-tab-type('page');

	&.active {
		border-radius: var(--radius-circle);
	}
}

.ui-tab.fill-light {
	@include set-tab-type('fill-light');
}

.ui-tab-inner {
	position: initial;
	display: flex;
	align-items: center;
	height: 100%;
}

.ui-tabs-line {
	position: absolute;
	height: 8rpx;
	width: 56rpx;
	background-color: var(--light-tabs-default-text-border);
	bottom: 0;
	left: 50%;
	transform: translate(-50%, 0);
	opacity: 0;
	transition: all 0.3s ease-out;

	&.show {
		opacity: 1;
	}
}

.ui-tabs-line-lg {
	border-radius: var(--button-lg-radius);
}

.ui-tabs-line-md {
	border-radius: var(--button-md-radius);
}

.ui-tabs-line-sm {
	border-radius: var(--button-sm-radius);
}

.ui-tab-dot {
	position: absolute;
	right: 10rpx;
	top: 10rpx;
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
	border: 2rpx solid var(--light-fill-lighter);

	&.primay {
		background: var(--light-primary-default);
	}

	&.error {
		background: var(--light-error-default);
	}

	&.success {
		background: var(--light-success-default);
	}
}

.margin-left-sm {
	@include set-tab-margin('sm');
}
.margin-left-md {
	@include set-tab-margin('md');
}
.margin-left-lg {
	@include set-tab-margin('lg');
}
