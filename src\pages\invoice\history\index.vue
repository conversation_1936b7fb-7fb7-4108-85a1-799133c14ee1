<template>
	<view class="main">
		<Layout title="开票历史" :show-footer-btns="true" :footer-btn-list="footerBtnList">
			<!-- 用户信息卡片 -->
			<UserInfoCard ref="userInfoCard" @getUserInfo="handleGetUserInfo"></UserInfoCard>

			<!-- 开票历史列表 -->
			<view class="invoice-history">
				<!-- 按月份分组显示 -->
				<view class="month-group" v-for="(group, index) in invoiceGroups" :key="index">
					<UiTitle :title="group.month || ''" size="sm"></UiTitle>
					<view class="invoice-list">
						<view class="invoice-item" v-for="(item, itemIndex) in group.items" :key="itemIndex">
							<view class="invoice-header">
								<view class="invoice-date">{{ item.date }}</view>
								<UiTag v-if="item.status" type="primary" size="min" :text="item.status"></UiTag>
							</view>
							<view class="invoice-content">
								<UiCell :title="item.type || ''" :value="item.amount || 0" size="sm" />
							</view>
						</view>
					</view>
				</view>
			</view>
		</Layout>
	</view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import type { UserInfo } from '@/pages/commonTypes'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'

defineOptions({
	name: 'InvoiceHistory',
})

// 用户信息
const userInfoCard = ref()
const currentUser = ref({} as UserInfo)

// 模拟发票数据
const invoiceGroups = ref([
	{
		month: '2025年4月',
		items: [
			{
				date: '2025/04/25',
				type: '气费',
				amount: '102.40',
				status: '开票中'
			},
			{
				date: '2025/04/25',
				type: '气费/垃圾回收费/维修费\n保养费',
				amount: '102.40'
			}
		]
	},
	{
		month: '2025年3月',
		items: [
			{
				date: '2025/03/25',
				type: '气费',
				amount: '102.40'
			},
			{
				date: '2025/03/25',
				type: '气费/垃圾回收费/维修费\n保养费',
				amount: '102.40'
			}
		]
	},
	{
		month: '2025年2月',
		items: [
			{
				date: '2025/02/25',
				type: '气费',
				amount: '102.40'
			},
			{
				date: '2025/02/25',
				type: '气费/垃圾回收费/维修费\n保养费',
				amount: '102.40'
			}
		]
	}
])

// 底部按钮配置
const footerBtnList = ref([
	{
		text: '去开票',
		type: 'secondary',
		onClick: () => {
			uni.navigateTo({
				url: '/pages/invoice/open/index'
			})
		}
	}
])

// 获取用户信息回调
const handleGetUserInfo = (userInfo: UserInfo) => {
	currentUser.value = userInfo
}

onMounted(() => {
	// 这里可以添加获取发票历史数据的逻辑
})
</script>

<style lang="scss" scoped>
.main {
	background-color: var(--light-bg-base);
	min-height: 100vh;
}

.invoice-history {
	margin-top: 24rpx;
	
	.month-group {
		margin-bottom: 24rpx;
		
		.month-title {
			font-size: 30rpx;
			color: #5F677D;
			padding: 10rpx 0;
			margin-bottom: 8rpx;
		}
		
		.invoice-list {
			.invoice-item {
				background-color: #FFFFFF;
				border-radius: 20rpx;
				padding: 24rpx 32rpx;
				margin-bottom: 12rpx;
				
				.invoice-header {
					display: flex;
					justify-content: space-between;
					margin-bottom: 20rpx;
					
					.invoice-date {
						font-size: 30rpx;
						color: #5F677D;
					}
					
					.invoice-status {
						font-size: 20rpx;
						color: #2A56E8;
						background-color: #EEF1FA;
						border: 1rpx solid #E4E9F8;
						border-radius: 12rpx;
						padding: 4rpx 14rpx;
					}
				}
				
				.invoice-content {
					display: flex;
					justify-content: space-between;
					align-items: flex-start;
					
					.invoice-type {
						font-size: 30rpx;
						font-weight: 500;
						color: #32364D;
						line-height: 1.5;
						max-width: 324rpx;
						white-space: pre-line;
					}
					
					.invoice-amount {
						display: flex;
						align-items: center;
						
						.invoice-amount-symbol {
							font-size: 20rpx;
							font-weight: 500;
							color: #171928;
							margin-top: 10rpx;
							margin-right: 2rpx;
						}
						
						.invoice-amount-value {
							font-size: 32rpx;
							color: #171928;
							font-family: TCloudNumber, sans-serif;
						}
					}
				}
			}
		}
	}
}
</style>
