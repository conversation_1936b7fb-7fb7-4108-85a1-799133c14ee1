import type { PropType } from 'vue'

export const NavBarProps = {
	type: {
		type: String as PropType<'light' | 'dark'>,
		default: 'light',
	},
	leftText: {
		type: String,
	},
	rightText: {
		type: String,
		default: '',
	},
	leftIcon: {
		type: String,
	},
	rightIcon: {
		type: String,
	},
	clickable: {
		type: Boolean,
		default: true,
	},
	navBarBackground: {
		type: String,
	},
	titlePosition: {
		type: String,
	},
}

export const TabbarProps = {
	list: {
		type: Array,
		default: () => [],
	},
	currentIndex: {
		type: Number,
		default: 0,
	},
	beforeChange: {
		type: Function as PropType<(func: Function, index: number) => void>,
	},
}

export const FooterBtnsProps = {
	footerBtnList: {
		type: Array,
		default: () => [],
	},
}

export const LayoutProps = {
	title: {
		type: String,
		default: '',
	},
	showNavBar: {
		type: Boolean,
		default: true,
	},
	showTabbar: {
		type: Boolean,
		default: false,
	},
	showFooterBtns: {
		type: Boolean,
		default: false,
	},
	safeAreaInsetBottom: {
		type: Boolean,
		default: false,
	},
	layoutBackground: {
		type: String as PropType<'default' | 'paymentLinearGradient' | 'mineLinearGradient' | 'homeLinearGradient'>,
		default: 'default',
	},
	backgroundImageType: {
		type: String as PropType<'default' | 'main' | 'image2' | 'yuangong'>,
		default: '',
	},
}

export const LayoutBodyProps = {
	scroll: {
		type: Boolean,
		default: false,
	},
	scrollTop: {
		type: Number,
		default: 0,
	},
}
