.ui-avatar {
	display: inline-flex;
    align-items: center;
    justify-content: center;
	width: 96rpx;
	height: 96rpx;
	line-height: 1;
	overflow: hidden;
	text-align: center;
	box-sizing: border-box;
	border: 1px solid var(--light-border-base);
	background-color: var(--light-primary-l3);
	border-radius: 100%;
	font-size: 64rpx;
	color: var(--dark-text-primary);
}

.ui-avatar-image {
	width: 100%;
	height: 100%;
	display: block;
	vertical-align: middle;
}

.ui-avatar-icon {
	font-size: 48rpx;
	color: var(--dark-icon-primary);
}

.ui-avatar-min {
	width: 48rpx;
	height: 48rpx;
	font-size: 28rpx;
	.ui-avatar-icon {
		font-size: 12px;
	}
}


.ui-avatar-sm {
	width: 72rpx;
	height: 72rpx;
	font-size: 44rpx;
	.ui-avatar-icon {
		font-size: 36rpx;
	}
}

.ui-avatar-md {
	width: 96rpx;
	height: 96rpx;
	font-size: 64rpx;
	.ui-avatar-icon {
		font-size: 48rpx;
	}
}

.ui-avatar-lg {
	width: 120rpx;
	height: 120rpx;
	font-size: 76rpx;
	.ui-avatar-icon {
		font-size: 60rpx;
	}
}

.ui-avatar-xlg {
	width: 144rpx;
	height: 144rpx;
	font-size: 92rpx;
	.ui-avatar-icon {
		font-size: 72rpx;
	}
}

.ui-avatar-xxlg {
	width: 320rpx;
	height: 320rpx;
	font-size: 232rpx;
	.ui-avatar-icon {
		font-size: 92px;
	}
}
