.ui-search {
	border-radius: var(--radius-circle);
	border: 1px solid var(--light-border-light);
	box-sizing: border-box;
	height: var(--search-md-height);
	padding: 0 var(--content-default-padding-md);
	background: var(--light-fill-blank);
}
.ui-search-content {
	display: flex;
	height: 100%;
	align-items: center;
	input {
		flex: 1;
		border: none;
		outline: none;
		color: var(--light-text-primary);
		font-size: var(--font-body-md);
		&::placeholder {
			color: var(--light-text-light);
		}
	}
}
.ui-search-icon {
	display: flex;
	box-sizing: border-box;
	align-items: center;
	height: 100%;
	margin-right: var(--content-default-margin-sm);
	padding-left: var(--content-default-padding-sm);
}
.ui-search-close-icon {
	display: flex;
	align-items: center;
}
