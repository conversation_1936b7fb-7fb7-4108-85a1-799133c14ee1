<template>
	<Layout title="我的表具" :leftArrow="true" left-text="" @left-click="navigateBack">
		<view class="page-content">
			<view class="meter-item-wrap" v-for="item in meterList" :key="item.meterId" @click="itemClick(item)">
				<view class="meter-item-left">
					<image class="meter-icon" src="/static/images/payment/gasMeter.png"></image>
					<view class="meter-name-no-wrap">
						<view class="meter-name">{{ item.meterTypeDes || '--' }}</view>
						<view class="meter-no">{{ item.meterNo || '--' }}</view>
					</view>
				</view>
				<view class="meter-item-right">
					<view class="meter-right-key">余额</view>
					<view class="meter-right-value">
						<view class="meter-account-blance">
							{{ item.tableSurplusAmt == undefined ? '--' : item.tableSurplusAmt || '0.00' }}
						</view>
						<view class="meter-account-blance-unit">元</view>
					</view>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import { queryMeterList } from '@/api/payment.api'
import { showMsg } from '@/core/util'
import type { MeterInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'MeterList',
})

const meterList = ref<MeterInfo[]>([])
let userInfo = {}

onMounted(() => {
	const instance = getCurrentInstance()?.proxy as any
	const eventChannel = instance.getOpenerEventChannel()
	eventChannel.on('meterList', function (data: MeterInfo[]) {
		if (Array.isArray(data)) {
			meterList.value = data
		}
	})
})

onLoad((options: any) => {
	getMeterList(options)
})

// 查询表具列表
const getMeterList = (params: any) => {
	queryMeterList({ userNo: params.userNo, addressId: params.addressId })
		.then(res => {
			if (res && Array.isArray(res.meterList)) {
				userInfo = res
				meterList.value = res.meterList
			} else {
				meterList.value = []
				showMsg('未查询到表具')
			}
		})
		.catch(() => {})
}

// 表具点击事件
const itemClick = (item: MeterInfo) => {
	uni.navigateTo({
		url: '/pages/payment/meter-detail/index',
		success: function (res) {
			res.eventChannel.emit('meterDetail', {
				userInfo: userInfo,
				meterInfo: item,
			})
		},
	})
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	.meter-item-wrap {
		border-radius: var(--content-default-radius-lg);
		padding: var(--content-default-padding-lg) var(--content-default-padding-md);
		border: 1px solid var(--light-border-dark);
		margin-bottom: var(--content-default-margin-md);
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		.meter-item-left {
			gap: var(--content-default-margin-min);
			display: flex;
			flex-direction: row;
			align-items: center;
			.meter-icon {
				width: 104rpx;
				height: 104rpx;
			}
			.meter-name-no-wrap {
				.meter-name {
					font-weight: 600;
					font-size: var(--font-body-lg);
					color: var(--light-text-title);
				}
				.meter-no {
					font-size: var(--font-body-md);
					color: var(--light-text-secondary);
					margin-top: 12rpx;
				}
			}
		}
		.meter-item-right {
			border-radius: var(--content-default-radius-sm);
			padding: var(--content-default-padding-xs) var(--content-default-padding-sm);
			background: linear-gradient(270deg, #eef1fa 0%, rgba(238, 241, 250, 0) 100%);
			width: 280rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: flex-end;
			height: 104rpx;
			.meter-right-key {
				font-size: var(--font-body-sm);
				color: var(--light-form-normal-default-label);
			}
			.meter-right-value {
				gap: var(--content-default-margin-xs);
				display: flex;
				flex-direction: row;
				align-items: flex-end;
				.meter-account-blance {
					font-size: var(--font-number-lg);
					color: var(--light-form-normal-default-text);
				}
				.meter-account-blance-unit {
					font-size: var(--font-body-min);
					color: var(--light-text-secondary);
					margin-bottom: 8rpx;
				}
			}
		}
	}
}
</style>
