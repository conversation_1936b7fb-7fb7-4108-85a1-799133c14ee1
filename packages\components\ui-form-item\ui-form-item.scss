.ui-form-item {
	padding: 0 var(--content-default-padding-lg);
	.form-item-wrap {
		display: flex;
		flex-direction: row;
		.form-item-label-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: var(--form-md-margin);
			margin-right: var(--content-default-margin-xlg);
			.label-padding-md {
				line-height: 44rpx;
				padding: 22rpx 0;
				box-sizing: border-box !important;
				font-size: var(--font-body-md);
			}
			.label-padding-lg {
				line-height: 48rpx;
				padding: 32rpx 0;
				box-sizing: border-box !important;
			}
			// 必填样式
			.required-md {
				&::after {
					content: '*';
					color: #D14646;
					position: absolute;
					margin-top: 12rpx;
					top: 0;
					height: 22px;
					line-height: 22px;
					font-size: var(--font-body-md);
				}
			}
			// 必填样式
			.required-lg {
				&::after {
					content: '*';
					color: #D14646;
					position: absolute;
					margin-top: 20rpx;
					top: 0;
					height: 22px;
					line-height: 22px;
					font-size: var(--font-body-md);
				}
			}
		}
	}
	.item-content {
		flex: 1;
	}
	.background {
		background: var(--light-form-normal-default-background-input);
		padding: 0 var(--content-default-padding-lg);
		border-radius: var(--form-md-radius);
	}
	.border-bottom {
		border-bottom: 1px solid var(--light-form-normal-default-underline);
	}
	// 获取焦点样式
	.border-focus {
		border-bottom: 1px solid var(--light-form-normal-focus-border)
	}
	// 获取焦点样式 - 全边框
	.border-focus-around {
		border: 1px solid var(--light-form-normal-focus-border)
	}
	.vertical {
		flex-direction: column;
		.form-item-label-wrap {
			margin-right: 0;
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: var(--form-md-margin);
			.label-padding-md {
				line-height: 44rpx;
				padding: 10rpx 0;
				font-size: var(--font-body-md);
			}
			.label-padding-lg {
				line-height: 44rpx;
				padding: 22rpx 0;
				font-size: var(--font-body-md);
			}
		}
	}
}
.padding{
	padding: 0 var(--content-default-padding-lg);
}
.no-padding {
	padding: 0;
}

