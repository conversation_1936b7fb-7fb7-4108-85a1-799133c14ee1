import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'
import vitePluginAppinfo from 'vite-plugin-build-info'

export default mode => {
	return defineConfig({
		plugins: [
			uni(),
			vitePluginAppinfo({
				showBuildUser: true,
			}),
		],
		resolve: {
			alias:
				mode === 'development'
					? {
							'@': '/src',
							'@e-cloud/eslink-plus-uniapp': resolve(__dirname, '../eslink-plus-uniapp/packages'),
						}
					: {
							'@': '/src',
							'@e-cloud/eslink-plus-uniapp': '/src/uni_modules/eslink-plus-uniapp',
						},
		},
		server: {
			port: 3000,
			host: '0.0.0.0',
			proxy: {
				'/selfHelpGateway': {
					target: 'https://cloudselfhelp.test-new.eslink.net.cn', // 测试环境
					// arget: 'http://wechat-service-hall.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
					// rewrite: path => path.replace(/^\/api/, ''),
				},
				// 暂时增加下面转发，等后端开发好后去掉
				'/cloudselfhelp': {
					target: 'https://etbc-web.test-new.eslink.net.cn', // 测试环境
					// arget: 'http://wechat-service-hall.test-new.eslink.net.cn:32080', // 测试环境
					// target: 'http://wechat-service-hall.eslink.cc', // 正式环境
					changeOrigin: true,
					// rewrite: path => path.replace(/^\/api/, ''),
				},
			},
		},
		css: {
			preprocessorOptions: {
				scss: {
					api: 'modern-compiler',
					silenceDeprecations: ['legacy-js-api'],
				},
			},
		},
	})
}
