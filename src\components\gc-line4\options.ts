export default data => {
	const xData = data ? data.map(item => item.xValue) : []
	const seriesData1 = data ? data.map(item => item.yValue1) : []
	return {
		grid: {
			containLabel: true,
			bottom: 0,
			left: 0,
			top: 40,
			right: 0,
		},
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisLabel: {
					color: '#5F677D',
					fontSize: 12,
					lineHeight: 16,
				},
				data: xData,
			},
		],
		yAxis: [
			{
				splitNumber: 4,
				type: 'value',
				show: true,
				name: 'm³',
				nameTextStyle: {
					color: '#5F677D',
					fontSize: 12,
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: 'rgba(0, 0, 26, 0.15)',
					},
				},
				axisLabel: {
					color: '#5F677D',
					fontSize: 14,
					lineHeight: 16,
				},
				axisLine: {
					show: false,
				},
			},
		],
		series: [
			{
				barWidth: 10,
				type: 'bar',
				symbol: 'none',
				smooth: true,
				itemStyle: {
					normal: {
						color: '#426AEA',
					},
				},
				data: seriesData1,
			},
		],
	}
}
