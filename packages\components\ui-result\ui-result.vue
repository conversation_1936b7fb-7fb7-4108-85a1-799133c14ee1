<template>
	<view class="ui-result">
		<view class="ui-result-top">
			<view class="ui-result-image">
				<image :src="icon[type]" fit="scaleToFill"></image>
			</view>
			<view class="ui-result-title">{{ title }}</view>
		</view>
		<view class="ui-result-bottom" v-if="$slots.default || (subTittle && description)">
			<slot />
			<view class="ui-result-subtitle" v-if="!$slots.default && subTittle">{{ subTittle }}</view>
			<view class="ui-result-description" v-if="!$slots.default && description">{{ description }}</view>
		</view>
	</view>
</template>
<script lang="ts" setup>
import warning from '../../assets/images/components/ui-result/warning.png'
import info from '../../assets/images/components/ui-result/info.png'
import success from '../../assets/images/components/ui-result/success.png'
import error from '../../assets/images/components/ui-result/error.png'

defineOptions({
	name: 'UiResult',
})

const props = withDefaults(
	defineProps<{
		type: 'error' | 'info' | 'success' | 'warning'
		title: string
		subTitle: string
		description: string
	}>(),
	{
		type: 'success',
		title: '操作成功',
		subTitle: '',
		description: '',
	},
)

const icon = {
	error,
	info,
	success,
	warning,
}
</script>
<style scoped lang="scss">
@use './ui-result.scss';
</style>
