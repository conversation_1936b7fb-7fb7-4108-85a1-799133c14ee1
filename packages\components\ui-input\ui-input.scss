.ui-input-wrap {}

.ui-input-label-inner {
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: var(--form-md-margin);
}
.ui-input-inner {
	position: relative;
	display: flex;
	flex-direction: row;
	align-items: center;
	gap: var(--form-md-margin);
	flex: 1;
}

.ui-input-padding-left {
	padding-left: var(--content-default-margin-xlg);
}

.right-icon-wrap {
	display: flex;
	flex-direction: row;
	align-items: center;
}

// 灰色背景
.ui-input-background {
	background: var(--light-form-normal-default-background-input);
	padding: 0 var(--content-default-padding-md);
	border-radius: var(--form-md-radius);
}

// 无背景时正常状态下边框
.ui-input-border-bottom {
	border-bottom: 1px solid var(--light-form-normal-default-underline)
}

// 获取焦点样式 -- 下边框
.ui-border-bottom-focus {
	border-bottom: 1px solid var(--light-form-normal-focus-border)
}

// 获取焦点样式 - 全边框
.ui-border-focus {
	border: 1px solid var(--light-form-normal-focus-border)
}

// 布局方向
.ui-input-direction-row {
	display: flex;
	flex-direction: row;
}
// label样式
.ui-input-label {
	font-weight: 500;
	font-size: var(--font-title-md);
	color: var(--light-form-normal-default-label);
}
// 输入框左边距
.ui-input-margin-left {
	margin-right: var(--fix-gap_less1);
}
// 输入框样式
.ui-input-input {
	flex: 1;
	color: var(--light-form-normal-default-text);
}
// 正常状态必填样式
.required-md {
	&::after {
		content: '*';
		color: #D14646;
		position: absolute;
		margin-top: 8rpx;
		height: 22px;
		line-height: 22px;
		font-size: var(--font-body-md);
	}
}
// 正常状态必填样式
.required-lg {
	&::after {
		content: '*';
		color: #D14646;
		position: absolute;
		margin-top: 20rpx;
		height: 22px;
		line-height: 22px;
		font-size: var(--font-body-md);
	}
}

// md 输入框高度
.ui-input-height-md {
	height: var(--form-md-height-md);
	line-height: var(--form-md-height-md);
}
// lg 输入框高度
.ui-input-height-lg {
	height: var(--form-lg-height-large);
	line-height: var(--form-lg-height-large);
}

// md textarea输入框高度
.ui-textarea-height-md {
	height: 132rpx;
}
// lg textarea输入框高度
.ui-textarea-height-lg {
	height: 144rpx;
}

// md 输入框标题文字大小
.ui-input-label-md {
	font-size: var(--font-body-md);
}
// lg 输入框标题文字大小
.ui-input-label-lg {
	font-size: var(--font-body-lg);
}

// md 输入框input文字大小
.ui-input-input-md {
	font-size: var(--font-body-md);
}
// lg 输入框input文字大小
.ui-input-input-lg {
	font-size: var(--font-body-lg);
}

// 标题禁用样式
.ui-input-label-disabled {
	color: var(--light-form-normal-disable-label);
}
// 输入框禁用样式
.ui-input-input-disabled {
	color: var(--light-form-normal-disable-text);
}
// 必填图标禁用样式
.required-disabled-md {
	&::after {
		content: '*';
		color: var(--light-form-normal-disable-label);;
		position: absolute;
		margin-top: 8rpx;
		height: 22px;
		line-height: 22px;
		font-size: var(--font-body-md);
	}
}
// 必填图标禁用样式
.required-disabled-lg {
	&::after {
		content: '*';
		color: var(--light-form-normal-disable-label);;
		position: absolute;
		margin-top: 20rpx;
		height: 22px;
		line-height: 22px;
		font-size: var(--font-body-md);
	}
}

// 多行输入框背景样式
.ui-input-textarea {
	min-height: 144rpx !important;
	border-radius: var(--form-md-radius);
	padding: var(--content-default-padding-sm) 0;
	color: var(--light-form-normal-default-text);
	flex: 1;
}

// 提示文字样式
.ui-input-placeholder {
	color: var(--light-form-placeholder);
}

