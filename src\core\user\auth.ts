import { ref } from 'vue'
import { channelType } from '@/config'
import { loginServiceApi, loginMobileApi, logOutApi } from '@/api/login.api'
import Cache from '../cache'
import { showMsg } from '@/core/util'

type LoginStatus = 1 | 2 | 3 // 登录状态 : 1.未登录服务器或登录失败  2.登录服务成功  3.手机登录成功

export default class {
	static phone = ''
	static verifyCode = ''
	public static loginStatus = ref<LoginStatus>(1) // 登录状态
	public static loginSuccess = function () {}

	public static login() {
		return new Promise((resolve, reject) => {
			if (this.loginStatus.value < 2) {
				this.firstLogin()
					.then(async res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			} else {
				this.loginMobile()
					.then(res => {
						resolve(res)
					})
					.catch(err => {
						reject(err)
					})
			}
		})
	}

	// 登录
	private static firstLogin() {
		return new Promise((resolve, reject) => {
			// H5、微信公众号直接调后端的登录接口
			// #ifdef H5 || WEB
			let token = ''
			const urlAry = window.location.href.split('?')
			if (urlAry.length > 1) {
				const paramsStr = urlAry[urlAry.length - 1]
				const params = new URLSearchParams(paramsStr)
				token = params.get('token') ?? ''
			}
			if (token) {
				Cache.saveChannelToken(token) // 保存token，所有接口在header里加
			}
			this.loginService({ channelTypeKey: '2', code: token })
				.then(res => {
					resolve(res)
				})
				.catch(err => {
					reject(err)
				})
			// #endif

			// 微信小程序、支付宝小程序需要先登录到微信或者支付宝平台，再调后端的登录接口
			// #ifdef MP
			this.loginPlatform()
				.then((platformRes: any) => {
					// 保存token，所有接口在header里加
					Cache.saveChannelToken(platformRes.code)
					const accountInfo = uni.getAccountInfoSync()
					const extConfig = uni.getExtConfigSync()
					let appId = accountInfo.miniProgram.appId
					// #ifdef MP-ALIPAY
					appId = extConfig.appId
					// #endif
					// 登录后端接口
					this.loginService({
						code: platformRes.code,
						channelTypeKey: channelType,
						appId: appId,
					})
						.then(res => {
							resolve(res)
						})
						.catch(err => {
							reject(err)
						})
				})
				.catch(err => {
					reject(err)
				})
			// #endif
		})
	}
	// 登录到平台
	private static loginPlatform() {
		return new Promise((resolve, reject) => {
			uni.getProvider({
				service: 'oauth',
				success: (res: UniApp.GetProviderRes) => {
					console.log(res)
					uni.login({
						provider: res.provider,
						success: loginRes => {
							console.log('登录平台成功:', loginRes)
							resolve({ success: true, message: '登录平台成功！', code: loginRes.code })
						},
						fail: err => {
							showMsg('登录平台失败', err)
							reject(err)
						},
					})
				},
			})
		})
	}
	// H5或者微信公众号登录到后端服务器
	private static loginService(params: {}) {
		return new Promise((resolve, reject) => {
			//从当前页面的url地址中取出主机地址
			loginServiceApi(params)
				.then(res => {
					console.log('登录服务器接口调用成功')
					// 如果已手机登录则保存accessToken
					if (res && res.accessToken) {
						Cache.saveToken(res.accessToken) // 保存token，所有接口在header里加
						this.loginStatus.value = 3
						this.loginSuccess()
					} else {
						Cache.removeToken() // 移除全局token
						this.loginStatus.value = 2
					}
					resolve(res)
				})
				.catch(err => {
					this.loginStatus.value = 1
					reject(err)
				})
		})
	}

	// 手机号登录
	private static loginMobile() {
		return new Promise((resolve, reject) => {
			loginMobileApi({ phone: this.phone, verifyCode: this.verifyCode })
				.then(res => {
					// 如果已手机登录则保存accessToken
					if (res && res.accessToken) {
						Cache.saveToken(res.accessToken) // 保存token，所有接口在header里加
						this.loginStatus.value = 3
						this.loginSuccess()
					}
					resolve(res)
				})
				.catch(err => {
					reject(err)
				})
		})
	}

	// 退出登录
	public static logOut() {
		return new Promise((resolve, reject) => {
			logOutApi()
				.then(res => {
					console.log('退出登录成功！')
					showMsg('退出登录成功！')
					this.loginStatus.value = 2
					Cache.removeToken() // 移除全局token
					resolve(res)
				})
				.catch(err => {
					reject(err)
				})
		})
	}
}
