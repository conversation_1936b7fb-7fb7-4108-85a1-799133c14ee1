<template>
	<view class="ui-form-item" :class="{'no-padding': background && !vertical}">
		<view
			class="form-item-wrap"
			:class="{
				vertical: vertical,
				'border-focus': focus && !background,
				'border-bottom': !background,
				'background': background && !vertical,
				'border-focus-around': focus && background && !vertical}"
		>
			<view class="form-item-label-wrap" :style="{ width: labelWidth }">
				<slot name="labelLeft"></slot>
				<view
					:class="[
						`label-padding-${size}`,
						required ? `required-${size}` : '',
					]"
					v-if="label"
				>
					{{ label}}
				</view>
				<slot name="labelRight"></slot>
			</view>
			<view class="item-content">
				<slot></slot>
			</view>
		</view>
		
	</view>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'

const props = defineProps({
	label: {
		type: String,
		default: '',
	},
	required: {
		type: Boolean,
		default: false,
	},
	showRequired: {
		type: Boolean,
		default: true,
	},
	prefixIcon: {
		type: String,
		default: '',
	},
	suffixIcon: {
		type: String,
		default: '',
	},
	labelWidth: {
		type: String,
		default: '',
	},
	size: {
		type: String,
		default: 'md',
	},
	vertical: {
		type: Boolean,
		default: false,
	},
	focus: {
		type: Boolean,
		default: false,
	},
	background: {
		type: Boolean,
		default: false,
	}
})
// 必填样式
const getReauiredClass = () => {
	const { required, size } = props
	return required ? `required-${size}` : ''
}
</script>
<style lang="scss">
@use './ui-form-item.scss';
</style>
