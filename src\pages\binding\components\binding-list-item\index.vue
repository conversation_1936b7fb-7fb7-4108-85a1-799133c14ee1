<template>
	<view class="binding-list-item-wrap">
		<view class="list-item-content-wrap" @click="itemClick">
			<image
				v-if="dataItem.userInfoChangeType == '1' || dataItem.userInfoChangeType == '2'"
				class="binding-expired-icon"
				src="/static/images/binding/bindingExpired.png"
			></image>
			<view class="left-wrap">
				<view class="list-item-header">
					<view class="list-item-header-left">
						<view class="list-item-title">{{ dataItem.userName || '' }}</view>
						<view class="tag-view">
							<UiTag v-if="dataItem.defaultUser" type="primary" size="sm" text="默认"></UiTag>
							<view v-if="dataItem.userRemark" class="custom-tag-view">
								<UiTag
									v-for="tag in tagList"
									:key="tag.title"
									:type="tag.type"
									:size="tag.size"
									:text="tag.title"
								></UiTag>
							</view>
							<UiTag
								v-if="tagList.length < 4 && pageType == 'binding'"
								type="default"
								size="sm"
								icon="icon-add-line"
								@click="addTag"
							></UiTag>
						</view>
					</view>
					<view v-if="pageType == 'binding'">
						<UiButton type="transparent" size="md" icon="icon-qr-code-fill" @click="qrCodeClick"></UiButton>
					</view>
				</view>
				<view class="list-item-content">
					<UiCell
						v-for="(prop, index) in userProps"
						:key="index"
						:title="dataItem[prop.value]"
						size="sm"
						:noPadding="true"
					>
						<template #prefix>
							<image class="user-info-icon" :src="prop.icon"></image>
						</template>
						<template #subTitle v-if="index == 0">
							<i class="iconfont icon-file-copy-line user-no-copy-icon" @click="copyUserNo"></i>
						</template>
					</UiCell>
					<view class="binding-action-wrap" v-if="pageType == 'binding'">
						<view class="default-action-wrap">
							<UiRadio :checked="dataItem.defaultUser == 1" @change="settingDefaultUser">
								{{ dataItem.defaultUser == 1 ? '已默认' : '设为默认' }}
							</UiRadio>
						</view>
						<view class="other-action-wrap">
							<UiTag type="default" size="sm" text="解绑" @click="unBindingUserClick"></UiTag>
							<UiTag v-if="false" type="default" size="sm" text="申请户主认证"></UiTag>
							<UiTag
								v-if="dataItem.userInfoChangeType == '1' || dataItem.userInfoChangeType == '2'"
								type="default"
								size="sm"
								text="刷新"
								@click="refreshClick"
							></UiTag>
						</view>
					</view>
					<view class="binding-btn-wrap" v-if="pageType == 'binding'">
						<view>
							<UiButton
								type="normal"
								icon="icon-sysicon"
								size="md"
								style-name="light"
								text="去缴费"
								@click="payClick"
							></UiButton>
						</view>
					</view>
				</view>
			</view>
			<view class="right-icon" v-if="pageType != 'binding'">
				<image v-if="dataItem.selected" class="select-image" src="/static/images/binding/select.png"></image>
				<image v-else class="select-image" src="/static/images/binding/un-select.png"></image>
			</view>
		</view>
		<UiPopupConfirm
			v-model="confirmVisible"
			ref="popupConfirmRef"
			title="确认解绑？"
			cancelText="取消"
			confirmText="解绑"
			width="686rpx"
			@confirm="unBindUserInfo"
		>
			<view class="unbinding-remind">解绑后该信息将被删除且无法恢复</view>
		</UiPopupConfirm>
		<UiPopupConfirm
			v-model="popupShow"
			ref="popupConfirmRef"
			title="二维码"
			cancelText="确认"
			width="686rpx"
			:showConfirmButton="false"
		>
			<image class="qrcode-image" src="/static/images/binding/qrCode.png"></image>
		</UiPopupConfirm>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import type { PropType } from 'vue'
import type { UserInfo } from '@/pages/commonTypes'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiPopupConfirm from '@e-cloud/eslink-plus-uniapp/components/ui-popup-confirm/ui-popup-confirm.vue'
import userNoIcon from '@/static/images/common/user-no-icon.png'
import addressIcon from '@/static/images/common/address-icon.png'
import { refreshBindUserInfoApi, unUserBindApi } from '@/api/userBind.api'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'BindingListItem',
})

const props = defineProps({
	// 绑定的用户
	dataItem: {
		type: Object as PropType<UserInfo>,
		default: {},
	},
	pageType: {
		type: String,
		default: 'binding',
	},
})

const popupShow = ref(false)
const confirmVisible = ref(false)
// 展示的字段
const userProps = ref([
	{
		key: '户号',
		value: 'userNo',
		icon: userNoIcon,
	},
	{
		key: '地址',
		value: 'userAddress',
		value1: 'addressId',
		icon: addressIcon,
	},
])
// 绑定关系改变

onMounted(() => {})

const tagList = computed(() => {
	let list = [] as any[]
	if (props.dataItem.userRemark) {
		list = props.dataItem.userRemark.split(',')
	}
	const typeList = ['success', 'error', 'otherflow']
	return list.map((item, index) => {
		return {
			size: 'sm',
			type: typeList[index],
			title: item,
		}
	})
})

watch(
	() => props.dataItem.defaultUser,
	newValue => {
		console.log('newValue', newValue)
	},
)
const emits = defineEmits(['refresh', 'setDefaultUser', 'itemClick'])

// item点击
const itemClick = () => {
	emits('itemClick', props.dataItem)
}

// 二维码点击
const qrCodeClick = () => {
	popupShow.value = true
}

// 弹窗取消按钮点击
const popupCancel = () => {
	popupShow.value = false
}

// 解绑点击
const unBindingUserClick = () => {
	confirmVisible.value = true
}

// 添加标签
const addTag = () => {
	uni.navigateTo({
		url:
			'/pages/binding/tag-manager/index?userRemark=' +
			(props.dataItem.userRemark || '') +
			'&dataId=' +
			(props.dataItem.id || ''),
	})
}
// 复制用户号
const copyUserNo = () => {
	uni.setClipboardData({
		data: props.dataItem.userNo,
	})
}

// 设置默认用户
const settingDefaultUser = () => {
	console.log('设置默认用户, ', props.dataItem.defaultUser)
	emits('setDefaultUser', props.dataItem)
}
// 刷新按钮点击
const refreshClick = () => {
	let params = {
		userNo: props.dataItem.userNo || '',
		meterNo: props.dataItem.meterNo || '',
		addressId: props.dataItem.addressId || '',
	}
	refreshBindUserInfoApi(params)
		.then(() => {
			showMsg('刷新成功')
			emits('refresh')
		})
		.catch(() => {})
}

// 用户解绑
const unBindUserInfo = () => {
	unUserBindApi(props.dataItem.id.toString())
		.then(() => {
			showMsg('解绑成功')
			emits('refresh')
		})
		.catch(() => {})
}

// 跳转缴费
const payClick = () => {
	uni.navigateTo({
		url:
			'/pages/payment/payment/index?userNo=' +
			(props.dataItem.userNo || '') +
			'&addressId=' +
			(props.dataItem.addressId || ''),
	})
}
</script>

<style lang="scss" scoped>
.binding-list-item-wrap {
	position: relative;
	border-radius: var(--content-default-radius-md);
	background: var(--light-fill-blank);
	margin-top: 20rpx;
	padding-top: var(--content-default-padding-sm);
	.list-item-content-wrap {
		display: flex;
		flex-direction: row;
		justify-content: center;
		.binding-expired-icon {
			position: absolute;
			width: 128rpx;
			height: 128rpx;
			top: 16rpx;
			right: 96rpx;
			z-index: 1;
		}
		.left-wrap {
			flex: 1;
			.list-item-header {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				height: var(--form-md-height-md);
				padding-left: var(--content-default-padding-lg);
				padding-right: var(--content-default-padding-min);
				.list-item-header-left {
					display: flex;
					flex-direction: row;
					.list-item-title {
						font-weight: 600;
						font-size: var(--font-title-md);
						color: var(--light-text-title);
					}
					.tag-view {
						display: flex;
						flex-direction: row;
						margin-left: 20rpx;
						gap: 8rpx;
						.custom-tag-view {
							display: flex;
							flex-direction: row;
							gap: 8rpx;
						}
					}
				}
			}

			.list-item-content {
				padding: 0 var(--content-default-padding-md);
				.user-no-copy-icon {
					width: var(--icon-md);
					height: var(--icon-md);
					margin-left: 12rpx;
					color: var(--light-form-normal-default-icon-light);
				}
				.margin-bottom {
					margin-bottom: 24rpx;
				}
				.binding-action-wrap {
					display: flex;
					flex-direction: row;
					justify-content: space-between;
					align-items: center;
					height: 80rpx;
					.default-action-wrap {
						display: flex;
						flex-direction: row;
						align-items: center;
						gap: 16rpx;
						.set-default-label {
							font-size: var(--font-body-md);
							color: var(--light-form-normal-default-label);
						}
					}
					.other-action-wrap {
						display: flex;
						flex-direction: row;
						gap: 16rpx;
					}
				}
				.binding-btn-wrap {
					width: 100%;
					height: 96rpx;
					border-top: 2px solid var(--light-border-lighter);
					display: flex;
					flex-direction: row;
					align-items: center;
					justify-content: flex-end;
				}
			}
		}
		.right-icon {
			width: 88rpx;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			.select-image {
				width: 40rpx;
				height: 40rpx;
			}
		}
	}

	.qrcode-image {
		width: 478rpx;
		height: 478rpx;
	}
}
.unbinding-remind {
	font-size: var(--font-body-lg);
	color: var(--light-text-primary);
}
.user-info-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
	margin-top: 6rpx;
}
:deep(.setting-cell-wrap .cell-left .cell-title-sm) {
	color: var(--light-list-normal-default-text);
	font-weight: 400;
}
</style>
