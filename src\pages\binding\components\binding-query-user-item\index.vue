<template>
	<view class="user-info-item" :class="{ 'user-info-item-active': userInfo.selected }" @click="chooseUserInfo">
		<view class="user-info-content">
			<UiTitle :title="userInfo.userName || ''" size="md" :noPadding="true">
				<template #subTitle>
					<UiTag
						:type="userInfo.isBind ? 'primary' : 'default'"
						size="sm"
						:text="userInfo.isBind ? '已绑定' : '未绑定'"
					></UiTag>
				</template>
			</UiTitle>
			<UiCell
				v-for="(prop, index) in userProps"
				:key="index"
				:title="userInfo[prop.value]"
				size="sm"
				:noPadding="true"
			>
				<template #prefix>
					<image class="user-info-icon" :src="prop.icon"></image>
				</template>
			</UiCell>
		</view>
		<UiRadio :checked="userInfo.selected" :disabled="checkDisabled"></UiRadio>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { PropType } from 'vue'
import type { UserInfo } from '@/pages/commonTypes'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import userNoIcon from '@/static/images/common/user-no-icon.png'
import addressIcon from '@/static/images/common/address-icon.png'

defineOptions({
	name: 'BindingQueryUserItem',
})

const props = defineProps({
	// 绑定的用户
	userInfo: {
		type: Object as PropType<UserInfo>,
		default: {},
	},
})

// 展示的字段
const userProps = ref([
	{
		key: '户号',
		value: 'userNo',
		icon: userNoIcon,
	},
	{
		key: '地址',
		value: 'userAddress',
		value1: 'addressId',
		icon: addressIcon,
	},
])

const checkDisabled = computed(() => {
	return props.userInfo.isBind
})

const emits = defineEmits(['itemClick'])

// 选择用户点击事件
const chooseUserInfo = () => {
	if (!checkDisabled.value) {
		emits('itemClick', props.userInfo)
	}
}
</script>

<style lang="scss" scoped>
.user-info-item {
	border: 1px solid var(--light-border-light);
	border-radius: var(--content-default-radius-md);
	padding: 0 var(--content-default-padding-md);
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	margin-bottom: var(--content-default-margin-md);
	background: white;
	.user-info-content {
		padding: var(--content-default-padding-xs) 0;
	}
}
.user-info-item-active {
	border: 1px solid var(--light-primary-default);
}
.user-info-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
	margin-top: 6rpx;
}
:deep(.setting-cell-wrap .cell-left .cell-title-sm) {
	color: var(--light-list-normal-default-text);
	font-weight: 400;
}
</style>
