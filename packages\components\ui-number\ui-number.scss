@font-face {
	font-family: 'number-bold';
	src: url('../../assets/number/TCloudNumber-Bold.ttf') format('truetype');
}

@font-face {
	font-family: 'number-normal';
	src: url('../../assets/number/TCloudNumber-Regular.ttf') format('truetype');
}

@font-face {
	font-family: 'number-light';
	src: url('../../assets/number/TCloudNumber-Light.ttf') format('truetype');
}

.ui-number {
	display: flex;
	align-items: baseline;
}

.ui-number-value {
	color: #171928;
}

.number-bold {
	font-family: 'number-bold' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.number-normal {
	font-family: 'number-normal' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.number-light {
	font-family: 'number-light' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}


.ui-number-unit {
	color: var(--light-text-secondary);
	font-weight: 400;
	font-size: var(--font-body-min);
	line-height: 28rpx;
}
.align-center {
	align-items: center;
}
