<template>
	<view class="menu-item-wrap">
		<UiCardTabs v-model="userType" :options="userTypeList" @change="userTypeClick">
			<view class="user-info-wrap" v-if="User.loginStatus.value < 3 || userList.length == 0">
				<view class="user-info-top">
					<view class="user-info-title">
						<view class="welcomew-text">欢迎您！</view>
					</view>
					<view>
						<UiButton
							v-if="User.loginStatus.value == 2"
							type="normal"
							size="md"
							style-name="light"
							text="立即登录"
							@click="loginClick"
						></UiButton>
						<UiButton
							v-if="User.loginStatus.value == 3"
							type="normal"
							size="md"
							style-name="light"
							text="添加户号"
							@click="goBindingClick"
						></UiButton>
					</view>
				</view>
				<view class="add-remind" v-if="User.loginStatus.value == 1">未连接服务器</view>
				<view class="add-remind" v-if="User.loginStatus.value == 2">登录账号获取更多信息</view>
				<view class="add-remind" v-if="User.loginStatus.value == 3">添加户号获取更多信息</view>
			</view>
			<swiper
				v-else
				:style="{ height: swiperHeight + 'rpx' }"
				circular
				:indicator-dots="true"
				:autoplay="false"
				:interval="2000"
				:duration="500"
			>
				<swiper-item v-for="item in userList" :key="item" style="overflow: auto">
					<view class="user-info-wrap">
						<view class="user-info-top">
							<view class="user-info-title">
								<view class="user-info-title-wrap">
									<view class="user-info-name">{{ item.userName || '--' }}</view>
									<view class="user-info-no">
										<UiTag type="default" size="sm" :text="item.userNo || '--'"></UiTag>
									</view>
								</view>
							</view>
							<view>
								<UiButton
									type="normal"
									size="md"
									width="150rpx"
									style-name="light"
									text="去缴费"
									@click="goPaymentClick(item)"
								></UiButton>
							</view>
						</view>
						<view class="user-info-content">
							<view class="user-info-money-wrap" :class="{ 'no-line': !isExpand }">
								<view class="user-info-money-left">
									<view class="money-left-line first-line">
										<view class="money-left-title">应缴金额</view>
										<i class="iconfont icon-help-icon help-icon" @click="helpClick"></i>
										<i
											class="iconfont eye-icon"
											:class="{ 'icon-eye-open': showMoney, 'icon-eye-close': !showMoney }"
											@click="eyeClick"
										></i>
									</view>
									<view class="money-left-line second-line">
										<UiNumber
											weight="normal"
											:value="item.accountBalance || '0.00'"
											valueSize="64rpx"
											unitSize="30rpx"
											gap="4px"
											unit="元"
										></UiNumber>
										<i class="iconfont icon-refresh-line refresh-icon" @click="refreshClick"></i>
									</view>
									<view class="money-left-line third-line" v-if="showMoney">
										<view class="account-blance-label">账户余额</view>
										<UiNumber
											weight="normal"
											:value="item.accountBalance || '0.00'"
											valueSize="40rpx"
											unitSize="30rpx"
											gap="4px"
											unit="元"
										></UiNumber>
									</view>
								</view>
								<view class="user-info-money-expand" @click="expandClick">
									<view class="expand-label">{{ isExpand ? '收起' : '展开' }}</view>
									<i
										class="iconfont arrow-drop-icon"
										:class="{ 'icon-arrow-drop-up': isExpand, 'icon-arrow-drop-down': !isExpand }"
									></i>
								</view>
							</view>
							<view
								class="user-use-info-wrap"
								:class="{ 'iot-user-info-wrap': item.userMeterType == 'IOT' }"
								v-if="isExpand"
							>
								<view
									class="use-info-item-wrap"
									v-for="prop in item.userMeterType == 'IOT' ? iotUserInfoProps : pbUserInfoProps"
									:key="prop.id"
								>
									<view class="use-info-item-label">{{ prop.label }}</view>
									<view class="use-info-item-value-wrap">
										<UiNumber
											weight="normal"
											:value="item[prop.value] || '24.00'"
											valueSize="32rpx"
											unitSize="20rpx"
											gap="4px"
											:unit="prop.unit || ''"
										></UiNumber>
									</view>
								</view>
							</view>
							<view class="page-icon-wrap"></view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</UiCardTabs>
		<UiPopupConfirm
			v-model="popupShow"
			ref="popupConfirmRef"
			title="应缴金额"
			cancelText="我知道了"
			width="686rpx"
			:showConfirmButton="false"
		>
			<view class="unbinding-remind">应缴金额描述</view>
			<template #footer>
				<view class="popup-footer" @click="popupCancel">我知道了</view>
			</template>
		</UiPopupConfirm>
	</view>
</template>

<script setup lang="ts">
import { ref, nextTick, getCurrentInstance } from 'vue'
import type { PropType } from 'vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiNumber from '@e-cloud/eslink-plus-uniapp/components/ui-number/ui-number.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCardTabs from '@e-cloud/eslink-plus-uniapp/components/ui-card-tabs/ui-card-tabs.vue'
import UiPopupConfirm from '@e-cloud/eslink-plus-uniapp/components/ui-popup-confirm/ui-popup-confirm.vue'
import User from '@/core/user'
import { getLoginPageUrl } from '@/core/util'
import type { UserInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'UserInfoCard',
})

const props = defineProps({
	// 用户列表
	userList: {
		type: Array as PropType<UserInfo[]>,
		default: [{}, {}],
	},
})
// 是否显示金额
const showMoney = ref(true)
// 展开收起
const isExpand = ref(true)
// 轮播组件高度
const swiperHeight = ref(204) // 默认高度204rpx
const popupShow = ref(false)

// 当前选中的用户类型 - 居民户:2, 工商户: 1
const userType = ref('2')
// 用户类型列表
const userTypeList = [
	{ label: '居民户', value: '2' },
	{ label: '工商户', value: '1' },
]
// 普表下面的用气信息
const pbUserInfoProps = ref([
	{
		id: '1',
		label: '最近气量',
		value: 'useGas',
		unit: 'm³',
	},
	{
		id: '2',
		label: '最近气费',
		value: 'useGasMoney',
		unit: '元',
	},
	{
		id: '3',
		label: '周期累计用气',
		value: 'useGasTotal',
		unit: 'm³',
	},
	{
		id: '4',
		label: '周期累计费用',
		value: 'totalMoney',
		unit: '元',
	},
])
// 物联网表下面的用气信息
const iotUserInfoProps = ref([
	{
		id: '1',
		label: '上次充值',
		value: 'useGas',
		unit: '元',
	},
	{
		id: '2',
		label: '累计充值',
		value: 'useGasMoney',
		unit: '元',
	},
])

const emits = defineEmits(['refresh', 'userTypeClick'])

// 工商、居民户tab切换
const userTypeClick = (value: string) => {
	userType.value = value
	emits('userTypeClick')
}

// 立即登录按钮点击
const loginClick = () => {
	uni.navigateTo({
		url: getLoginPageUrl('/pages/home/<USER>/index'),
	})
}
// 添加户号按钮点击
const goBindingClick = () => {
	uni.navigateTo({
		url: '/pages/binding/binding-list/index',
	})
}

// 去缴费按钮点击
const goPaymentClick = (item: UserInfo) => {
	uni.navigateTo({
		url: '/pages/payment/payment/index?userNo=' + (item.userNo || '') + '&addressId=' + (item.addressId || ''),
	})
}
// 问号点击
const helpClick = () => {
	popupShow.value = true
}
// 弹窗取消按钮点击
const popupCancel = () => {
	popupShow.value = false
}

// 眼睛点击
const eyeClick = () => {
	console.log('点击了眼睛')
	showMoney.value = !showMoney.value
	reSetSwiperHeight()
}

// 刷新按钮点击
const refreshClick = () => {
	console.log('点击了刷新')
	emits('refresh')
}

// 展开收起点击
const expandClick = () => {
	console.log('点击了展开收起')
	isExpand.value = !isExpand.value
	reSetSwiperHeight()
}
const instance = getCurrentInstance()
// 重新设置轮播组件的高度
const reSetSwiperHeight = () => {
	nextTick(() => {
		// swiper高度自适应内容高度
		uni.createSelectorQuery()
			.in(instance)
			.select('.user-info-wrap')
			.boundingClientRect((rect: any) => {
				if (rect && rect.height) {
					const sysInfo = uni.getSystemInfoSync()
					const scale = 750 / sysInfo.screenWidth // rpx默认的屏幕宽度是750
					swiperHeight.value = rect.height * scale
				}
			})
			.exec()
	})
}

defineExpose({
	reSetSwiperHeight,
	userType,
})
</script>

<style lang="scss" scoped>
.user-info-wrap {
	padding: var(--content-default-padding-sm) var(--content-default-padding-lg);
	background: linear-gradient(180deg, #ffffff 0%, #f2f6ff 100%);
	border-right: 1px solid;
	border-left: 1px solid;
	border-bottom: 1px solid;
	border-image: linear-gradient(180deg, rgba(234, 237, 247, 0) 0%, #dbe0ee 100%) 1;
	clip-path: inset(0 round 20rpx);

	.user-info-top {
		padding: 16rpx 0;
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		.user-info-title {
			font-weight: 600;
			font-size: var(--font-title-lg);
			color: var(--light-text-title);
			margin-right: 10rpx;
			flex: 1;
			.user-info-title-wrap {
				display: flex;
				flex-direction: row;
				gap: var(--content-default-margin-xs);
				.user-info-name {
					max-width: 50%;
				}
				.user-info-no {
					margin-top: 6rpx;
				}
			}
		}
	}
	.add-remind {
		height: 68rpx;
		font-size: var(--font-body-md);
		color: var(--light-form-normal-default-text);
	}
	.user-info-content {
		.no-line {
			border-bottom: 0 !important;
		}
		.user-info-money-wrap {
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			padding-bottom: var(--content-default-padding-xs);
			border-bottom: 1px solid var(--light-border-dark);
			.user-info-money-left {
				flex: 1;
				.money-left-line {
					display: flex;
					flex-direction: row;
					align-items: center;
					.money-left-title {
						font-size: var(--font-body-md);
						color: var(--light-text-secondary);
					}
					.account-blance-label {
						font-size: var(--font-body-md);
						color: var(--light-text-secondary);
					}
					.help-icon,
					.eye-icon,
					.refresh-icon {
						font-size: var(--icon-md);
						color: var(--light-button-default-transparent-icon);
					}
				}
				.first-line {
					height: 60rpx;
					gap: var(--form-md-margin);
				}
				.second-line {
					height: var(--form-md-height-md);
					gap: var(--form-md-margin);
				}
				.third-line {
					height: 60rpx;
					gap: var(--content-default-margin-xs);
				}
			}
			.user-info-money-expand {
				width: 116rpx;
				height: 60rpx;
				display: flex;
				flex-direction: row;
				align-items: center;
				justify-content: center;
				.expand-label {
					font-size: var(--font-body-sm);
					color: var(--light-button-default-transparent-text);
				}
				.arrow-drop-icon {
					font-size: var(--icon-md);
					color: var(--light-button-default-transparent-icon);
				}
			}
		}
		.user-use-info-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
			padding: var(--content-default-padding-sm) 0;
			overflow-x: auto;
			.use-info-item-wrap {
				gap: var(--content-default-margin-xs);
				height: 92rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				align-items: center;
				flex-shrink: 0;
				.use-info-item-label {
					font-size: var(--font-body-sm);
					color: var(--light-form-normal-default-label);
				}
				.use-info-item-value-wrap {
					display: flex;
					flex-direction: row;
					align-items: flex-end;
				}
			}
		}
		.iot-user-info-wrap {
			justify-content: space-around;
		}
		.page-icon-wrap {
			height: 32rpx;
			line-height: 32rpx;
			text-align: center;
		}
	}
}
.popup-footer {
	width: 100%;
	height: 96rpx;
	line-height: 96rpx;
	color: var(--light-button-default-link-text);
	text-align: center;
}
</style>
