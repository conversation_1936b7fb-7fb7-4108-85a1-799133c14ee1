<template>
	<view class="ui-tips" :class="classNames">
		<view v-if="icon || $slots.icon" class="ui-tips-prefix">
			<slot name="icon">
				<ui-icon v-if="icon" class="ui-tips-prefix-icon" customClass="ui-tips-prefix-icon" :name="icon" size="md"></ui-icon>
			</slot>
		</view>
		<view class="ui-tips-content">
			<slot>
				<text>{{ content }}</text>
			</slot>
		</view>
		<!-- TODO closable -->
		<!-- <ui-icon class="ui-tips-close-button" name="close-line"></ui-icon> -->
	</view>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useMpOptions } from '../../hooks/useMpOptions'
import type { TipsProps } from './types'
import { TipsTypeEnum } from './types'
import UiIcon from '../ui-icon/ui-icon.vue'

defineOptions({
	name: 'uiTips',
	...useMpOptions(),
})

const props = withDefaults(defineProps<TipsProps>(), {
	type: TipsTypeEnum.info,
	content: '',
	icon: 'information-line',
})

const classNames = computed(() => {
	const classNames = []
	const { type, customClass } = props
	if (type) {
		classNames.push(`ui-tips-${type}`)
	}

	if (customClass) {
		classNames.push(customClass)
	}

	return classNames
})
</script>
<style lang="scss">
@import url('./ui-tips.scss');
</style>
