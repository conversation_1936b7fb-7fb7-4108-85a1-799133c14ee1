<template>
	<Layout
		title="账单"
		left-text=""
		nav-bar-background="transparent"
		backgroundImageType="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<UserInfoCard
				ref="userInfoCard"
				pageType="payment"
				:userNo="currentUser.userNo"
				:addressId="currentUser.addressId"
				@changeUser="changeUserClick"
			></UserInfoCard>
			<view class="params-wrap">
				<UiDatepicker v-model="currentDate" type="month"></UiDatepicker>
				<UiDropdown v-model="currentDate" :options="options"></UiDropdown>
			</view>
			<view class="bill-detail-wrap">
				<view class="bill-item-list-wrap">
					<UiTitle title="账单明细" size="md"></UiTitle>
					<UiCell title="账期" size="sm" value="2025年5月"></UiCell>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiDropdown from '@e-cloud/eslink-plus-uniapp/components/ui-dropdown/ui-dropdown.vue'
import UiDatepicker from '@e-cloud/eslink-plus-uniapp/components/ui-datepicker/ui-datepicker.vue'
import type { UserInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'RecordList',
})

const currentUser = ref({} as UserInfo) // 当前默认用户
const currentDate = ref('')
const options = ref([
	{
		label: '今天',
		value: 'today',
	},
])

onMounted(() => {})

const changeUserClick = () => {}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.params-wrap {
	height: 112rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	margin-top: 24rpx;
	padding: 0 var(--content-default-padding-lg);
}

.bill-detail-wrap {
	background: var(--light-fill-blank);
	border-radius: var(--content-default-radius-md);
	padding: var(--content-default-padding-xs) 0;
	.bill-item-list-wrap {
		padding: var(--content-default-padding-sm) 0;
		.setting-cell-wrap {
			justify-content: flex-start;
		}
		:deep(.cell-right) {
			margin-left: 48rpx;
			justify-content: flex-start;
		}
		:deep(.cell-title-sm) {
			color: var(--light-form-normal-default-text-light);
			font-weight: 400;
		}
	}
}
</style>
