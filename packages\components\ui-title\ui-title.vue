<template>
	<view class="ui-title" :class="classNames">
		<view class="ui-title-inner">
			<view class="ui-title-left">
				<view v-if="prefixIcon || $slots.prefix" class="ui-title-prefix">
					<slot name="prefix">
						<ui-icon
							v-if="prefixIcon"
							class="ui-title-prefix-icon"
							:name="prefixIcon"
							:textColor="textColor"
							@click="handlePrefixClick"
						></ui-icon>
					</slot>
				</view>
				<view class="ui-title-title" :style="{color: textColor }">
					<slot name="title">
						{{ title }}
					</slot>
				</view>
				<view v-if="subTitle || $slots.subTitle" class="ui-title-sub-title">
					<slot name="subTitle">
						{{ subTitle }}
					</slot>
				</view>
				<view class="ui-title-suffix" v-if="suffixIcon || $slots.suffix">
					<slot name="suffix">
						<ui-icon
							v-if="suffixIcon"
							class="ui-title-suffix-icon"
							:name="suffixIcon"
							:textColor="textColor"
							@click="handleSuffixClick"
						></ui-icon>
					</slot>
				</view>
			</view>
			<view class="ui-title-extra" v-if="$slots.extra">
				<slot name="extra"></slot>
			</view>
		</view>
	</view>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { SizeEnum } from '../../shared/enum'
import type { TitleProps } from './types'

import UiIcon from '../ui-icon/ui-icon.vue'

defineOptions({
	options: {
		styleIsolation: 'shared', // 解除样式隔离
	},
})

const emits = defineEmits(['suffix-click', 'prefix-click'])

const props = withDefaults(defineProps<TitleProps>(), {
	size: SizeEnum.md,
	prefixIcon: '',
	suffixIcon: '',
	title: '',
	subTitle: '',
	textColor: '',
	noPadding: false,
})

const classNames = computed(() => {
	const classNames = []
	const { size, noPadding } = props
	if (size) {
		classNames.push(`ui-title-${size}`)
	}
	if (noPadding) {
		classNames.push('no-padding')
	}
	return classNames
})

const handlePrefixClick = e => {
	emits('prefix-click', e)
}

const handleSuffixClick = e => {
	emits('suffix-click', e)
}
</script>
<style lang="scss">
@import url('./ui-title.scss');
</style>
