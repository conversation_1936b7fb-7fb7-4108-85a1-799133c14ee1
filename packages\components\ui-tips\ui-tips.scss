.ui-tips {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	padding: var(--gap-6) 0;
}
.ui-tips-prefix .ui-icon,
.ui-tips-prefix-icon {
	color: var(--light-text-light);
	line-height: 1.2;
}

.ui-tips-prefix {
	margin-right: var(--gap-4);
}

.ui-tips-content {
	flex: 1;
	font-size: var(--font-body-sm);
	font-weight: 400;
	line-height: 1.5;
	color: var(--light-text-light);
	overflow: hidden;
	word-break: break-all;
	white-space: pre-wrap;
}

.ui-tips-info {
	.ui-tips-prefix .ui-icon,
	.ui-tips-prefix-icon,
	.ui-tips-content {
		color: var(--light-text-light);
	}
}

.ui-tips-success {
	.ui-tips-prefix .ui-icon,
	.ui-tips-prefix-icon,
	.ui-tips-content {
		color: var(--light-success-light);
	}
}

.ui-tips-error {
	.ui-tips-prefix .ui-icon,
	.ui-tips-prefix-icon,
	.ui-tips-content {
		color: var(--light-error-light);
	}
}

.ui-tips-warning {
	.ui-tips-prefix .ui-icon,
	.ui-tips-prefix-icon,
	.ui-tips-content {
		color: var(--light-important-dark);
	}
}
