<template>
	<view class="company-form">
		<view class="form-header">
			<text>{{ isEdit ? '编辑抬头' : '新增抬头' }}</text>
		</view>
		
		<view class="form-content">
			<!-- 抬头类型 -->
			<view class="form-item title-type">
				<view class="item-label">
					<text class="required">*</text>
					<text>抬头类型</text>
				</view>
				<view class="radio-group">
					<view class="radio-item" :class="{ active: formData.titleType === 'company' }" @click="updateField('titleType', 'company')">
						<view class="radio-icon">
							<view class="radio-inner" v-if="formData.titleType === 'company'"></view>
						</view>
						<text>企业单位</text>
					</view>
					<view class="radio-item" :class="{ active: formData.titleType === 'personal' }" @click="updateField('titleType', 'personal')">
						<view class="radio-icon">
							<view class="radio-inner" v-if="formData.titleType === 'personal'"></view>
						</view>
						<text>个人/非企业</text>
					</view>
				</view>
			</view>
			
			<!-- 抬头名称 -->
			<view class="form-item">
				<view class="item-label">
					<text class="required">*</text>
					<text>抬头名称</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="必填" 
						:value="formData.companyName" 
						@input="e => updateField('companyName', e.detail.value)"
					/>
				</view>
			</view>
			
			<!-- 公司税号 -->
			<view class="form-item" v-if="formData.titleType === 'company'">
				<view class="item-label">
					<text class="required">*</text>
					<text>公司税号</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="必填" 
						:value="formData.taxNumber" 
						@input="e => updateField('taxNumber', e.detail.value)"
					/>
				</view>
			</view>
			
			<!-- 公司地址 -->
			<view class="form-item">
				<view class="item-label">
					<text>公司地址</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="请输入" 
						:value="formData.companyAddress" 
						@input="e => updateField('companyAddress', e.detail.value)"
					/>
				</view>
			</view>
			
			<!-- 公司电话 -->
			<view class="form-item">
				<view class="item-label">
					<text>公司电话</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="请输入" 
						:value="formData.companyPhone" 
						@input="e => updateField('companyPhone', e.detail.value)"
					/>
				</view>
			</view>
			
			<!-- 公司开户行 -->
			<view class="form-item">
				<view class="item-label">
					<text>公司开户行</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="请输入" 
						:value="formData.bankName" 
						@input="e => updateField('bankName', e.detail.value)"
					/>
				</view>
			</view>
			
			<!-- 开户行账户 -->
			<view class="form-item">
				<view class="item-label">
					<text>开户行账户</text>
				</view>
				<view class="input-wrapper">
					<input 
						type="text" 
						placeholder="请输入" 
						:value="formData.bankAccount" 
						@input="e => updateField('bankAccount', e.detail.value)"
					/>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<UiButton 
				type="normal" 
				size="lg" 
				text="取消" 
				@click="cancel"
			></UiButton>
			<UiButton 
				type="primary" 
				size="lg" 
				text="保存" 
				@click="save"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'

defineOptions({
	name: 'CompanyForm',
})

// 接收从父组件传递的数据
const props = defineProps({
	company: {
		type: Object,
		default: () => ({})
	},
	isEdit: {
		type: Boolean,
		default: false
	}
})

const emit = defineEmits(['save', 'cancel'])

// 表单数据
const formData = reactive({
	titleType: props.company.titleType || 'company',
	companyName: props.company.companyName || '',
	taxNumber: props.company.taxNumber || '',
	companyAddress: props.company.companyAddress || '',
	companyPhone: props.company.companyPhone || '',
	bankName: props.company.bankName || '',
	bankAccount: props.company.bankAccount || ''
})

// 更新表单字段
const updateField = (field, value) => {
	formData[field] = value
}

// 取消
const cancel = () => {
	emit('cancel')
}

// 保存
const save = () => {
	// 表单验证
	if (!formData.companyName) {
		uni.showToast({
			title: '请填写抬头名称',
			icon: 'none'
		})
		return
	}
	
	if (formData.titleType === 'company' && !formData.taxNumber) {
		uni.showToast({
			title: '请填写公司税号',
			icon: 'none'
		})
		return
	}
	
	emit('save', { ...formData })
}
</script>

<style scoped lang="scss">
.company-form {
	background-color: #F5F7FA;
	height: 100vh;
	display: flex;
	flex-direction: column;
	
	.form-header {
		padding: 32rpx;
		background-color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
		color: #171928;
		text-align: center;
		border-bottom: 1px solid #EBEDF0;
	}
	
	.form-content {
		flex: 1;
		background-color: #FFFFFF;
		padding: 0 32rpx;
		
		.form-item {
			padding: 24rpx 0;
			border-bottom: 1px solid #EBEDF0;
			
			.item-label {
				margin-bottom: 16rpx;
				font-size: 28rpx;
				color: #171928;
				
				.required {
					color: #FF4D4F;
					margin-right: 4rpx;
				}
			}
			
			.input-wrapper {
				input, textarea {
					width: 100%;
					height: 72rpx;
					font-size: 28rpx;
					color: #171928;
					background-color: #F5F7FA;
					border-radius: 8rpx;
					padding: 0 24rpx;
				}
				
				textarea {
					height: 160rpx;
					padding: 16rpx 24rpx;
				}
			}
			
			&.title-type {
				.radio-group {
					display: flex;
					
					.radio-item {
						display: flex;
						align-items: center;
						margin-right: 48rpx;
						
						.radio-icon {
							width: 36rpx;
							height: 36rpx;
							border-radius: 50%;
							border: 1px solid #DCDFE6;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 8rpx;
							
							.radio-inner {
								width: 20rpx;
								height: 20rpx;
								border-radius: 50%;
								background-color: #3370FF;
							}
						}
						
						&.active {
							.radio-icon {
								border-color: #3370FF;
							}
						}
					}
				}
			}
		}
	}
	
	.footer-buttons {
		display: flex;
		justify-content: space-between;
		padding: 32rpx;
		background-color: #FFFFFF;
		border-top: 1px solid #EBEDF0;
		
		:deep(.ui-button) {
			width: 45%;
		}
	}
}
</style>