export enum typeEnum {
	check = 'check',
	dot = 'dot',
}

export interface RadioProps {
	/**
	 * @description 形状
	 */
	modelValue?: string | number | boolean
	/**
	 * @description 是否选中
	 */
	checked?: boolean
	/**
	 * @description 单选框的label
	 */
	label?: string | number
	/**
	 * @description 单选框值
	 */
	value?: string | number | boolean
	/**
	 * @description 形状
	 */
	type?: keyof typeof typeEnum
	/**
	 * @description 是否禁用
	 */
	disabled?: boolean
	/**
	 * @description 是否禁用文本内容点击
	 */
	labelDisabled?: boolean
}
