<template>
	<view
		v-if="showPopup"
		class="ui-popup-container"
		:style="uiPopTop"
		:class="[popupstyle, isDesktop ? 'fixforpc-z-index' : '']"
	>
		<view @touchstart="touchstart">
			<UiTransition
				key="1"
				v-if="maskShow"
				name="mask"
				mode-class="fade"
				:styles="maskClass"
				:duration="duration"
				:show="showTrans"
				@click="onTap"
			/>
			<UiTransition
				key="2"
				:mode-class="ani"
				name="content"
				:styles="transClass"
				:duration="duration"
				:show="showTrans"
				@click="onTap"
			>
				<view class="ui-popup-container-wrapper" :style="getStyles" :class="[popupstyle]" @click="clear">
					<slot />
				</view>
			</UiTransition>
		</view>
	</view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, onUnmounted, onActivated, onDeactivated } from 'vue'
import UiTransition from '../ui-transition/ui-transition.vue'
import type { PopupContainerProps } from './types'

defineOptions({
	name: 'UiPopupContainer',
})

const props = withDefaults(defineProps<PopupContainerProps>(), {
	animation: true,
	position: 'center',
	isMaskClick: null,
	backgroundColor: 'none',
	maskShow: false,
	maskBackgroundColor: 'rgba(0, 0, 0, 0.4)',
})

const emit = defineEmits(['change', 'maskClick'])

const showPopup = ref(false)
const showTrans = ref(false)
const popupWidth = ref(0)
const popupHeight = ref(0)
const ani = ref([])
const maskClass = ref({
	position: 'fixed',
	bottom: 0,
	top: 0,
	left: 0,
	right: 0,
	backgroundColor: 'rgba(0, 0, 0, 0.4)',
})
const transClass = ref({
	backgroundColor: 'transparent',
	borderRadius: props.borderRadius || '0',
	position: 'fixed',
	left: 0,
	right: 0,
})
const mkclick = ref(true)
const popupstyle = ref('top')
const duration = ref(300)
const timer = ref(null)
const safeAreaInsets = ref(0)
const clearPropagation = ref(false)

const config = {
	top: 'top',
	bottom: 'bottom',
	center: 'center',
	left: 'left',
	right: 'right',
}

const getStyles = computed(() => {
	const position = popupstyle.value
	if (position === 'left' || position === 'right') {
		return { backgroundColor: bg.value, borderRadius: props.borderRadius }
	}
	return {
		backgroundColor: bg.value,
		borderRadius: props.borderRadius,
	}
})

const isDesktop = computed(() => popupWidth.value >= 500 && popupHeight.value >= 500)

const bg = computed(() => {
	if (props.backgroundColor === '' || props.backgroundColor === 'none') {
		return 'transparent'
	}
	return props.backgroundColor
})

const uiPopTop = computed(() => {
	return { zIndex: Number(props.zIndex) }
})

watch(
	() => props.isMaskClick,
	val => {
		mkclick.value = val
	},
	{ immediate: true },
)

watch(showPopup, show => {
	// #ifdef H5
	document.body.style.overflow = show ? 'hidden' : 'visible'
	// #endif
})

onMounted(() => {
	const fixSize = () => {
		// #ifdef MP-WEIXIN
		const info = uni.getWindowInfo()
		popupWidth.value = info.windowWidth
		popupHeight.value = info.windowHeight + (info.windowTop || 0)
		if (info.safeArea && props.safeArea) {
			safeAreaInsets.value = info.screenHeight - info.safeArea.bottom
		}
		// #endif

		// #ifndef MP-WEIXIN
		const info = uni.getSystemInfoSync()
		popupWidth.value = info.windowWidth
		popupHeight.value = info.windowHeight + (info.windowTop || 0)
		if (info.safeArea && props.safeArea) {
			safeAreaInsets.value = info.safeAreaInsets.bottom
		}
		// #endif
	}
	fixSize()
})

onUnmounted(() => {
	setH5Visible()
})

onActivated(() => {
	setH5Visible(!showPopup.value)
})

onDeactivated(() => {
	setH5Visible(true)
})

mkclick.value = props.isMaskClick !== null ? props.isMaskClick : true
duration.value = props.animation ? 300 : 0
maskClass.value.backgroundColor = props.maskBackgroundColor

const setH5Visible = (visible = true) => {
	// #ifdef H5
	document.body.style.overflow = visible ? 'visible' : 'hidden'
	// #endif
}

const disableMask = () => {
	mkclick.value = false
}

const clear = e => {
	e.stopPropagation()
	clearPropagation.value = true
}

const open = (direction: string) => {
	if (showPopup.value) return
	const innerType = ['top', 'center', 'bottom', 'left', 'right']
	if (!(direction && innerType.includes(direction))) {
		direction = props.position
	}

	if (!config[direction]) {
		console.error('缺少类型：', direction)
		return
	}
	triggerPopup(config[direction])
	emit('change', { show: true, position: direction })
}

const close = () => {
	showTrans.value = false
	emit('change', { show: false, position: props.position })
	clearTimeout(timer.value)
	timer.value = setTimeout(() => {
		showPopup.value = false
	}, 300)
}

const touchstart = () => {
	clearPropagation.value = false
}

const onTap = () => {
	if (clearPropagation.value) {
		clearPropagation.value = false
		return
	}
	emit('maskClick')
	if (!mkclick.value) return
	close()
}

const triggerPopup = (position, typeOnly = false) => {
	switch (position) {
		case 'top':
			popupstyle.value = isDesktop.value ? 'fixforpc-top' : 'top'
			ani.value = ['slide-top']
			transClass.value = {
				position: 'fixed',
				left: 0,
				right: 0,
				backgroundColor: bg.value,
				borderRadius: props.borderRadius || '0',
			}
			break
		case 'bottom':
			popupstyle.value = 'bottom'
			ani.value = ['slide-bottom']
			transClass.value = {
				position: 'fixed',
				left: 0,
				right: 0,
				bottom: 0,
				paddingBottom: `${safeAreaInsets.value}px`,
				backgroundColor: bg.value,
				borderRadius: props.borderRadius || '0',
			}
			break
		case 'center':
			popupstyle.value = 'center'
			ani.value = process.env.VUE_APP_PLATFORM === 'mp-weixin' ? ['fade'] : ['zoom-out', 'fade']
			transClass.value = {
				position: 'fixed',
				display: 'flex',
				flexDirection: 'column',
				bottom: 0,
				left: 0,
				right: 0,
				top: 0,
				justifyContent: 'center',
				alignItems: 'center',
				borderRadius: props.borderRadius || '0',
			}
			break
		case 'left':
			popupstyle.value = 'left'
			ani.value = ['slide-left']
			transClass.value = {
				position: 'fixed',
				left: 0,
				bottom: 0,
				top: 0,
				backgroundColor: bg.value,
				borderRadius: props.borderRadius || '0',
				display: 'flex',
				flexDirection: 'column',
			}
			break
		case 'right':
			popupstyle.value = 'right'
			ani.value = ['slide-right']
			transClass.value = {
				position: 'fixed',
				bottom: 0,
				right: 0,
				top: 0,
				backgroundColor: bg.value,
				borderRadius: props.borderRadius || '0',
				display: 'flex',
				flexDirection: 'column',
			}
			break
		default:
	}

	if (!typeOnly) {
		showPopup.value = true
		showTrans.value = true
	}
}

watch(
	() => props.position,
	position => {
		if (!config[position]) return
		triggerPopup(config[position], true)
	},
	{ immediate: true },
)

watch(
	() => isDesktop.value,
	newVal => {
		if (!config[newVal]) return
		triggerPopup(config[props.position], true)
	},
	{ immediate: true },
)

defineExpose({
	open,
	close,
})
</script>

<style lang="scss">
@use './ui-popup-container.scss';
</style>
