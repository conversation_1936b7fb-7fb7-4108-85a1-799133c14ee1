@font-face {
	font-family: 'number-bold';
	src: url('../../assets/number/TCloudNumber-Bold.ttf') format('truetype');
}

@font-face {
	font-family: 'number-normal';
	src: url('../../assets/number/TCloudNumber-Regular.ttf') format('truetype');
}

@font-face {
	font-family: 'number-light';
	src: url('../../assets/number/TCloudNumber-Light.ttf') format('truetype');
}

.ui-money {
	display: flex;
	align-items: center;
	box-sizing: border-box;
}

.number-bold {
	font-family: 'number-bold' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.number-normal {
	font-family: 'number-normal' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.number-light {
	font-family: 'number-light' !important;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.ui-money-value {
	color: var(--light-list-normal-default-text);
	box-sizing: border-box;
}
.ui-money-unit {
	color: var(--light-list-normal-default-text);
	padding-top: 4px;
	box-sizing: border-box;
	font-weight: 500;
}
.unit-size-lg {
	font-size: var(--font-body-sm);
}
.unit-size-md {
	font-size: var(--font-body-min);
}
.unit-size-sm {
	font-size: var(--font-body-min);
}
.value-size-lg {
	font-size: var(--font-number-lg);
}
.value-size-md {
	font-size: var(--font-number-md);
}
.value-size-sm {
	font-size: var(--font-number-md);
}
.ui-money-sm {
	height: var(--list-sm-height);
}
.ui-money-md {
	height: var(--list-md-height);
}
.ui-money-lg {
	height: var(--list-lg-height);
}
