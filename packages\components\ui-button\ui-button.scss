$button-themes: 'light', 'dark';
$button-types: 'primary', 'link', 'secondary', 'transparent', 'lighter';

@mixin set-button-state($theme, $type, $state) {
	$prefix: --#{$theme}-button;

	border-color: var(#{$prefix}-#{$state}-#{$type}-border, transparent);
	background: var(#{$prefix}-#{$state}-#{$type}-background, transparent);
	color: var(#{$prefix}-#{$state}-#{$type}-text, transparent);
}

@mixin apply-size($size, $fontSize) {
	height: var(--button-#{$size}-height);
	padding: 0 calc(var(--button-#{$size}-padding) - 2rpx);
	border-radius: var(--button-#{$size}-radius);
	font-size: var(--font-body-#{$fontSize});
	line-height: var(--font-body-#{$size});

	.ui-button-icon {
		font-size: var(--icon-#{$size});
		line-height: var(--icon-#{$size});
	}

	&.icon-left {
		.ui-button-text + .ui-button-icon {
			margin-right: var(--button-#{$size}-margin);
		}
	}

	&.icon-right {
		.ui-button-text + .ui-button-icon {
			margin-left: var(--button-#{$size}-margin);
		}
	}
}

@function get-size-value($size, $subtract: 0) {
	@if $size == 'xlg' {
		@return 56rpx - $subtract;
	} @else if $size == 'lg' {
		@return 48rpx - $subtract;
	} @else if $size == 'md' {
		@return 40rpx - $subtract;
	} @else if $size == 'sm' {
		@return 32rpx - $subtract;
	} @else {
		@return null;
	}
}

@mixin set-icon-size($size) {
	font-size: get-size-value($size);

	&.fix {
		font-size: get-size-value($size, 8rpx);
	}
}

.ui-button {
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	width: fit-content;
	border-width: 2rpx;
	border-style: solid;
	white-space: nowrap;
	transition: all 0.3s ease-out;

	&.icon-left {
		flex-direction: row-reverse;
	}

	&::after {
		display: none;
	}

	.ui-button-icon {
		font-weight: 400;
	}
}

.ui-button-xlg {
	@include apply-size('xlg', 'lg');
	font-weight: 400;
}

.ui-button-lg {
	@include apply-size('lg', 'lg');
	font-weight: 400;
}

.ui-button-md {
	@include apply-size('md', 'md');
	font-weight: 400;
}

.ui-button-sm {
	@include apply-size('sm', 'sm');
	font-weight: 400;
}

.ui-button.normal {
	&.light,
	&.dark {
		border-color: var(--light-button-default-normal-border);
		background: var(--light-button-default-normal-background);
		color: var(--light-button-default-normal-text);

		&:active:not(.disabled) {
			background: var(--light-button-press-normal-background);
			border-color: var(--light-button-press-normal-border);
		}

		&.disabled {
			border-color: var(--light-button-disable-normal-border);
			background: var(--light-button-disable-normal-background);
			color: var(--light-button-disable-normal-text);
		}
	}
}

.ui-button {
	@each $theme in $button-themes {
		@each $type in $button-types {
			&.#{$type} {
				&.#{$theme} {
					@include set-button-state($theme, $type, 'default');

					&:active:not(.disabled) {
						@include set-button-state($theme, $type, 'press');
					}

					&.disabled {
						@include set-button-state($theme, $type, 'disable');
					}
				}
			}
		}
	}
}

.ui-button-icon-xlg {
	@include set-icon-size('xlg');
}

.ui-button-icon-lg {
	@include set-icon-size('lg');
}

.ui-button-icon-md {
	@include set-icon-size('md');
}

.ui-button-icon-sm {
	@include set-icon-size('sm');
}
