<template>
	<Layout
		title="标签管理"
		left-text=""
		layout-background="default"
		nav-bar-background="transparent"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<view class="tags-wrap">
				<UiTitle title="我的标签" subTitle=" | 最多添加3个标签"></UiTitle>
				<view class="tags-view">
					<UiTag
						v-for="(tag, index) in tagList"
						:key="index"
						:type="tag.type"
						:size="tag.size"
						icon="icon-close"
						iconColor="#32364D"
						:text="tag.title"
						@click="deleteTag(tag)"
					></UiTag>
				</view>
			</view>
			<view class="add-tag-wrap">
				<UiTitle title="新增标签"></UiTitle>
				<view class="add-tag-bottom">
					<UiInput v-model="newTag" background clearable :disabled="tagList.length >= 3"></UiInput>
					<view class="add-btn-wrap">
						<view class="add-btn">
							<UiButton
								type="secondary"
								width="100%"
								size="xlg"
								style-name="light"
								text="保存"
								:disabled="tagList.length >= 3"
								@click="addClick"
							></UiButton>
						</view>
					</view>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Layout from '@/layout/index.vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import { onLoad } from '@dcloudio/uni-app'
import type { TagInfo } from '../bindingTypings'
import { updateBindRemarkInfoApi } from '@/api/userBind.api'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'BindingListPage',
})

const newTag = ref('')
const tagList = ref([] as TagInfo[])
let copyTagList = [] as TagInfo[]
const type = ['success', 'error', 'otherflow']
let dataId = ''

onLoad((options: any) => {
	const userRemark = options.userRemark
	dataId = options.dataId || ''

	let tagTitle = [] as string[]
	if (userRemark) {
		tagTitle = userRemark.split(',')
	}
	tagTitle.forEach((item, index) => {
		tagList.value.push({
			size: 'md',
			type: type[index],
			title: item,
		})
	})
	copyTagList = tagList.value
})

// 添加标签
const addClick = () => {
	if (tagList.value.length < 3) {
		if (newTag.value) {
			copyTagList.push({
				size: 'md',
				type: type[tagList.value.length],
				title: newTag.value,
			})
			saveTags()
		} else {
			showMsg('请输入标签名称')
		}
	}
}

// 保存标签
const saveTags = () => {
	let remark = copyTagList.map(item => item.title).join(',')
	let params = {
		remark: remark,
		phoneUserBindId: dataId,
	}
	updateBindRemarkInfoApi(params)
		.then(() => {
			tagList.value = copyTagList
			newTag.value = ''
			showMsg('更新成功')
		})
		.catch(() => {})
}
// 删除标签
const deleteTag = (item: TagInfo) => {
	const index = copyTagList.findIndex(tag => tag.title === item.title)
	tagList.value.splice(index, 1)
	saveTags()
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	.tags-wrap {
		width: 100%;
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-padding-sm) 0;
		background-color: #ffffff;
		.tags-view {
			display: flex;
			flex-direction: row;
			align-items: center;
			height: 96rpx;
			gap: var(--tag-lg-margin);
			padding: 0 var(--content-default-padding-lg);
		}
	}
	.add-tag-wrap {
		border-radius: var(--content-default-radius-md);
		background-color: var(--light-fill-blank);
		margin-top: var(--content-default-margin-md);
		.add-tag-bottom {
			padding: 0 var(--content-default-padding-md);
			.add-btn-wrap {
				display: flex;
				flex-direction: row;
				align-items: center;
				height: 144rpx;
				.add-btn {
					width: 100%;
				}
			}
		}
	}
}
</style>
