<template>
	<Layout title="用能分析" scroll>
		<view style="padding: 24rpx">
			<UserInfoCard></UserInfoCard>
			<GcLine1 />
			<GcLine2 />
			<GcLine3 />
			<GcLine4 v-model="list" />
		</view>
	</Layout>
</template>

<script setup lang="ts">
import Layout from '@/layout/index.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import GcLine1 from '@/components/gc-line1/index.vue'
import GcLine2 from '@/components/gc-line2/index.vue'
import GcLine3 from '@/components/gc-line3/index.vue'
import GcLine4 from '@/components/gc-line4/index.vue'

defineOptions({
	name: 'AnalysisUse',
})
const list = [
	{ yValue1: Math.ceil(Math.random() * 20), xValue: '6/1' },
	{ yValue1: Math.ceil(Math.random() * 120), xValue: '6/2' },
	{ yValue1: Math.ceil(Math.random() * 150), xValue: '6/3' },
	{ yValue1: Math.ceil(Math.random() * 200), xValue: '6/4' },
	{ yValue1: Math.ceil(Math.random() * 250), xValue: '6/5' },
	{ yValue1: Math.ceil(Math.random() * 250), xValue: '6/6' },
	{ yValue1: Math.ceil(Math.random() * 250), xValue: '6/7' },
]
</script>
<style scoped lang="scss"></style>
