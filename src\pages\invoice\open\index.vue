<template>
	<Layout title="缴费开票" @left-click="navigateBack" show-nav-bar backgroundImageType="default">
		<view class="page-container">
			<view class="step-wrapper">
				<UiStep :list="list" type="horizontal" :current="current"></UiStep>
			</view>

			<view class="content-wrapper">
				<!-- 根据当前步骤显示对应的组件 -->
				<OrderSelect v-if="current === 1" @next-step="goToEdit" />
				<InvoiceEdit v-else-if="current === 2" @prev-step="goToOrder" @next-step="goToConfirm" />
				<InvoiceConfirm v-else-if="current === 3" @prev-step="goToEdit" @confirm="handleConfirm" />
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UiStep from '@e-cloud/eslink-plus-uniapp/components/ui-step/ui-step.vue'
import Layout from '@/layout/index.vue'
import OrderSelect from './components/OrderSelect.vue'
import InvoiceEdit from './components/InvoiceEdit.vue'
import InvoiceConfirm from './components/InvoiceConfirm.vue'

defineOptions({
	name: 'InvoiceOpen',
})

// 步骤条配置
const list = ['订单选择', '开票填报', '开票确认']
const current = ref(1)

// 导航函数
const navigateBack = () => {
	uni.navigateBack()
}

// 去订单选择页面
const goToOrder = () => {
	current.value = 1
}

// 去开票填报页面
const goToEdit = () => {
	current.value = 2
}

// 去开票确认页面
const goToConfirm = () => {
	current.value = 3
}

// 处理确认开票
const handleConfirm = () => {
	// 处理确认开票逻辑
	uni.navigateTo({
		url: '/pages/invoice/success/index',
	})
}
</script>

<style scoped lang="scss">
.page-container {
	position: relative;
	display: flex;
	flex-direction: column;
	height: calc(100vh - var(--status-bar-height) - 88rpx); /* 减去状态栏和导航栏高度 */
	overflow: hidden;
}

.step-wrapper {
	position: relative;
	z-index: 1;
	padding: 24px 0;
	flex-shrink: 0;
}

.content-wrapper {
	flex: 1;
	position: relative;
	z-index: 1;
	box-sizing: border-box;
	overflow: hidden; /* 防止内容溢出 */
}
</style>
