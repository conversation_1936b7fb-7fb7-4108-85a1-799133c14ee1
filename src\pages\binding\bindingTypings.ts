import type { TagType } from '@e-cloud/eslink-plus-uniapp/components/ui-tag/types'
import type { SizeEnum } from '@e-cloud/eslink-plus-uniapp/shared/enum'

// 绑定的用户操作项
export interface OperationItem {
	title: string
	icon: string
	url: string
	type: string
	use: string
	code: string
}

// 绑定的用户页面顶部信息
export interface BindingUserHeaderInfo {
	title: string
	icon: string
}

// tag 标签信息
export interface TagInfo {
	size: Exclude<keyof typeof SizeEnum, 'xlg'>
	type: TagType
	title: string
}
