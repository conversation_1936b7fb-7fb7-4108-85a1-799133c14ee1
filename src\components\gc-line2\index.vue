<template>
	<div class="gc-line2" id="chart" ref="chartDom"></div>
</template>
<script lang="ts" setup>
import { defineProps, onMounted, ref, watch } from 'vue'
import options from './options.ts'
import * as echarts from 'echarts'

defineOptions({ name: 'GcLine2' })
const props = defineProps({
	legend: {
		type: Array,
		default() {
			return ['2024年', '2025年']
		},
	},
	modelValue: {
		type: Array,
		default() {
			return [
				{ yValue1: Math.ceil(Math.random() * 20), yValue2: Math.ceil(Math.random() * 120), xValue: '1月' },
				{ yValue1: Math.ceil(Math.random() * 120), yValue2: Math.ceil(Math.random() * 120), xValue: '2月' },
				{ yValue1: Math.ceil(Math.random() * 150), yValue2: Math.ceil(Math.random() * 120), xValue: '3月' },
				{ yValue1: Math.ceil(Math.random() * 200), yValue2: Math.ceil(Math.random() * 120), xValue: '4月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: Math.ceil(Math.random() * 120), xValue: '5月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: Math.ceil(Math.random() * 120), xValue: '6月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '7月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '8月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '9月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '10月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '11月' },
				{ yValue1: Math.ceil(Math.random() * 250), yValue2: null, xValue: '12月' },
			]
		},
	},
})
// echart dom
const chartDom = ref<null | HTMLElement>(null)
// echart 实例
let myChart: any = null
const currentValue = ref(props.modelValue)
onMounted(() => {
	init()
})
watch(
	() => props.modelValue,
	val => {
		currentValue.value = val
		init()
	},
)
const init = () => {
	chartDom.value && !myChart && (myChart = echarts.init(chartDom.value))
	myChart.setOption(options(currentValue.value, props))
}
</script>
<style lang="scss" scoped>
.gc-line2 {
	height: 448rpx;
	width: 654rpx;
}
</style>
