<template>
  <view class="custom-popup" v-if="show">
    <view class="popup-mask" @click="maskClick && close()"></view>
    <view class="popup-content" :class="type">
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

defineOptions({
  name: 'CustomPopup'
})

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'center'
  },
  maskClick: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['change', 'close'])

const isOpen = ref(false)

watch(() => props.show, (newVal) => {
  if (newVal) {
    open()
  } else {
    close()
  }
})

const open = (type = props.type) => {
  isOpen.value = true
  emit('change', {
    show: true,
    type
  })
}

const close = () => {
  isOpen.value = false
  emit('change', {
    show: false
  })
  emit('close')
}

defineExpose({
  open,
  close
})
</script>

<style scoped lang="scss">
.custom-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  
  .popup-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
  
  .popup-content {
    position: absolute;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 0 30rpx rgba(0, 0, 0, 0.1);
    
    &.center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      min-width: 600rpx;
    }
    
    &.bottom {
      bottom: 0;
      left: 0;
      right: 0;
      border-radius: 16rpx 16rpx 0 0;
    }
    
    &.top {
      top: 0;
      left: 0;
      right: 0;
      border-radius: 0 0 16rpx 16rpx;
    }
    
    &.left {
      top: 0;
      left: 0;
      bottom: 0;
      border-radius: 0 16rpx 16rpx 0;
    }
    
    &.right {
      top: 0;
      right: 0;
      bottom: 0;
      border-radius: 16rpx 0 0 16rpx;
    }
  }
}
</style>