<template>
	<Layout
		show-tabbar
		title=""
		:currentIndex="2"
		nav-bar-background="transparent"
		backgroundImageType="main"
		left-text=""
		left-icon=""
	>
		<view class="page-content no-padding-wrap">
			<view class="user-info-wrap">
				<UiAvatar size="xlg" :src="userInfo.headImgUrl || '/static/images/mine/header.png'"></UiAvatar>
				<view class="user-info-item">
					<view class="user-info">
						<text class="user-info-name">{{ userInfo.name || '--' }}</text>
						<text class="user-info-phone">{{ userInfo.bindPhone || '--' }}</text>
					</view>
					<UiTag type="success" size="sm" text="已实名"></UiTag>
				</view>
				<view>
					<UiButton
						type="transparent"
						size="md"
						icon="icon-change-icon"
						@click="changeAccountClick"
					></UiButton>
				</view>
			</view>
			<view class="default-user-no-wrap" @click="bindingUserClick">
				<UiTitle title="我的户号" :subTitle="'(' + (bindingUserTotal || '0') + '户)'" :noPadding="true">
					<template #extra>
						<i class="iconfont icon-sysicon"></i>
					</template>
				</UiTitle>
				<UiCell
					v-for="(prop, index) in userProps"
					:key="index"
					:title="defaultUser[prop.value] || defaultUser[prop.value1]"
					size="sm"
					:noPadding="true"
				>
					<template #prefix>
						<image class="user-info-icon" :src="prop.icon"></image>
					</template>
					<template #subTitle v-if="index == 0">
						<view class="default-user-info-item align-center">
							<text class="default-user-line"></text>
							<text class="default-user-name">{{ defaultUser.userName || '--' }}</text>
							<UiTag
								v-if="defaultUser.defaultUser"
								class="default-user-tag"
								type="primary"
								size="min"
								text="默认"
							></UiTag>
						</view>
					</template>
				</UiCell>
			</view>
			<view class="service-record-title">
				<UiTitle title="服务记录" size="lg"></UiTitle>
			</view>
			<view class="service-record-wrap">
				<view class="service-record-item" v-for="item in serviceRecordList" :key="item.id">
					<service-record-item :dataItem="item"></service-record-item>
				</view>
			</view>
			<UiCell title="设置" icon="icon-Lable-icon-L" :arrow="true" @cellClick="settingClick"></UiCell>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiAvatar from '@e-cloud/eslink-plus-uniapp/components/ui-avatar/ui-avatar.vue'
import Layout from '@/layout/index.vue'
import ServiceRecordItem from './components/ServiceRecordItem.vue'
import userNoIcon from '@/static/images/common/user-no-icon.png'
import addressIcon from '@/static/images/common/address-icon.png'
import type { MineUserInfo } from './mineTypings'
import type { UserInfo } from '@/pages/commonTypes'
import { getUserInfoEnApi, getServiceRecordListEnApi } from '@/api/mine.api'
import { queryDefaultUserApi } from '@/api/userBind.api'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'MinePage',
})

const userInfo = ref({} as MineUserInfo)
const defaultUser = ref({} as UserInfo)
const bindingUserTotal = ref(0)
const serviceRecordList = ref([
	{
		id: 1,
		title: '我的账单',
		desc: '账单明细查询',
		icon: 'bill',
	},
	{
		id: 2,
		title: '我的缴费',
		desc: '缴费明细查询',
		icon: 'payIcon',
	},
	{
		id: 3,
		title: '我的用气',
		desc: '用气量查询',
		icon: 'gas',
	},
	{
		id: 4,
		title: '我的发票',
		desc: '发票记录',
		icon: 'invoice',
	},
	// {
	// 	id: 5,
	// 	title: '我的合同',
	// 	desc: '历史合同记录',
	// 	icon: 'contract',
	// },
	{
		id: 6,
		title: '我的办理',
		desc: '燃气业务办理',
		icon: 'business',
	},
	// {
	// 	id: 7,
	// 	title: '我的保险',
	// 	desc: '保障用气安全',
	// 	icon: 'safe',
	// },
])

// 展示的字段
const userProps = ref([
	{
		key: '户号',
		value: 'userNo',
		value1: 'userId',
		icon: userNoIcon,
	},
	{
		key: '地址',
		value: 'userAddress',
		value1: 'addressId',
		icon: addressIcon,
	},
])

onShow(() => {
	getUserInfo()
	queryDefaultUser()
	// getServiceRecordList()
})
// 查询当前用户信息
const getUserInfo = () => {
	getUserInfoEnApi()
		.then(res => {
			if (res) {
				userInfo.value = res
			}
		})
		.catch(err => {
			console.log(err)
		})
}

// 查询绑定用户默认值
const queryDefaultUser = () => {
	queryDefaultUserApi()
		.then(res => {
			if (res) {
				bindingUserTotal.value = res.total
				if (res.userInfo) {
					defaultUser.value = res.userInfo
				}
			}
		})
		.catch(() => {})
}
// 查询服务记录
const getServiceRecordList = () => {
	getServiceRecordListEnApi()
		.then(res => {
			if (res && Array.isArray(res.list)) {
				serviceRecordList.value = res.list
			}
		})
		.catch(err => {
			console.log(err)
		})
}

// 切换账号点击
const changeAccountClick = () => {
	showMsg('该功能暂未开通')
}
// 我的户号模块点击
const bindingUserClick = () => {
	uni.navigateTo({
		url: '/pages/binding/binding-list/index',
	})
}

const settingClick = () => {
	uni.navigateTo({
		url: '/pages/home/<USER>/setting/index',
	})
}
</script>
<style lang="scss" scoped>
.page-content {
	padding: 88rpx 48rpx 76rpx 48rpx;
	.user-info-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		.user-info-item {
			display: flex;
			flex-direction: column;
			justify-content: space-around;
			padding: 16rpx 0 16rpx 24rpx;
			flex: 1;
			.user-info {
				display: flex;
				flex-direction: row;
				align-items: center;
				.user-info-name {
					font-size: var(--font-size-lg);
					font-weight: 600;
				}
				.user-info-phone {
					font-size: var(--font-body-md);
					margin-left: 16rpx;
				}
			}
		}
	}
	.default-user-no-wrap {
		background-color: white;
		margin-top: 48rpx;
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-padding-xs) var(--content-default-padding-md);
		.default-user-info-item {
			display: flex;
			flex-direction: row;
			padding-bottom: 12rpx;
			.default-user-no {
				margin-left: 14rpx;
				color: var(--light-text-title);
				font-size: var(--font-body-md);
				font-weight: 600;
			}
			.default-user-line {
				margin-left: 8rpx;
				background: var(--light-border-darker);
				width: 2rpx;
				height: 20rpx;
			}
			.default-user-name {
				margin-left: 8rpx;
				color: var(--light-form-normal-default-secondary);
				font-size: var(--font-body-md);
			}
			.default-user-tag {
				margin-left: 16rpx;
			}
		}
		.align-center {
			align-items: center;
		}
	}
	.service-record-title {
		margin-top: 24rpx;
	}
	.service-record-wrap {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		margin-bottom: 24rpx;
		.service-record-item {
			width: calc((100% - 24rpx) / 2);
			&:nth-child(2n) {
				margin-left: 24rpx;
				margin-bottom: 24rpx;
			}
		}
	}
}
.user-info-icon {
	width: 32rpx;
	height: 32rpx;
	margin-right: 12rpx;
	margin-top: 6rpx;
}
:deep(.setting-cell-wrap .cell-left .cell-title-sm) {
	color: var(--light-list-normal-default-text);
	font-weight: 400;
}
</style>
