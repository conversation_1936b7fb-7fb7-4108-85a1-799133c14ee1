import * as echarts from 'echarts'

export default (data, props) => {
	const xData = data ? data.map(item => item.xValue) : []
	const seriesData1 = data ? data.map(item => item.yValue1) : []
	const legend = props.legend.map(item => {
		return item.toString()
	})
	return {
		grid: {
			containLabel: true,
			bottom: 0,
			left: 0,
			top: 40,
			right: 0,
		},
		legend: {
			icon: 'rect',
			itemWidth: 16,
			itemHeight: 4,
			itemGap: 8,
			data: legend,
			textStyle: {
				fontSize: 12,
				lineHeight: 17,
				color: '#4F536F',
			},
		},
		xAxis: [
			{
				type: 'category',
				axisTick: {
					show: false,
				},
				axisLine: {
					show: false,
				},
				axisLabel: {
					color: '#5F677D',
					fontSize: 12,
					lineHeight: 16,
				},
				data: xData,
			},
		],
		yAxis: [
			{
				splitNumber: 4,
				type: 'value',
				show: true,
				name: 'm³',
				nameTextStyle: {
					color: '#5F677D',
					fontSize: 12,
				},
				splitLine: {
					lineStyle: {
						type: 'dashed',
						color: 'rgba(0, 0, 26, 0.15)',
					},
				},
				axisLabel: {
					color: '#5F677D',
					fontSize: 14,
					lineHeight: 16,
				},
				axisLine: {
					show: false,
				},
			},
		],
		series: [
			{
				name: props.legend[0],
				type: 'line',
				symbol: 'none',
				smooth: true,
				itemStyle: {
					normal: {
						color: '#8979FF',
						lineStyle: {
							width: 2,
							type: 'solid',
						},
					},
				},
				data: seriesData1,
				areaStyle: {
					color: new echarts.graphic.LinearGradient(
						0,
						0,
						0,
						1,
						[
							{
								offset: 0,
								color: 'rgba(137, 121, 255, 0.30)',
							},
							{
								offset: 1,
								color: 'rgba(137, 121, 255, 0.05)',
							},
						],
						false,
					),
				},
			},
		],
	}
}
