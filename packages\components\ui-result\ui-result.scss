.ui-result {
	width: 654rpx;
	box-sizing: border-box;
}
.ui-result-top {
	padding: var(--content-default-padding-sm) 0;
}
.ui-result-bottom {
	padding: var(--content-default-padding-sm) 0;
	text-align: center;
	display: flex;
	justify-content: center;
}
.ui-result-subtitle {
	color: var(--light-text-title,);
	text-align: center;
	font-size: 64rpx;
	font-style: normal;
	font-weight: 400;
	line-height: 80rpx;
	padding-bottom: var(--content-default-padding-sm);
}
.ui-result-description {
	color: var(--light-text-secondary);
	text-align: center;
	font-size: var(--font-body-md);
	font-style: normal;
	font-weight: 400;
	line-height: 44rpx;
}
.ui-result-image {
	display: flex;
	justify-content: center;
	padding-bottom: var(--content-default-padding-sm);
	image {
		width: 112rpx;
		height: 112rpx;
	}
}
.ui-result-title {
	color: var(--light-text-title);
	text-align: center;
	font-size: var(--font-title-md);
	font-style: normal;
	font-weight: 500;
	line-height: 48rpx;
}
