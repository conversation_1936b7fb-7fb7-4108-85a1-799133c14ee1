<template>
	<view class="invoice-confirm">
		<!-- 开票确认内容 -->
		<view class="confirm-content">
			<view class="confirm-title">开票确认</view>
			<view class="confirm-info">
				<!-- 这里是开票确认信息内容 -->
				<view class="info-item">
					<view class="info-label">确认内容示例</view>
					<view class="info-value">这里是开票确认的信息内容</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<UiButton 
				type="normal" 
				size="lg" 
				text="上一步" 
				@click="prevStep"
			></UiButton>
			<UiButton 
				type="primary" 
				size="lg" 
				text="确认开票" 
				@click="confirmInvoice"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'

defineOptions({
	name: 'InvoiceConfirm',
})

const emit = defineEmits(['prev-step', 'confirm'])

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 确认开票
const confirmInvoice = () => {
	emit('confirm')
}
</script>

<style scoped lang="scss">
.invoice-confirm {
	padding: 24rpx 0;
	
	.confirm-content {
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		
		.confirm-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #171928;
			margin-bottom: 24rpx;
		}
		
		.confirm-info {
			.info-item {
				margin-bottom: 24rpx;
				
				.info-label {
					font-size: 28rpx;
					color: #5F677D;
					margin-bottom: 12rpx;
				}
				
				.info-value {
					font-size: 30rpx;
					color: #171928;
				}
			}
		}
	}
	
	.footer-buttons {
		display: flex;
		justify-content: space-between;
		padding: 0 32rpx;
		margin-top: 48rpx;
		
		:deep(.ui-button) {
			width: 45%;
		}
	}
}
</style>