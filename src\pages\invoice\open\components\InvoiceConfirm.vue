<template>
	<view class="invoice-confirm">
		<!-- 内容区域（可滚动） -->
		<scroll-view class="content-scroll" scroll-y="true">
			<!-- 订单信息摘要 -->
			<view class="order-summary">
				<view class="summary-title">订单信息</view>
				<view class="summary-content">
					<UiCell title="已选订单" :value="`${selectedCount}个`" size="sm" />
					<UiCell title="合计金额" :value="`￥${totalAmount}`" size="sm">
						<template #value>
							<text class="amount-value">￥{{ totalAmount }}</text>
						</template>
					</UiCell>
				</view>
			</view>
			
			<!-- 开票确认内容 -->
			<view class="confirm-content">
				<view class="confirm-title">开票信息确认</view>
				<view class="confirm-info">
					<view class="info-item">
						<view class="info-label">发票类型</view>
						<view class="info-value">{{ invoiceTypeText }}</view>
					</view>
					<view class="info-item">
						<view class="info-label">发票抬头</view>
						<view class="info-value">{{ titleTypeText }}</view>
					</view>
					
					<!-- 企业信息（如果是企业抬头） -->
					<template v-if="invoiceEditData.titleType === 'company'">
						<view class="info-item">
							<view class="info-label">企业名称</view>
							<view class="info-value">{{ invoiceEditData.formData.companyName }}</view>
						</view>
						<view class="info-item">
							<view class="info-label">统一社会信用代码</view>
							<view class="info-value">{{ invoiceEditData.formData.taxNumber }}</view>
						</view>
						<view v-if="invoiceEditData.formData.companyAddress" class="info-item">
							<view class="info-label">企业地址</view>
							<view class="info-value">{{ invoiceEditData.formData.companyAddress }}</view>
						</view>
						<view v-if="invoiceEditData.formData.companyPhone" class="info-item">
							<view class="info-label">企业电话</view>
							<view class="info-value">{{ invoiceEditData.formData.companyPhone }}</view>
						</view>
						<view v-if="invoiceEditData.formData.bankName" class="info-item">
							<view class="info-label">开户银行</view>
							<view class="info-value">{{ invoiceEditData.formData.bankName }}</view>
						</view>
						<view v-if="invoiceEditData.formData.bankAccount" class="info-item">
							<view class="info-label">银行账号</view>
							<view class="info-value">{{ invoiceEditData.formData.bankAccount }}</view>
						</view>
					</template>
				</view>
			</view>
			
			<!-- 底部空白区域，为底部按钮留出空间 -->
			<view class="footer-placeholder"></view>
		</scroll-view>
		
		<!-- 底部按钮（固定在底部） -->
		<view class="footer-buttons">
			<UiButton 
				type="normal" 
				size="lg" 
				text="上一步" 
				@click="prevStep"
			></UiButton>
			<UiButton 
				type="primary" 
				size="lg" 
				text="确认开票" 
				@click="confirmInvoice"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'

defineOptions({
	name: 'InvoiceConfirm',
})

// 接收从父组件传递的数据
const props = defineProps({
	confirmData: {
		type: Object,
		required: true
	},
	invoiceEditData: {
		type: Object,
		required: true
	}
})

const emit = defineEmits(['update:confirmData', 'prev-step', 'confirm'])

// 计算选中的订单数量
const selectedCount = computed(() => props.invoiceEditData.selectedOrders?.length || 0)

// 计算总金额
const totalAmount = computed(() => props.invoiceEditData.totalAmount || '0.00')

// 获取发票类型文本
const invoiceTypeText = computed(() => {
	return props.invoiceEditData.invoiceType === 'electronic' ? '增值税电子普通发票' : '增值税普通发票'
})

// 获取发票抬头文本
const titleTypeText = computed(() => {
	return props.invoiceEditData.titleType === 'personal' ? '个人' : '企业'
})

// 上一步
const prevStep = () => {
	emit('prev-step')
}

// 确认开票
const confirmInvoice = () => {
	emit('confirm')
}
</script>

<style scoped lang="scss">
.invoice-confirm {
	display: flex;
	flex-direction: column;
	height: 100vh;
	position: relative;
	
	.content-scroll {
		flex: 1;
		overflow: hidden;
		padding: 24rpx 32rpx;
	}
	
	.order-summary {
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		
		.summary-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #171928;
			margin-bottom: 24rpx;
		}
		
		.summary-content {
			:deep(.ui-cell) {
				padding: 16rpx 0;
				
				.ui-cell__title {
					font-size: 28rpx;
					color: #5F677D;
				}
				
				.ui-cell__value {
					font-size: 28rpx;
					color: #171928;
				}
			}
			
			.amount-value {
				font-size: 28rpx;
				color: #FF6B2C;
				font-family: TCloudNumber, sans-serif;
				font-weight: 500;
			}
		}
	}
	
	.confirm-content {
		background-color: #FFFFFF;
		border-radius: 20rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		
		.confirm-title {
			font-size: 32rpx;
			font-weight: 500;
			color: #171928;
			margin-bottom: 24rpx;
		}
		
		.confirm-info {
			.info-item {
				margin-bottom: 24rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.info-label {
					font-size: 28rpx;
					color: #5F677D;
					margin-bottom: 12rpx;
				}
				
				.info-value {
					font-size: 30rpx;
					color: #171928;
				}
			}
		}
	}
	
	.footer-placeholder {
		height: 160rpx; /* 为底部按钮留出空间 */
	}
	
	.footer-buttons {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		display: flex;
		justify-content: space-between;
		padding: 24rpx 64rpx;
		background-color: #FFFFFF;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
		z-index: 99;
		
		:deep(.ui-button) {
			width: 45%;
		}
	}
}
</style>
