.ui-popup-dialog {
	box-sizing: border-box;
	border-radius: var(--content-default-radius-xlg);
	background-color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	overflow: hidden;
}

.ui-dialog-title {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 128rpx;
}

.ui-dialog-title-text {
	font-weight: 600;
	font-size: var(--font-title-lg);
	color: var(--light-text-title);
}

.ui-dialog-content {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	padding: var(--content-default-padding-md) 32rpx var(--content-default-padding-xlg) 32rpx;
}

.ui-dialog-content-text {
	font-weight: 400;
	font-size: var(--font-body-lg);
	color: var(--light-text-primary);
}

.ui-dialog-button-group {
	box-sizing: border-box;
	display: flex;
	padding: var(--content-default-padding-lg);
	border-top: 2rpx solid var(--light-border-lighter);
	background: var(--light-fill-blank);
}

.botton-wrapper {
	flex: 1;

	& + & {
		margin-left: 16rpx;
	}
}
