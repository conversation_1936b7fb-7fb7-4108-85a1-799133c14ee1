// #ifdef APP-NVUE
const nvueAnimation = uni.requireNativePlugin('animation')
// #endif

// Type definitions
interface AnimationOptions {
	duration?: number
	timingFunction?: string
	delay?: number
	transformOrigin?: string
}

interface AnimationStep {
	styles: Record<string, any>
	config: AnimationOptions
}

type AnimateType =
	| 'matrix'
	| 'matrix3d'
	| 'rotate'
	| 'rotate3d'
	| 'rotateX'
	| 'rotateY'
	| 'rotateZ'
	| 'scale'
	| 'scale3d'
	| 'scaleX'
	| 'scaleY'
	| 'scaleZ'
	| 'skew'
	| 'skewX'
	| 'skewY'
	| 'translate'
	| 'translate3d'
	| 'translateX'
	| 'translateY'
	| 'translateZ'
	| 'opacity'
	| 'backgroundColor'
	| 'width'
	| 'height'
	| 'left'
	| 'right'
	| 'top'
	| 'bottom'

class MPAnimation {
	private options: AnimationOptions
	private animation: any
	private currentStepAnimates: Record<number, AnimationStep> = {}
	private next: number = 0
	private $: any
	private isEnd: boolean = false

	constructor(options: AnimationOptions, _this: any) {
		this.options = {
			...{ duration: 300, timingFunction: 'linear', delay: 0, transformOrigin: '50% 50% 0' },
			...options,
		}
		this.animation = uni.createAnimation(this.options)
		this.$ = _this
	}

	private _nvuePushAnimates(type: AnimateType, args: any[]) {
		let aniObj = this.currentStepAnimates[this.next]
		let styles: Record<string, any> = {}

		if (!aniObj) {
			styles = {
				styles: {},
				config: {},
			}
		} else {
			styles = aniObj
		}

		const unit = type === 'rotate' ? 'deg' : ''

		if (animateTypes1.includes(type)) {
			if (!styles.styles.transform) {
				styles.styles.transform = ''
			}
			styles.styles.transform += `${type}(${args.join(',')}${unit}) `
		} else {
			styles.styles[type] = `${args}`
		}

		this.currentStepAnimates[this.next] = styles
	}

	private _animateRun(styles: Record<string, any>, config: AnimationOptions): Promise<void> {
		const ref = this.$.$refs['ani']?.ref
		if (!ref) return Promise.resolve()

		return new Promise((resolve, reject) => {
			nvueAnimation.transition(
				ref,
				{
					...styles,
					...config,
				},
				() => resolve(),
			)
		})
	}

	private _nvueNextAnimate(animates: Record<number, AnimationStep>, step: number = 0, fn?: () => void) {
		const obj = animates[step]
		if (obj) {
			const { styles, config } = obj
			this._animateRun(styles, config).then(() => {
				step += 1
				this._nvueNextAnimate(animates, step, fn)
			})
		} else {
			this.currentStepAnimates = {}
			if (typeof fn === 'function') fn()
			this.isEnd = true
		}
	}

	step(config: AnimationOptions = {}) {
		// #ifndef APP-NVUE
		this.animation.step(config)
		// #endif
		// #ifdef APP-NVUE
		this.currentStepAnimates[this.next].config = { ...this.options, ...config }
		this.currentStepAnimates[this.next].styles.transformOrigin =
			this.currentStepAnimates[this.next].config.transformOrigin
		this.next++
		// #endif
		return this
	}

	run(fn?: () => void) {
		// #ifndef APP-NVUE
		this.$.animationData = this.animation.export()
		this.$.timer = setTimeout(
			() => {
				if (fn) fn()
			},
			this.$.durationTime || this.options.duration || 300,
		)
		// #endif
		// #ifdef APP-NVUE
		this.isEnd = false
		const ref = this.$.$refs['ani']?.ref
		if (!ref) return
		this._nvueNextAnimate(this.currentStepAnimates, 0, fn)
		this.next = 0
		// #endif
	}
}

const animateTypes1: AnimateType[] = [
	'matrix',
	'matrix3d',
	'rotate',
	'rotate3d',
	'rotateX',
	'rotateY',
	'rotateZ',
	'scale',
	'scale3d',
	'scaleX',
	'scaleY',
	'scaleZ',
	'skew',
	'skewX',
	'skewY',
	'translate',
	'translate3d',
	'translateX',
	'translateY',
	'translateZ',
]

const animateTypes2: AnimateType[] = ['opacity', 'backgroundColor']
const animateTypes3: AnimateType[] = ['width', 'height', 'left', 'right', 'top', 'bottom']

;[...animateTypes1, ...animateTypes2, ...animateTypes3].forEach(type => {
	MPAnimation.prototype[type] = function (...args: any[]) {
		// #ifndef APP-NVUE
		this.animation[type](...args)
		// #endif
		// #ifdef APP-NVUE
		this._nvuePushAnimates(type, args)
		// #endif
		return this
	}
})

export function createAnimation(option: AnimationOptions, _this: any) {
	if (!_this) return
	clearTimeout(_this.timer)
	return new MPAnimation(option, _this)
}
