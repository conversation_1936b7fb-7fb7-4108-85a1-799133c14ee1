<template>
	<Layout
		title="缴费记录"
		left-text=""
		nav-bar-background="transparent"
		backgroundImageType="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<UserInfoCard
				ref="userInfoCard"
				pageType="payment"
				:userNo="currentUser.userNo"
				:addressId="currentUser.addressId"
				@changeUser="changeUserClick"
			></UserInfoCard>
			<view v-for="(item, index) in recordList" :key="index">
				<view class="group-title">{{ item.title }}</view>
				<view>
					<RecordListItem
						v-for="(recordItem, index) in item.list"
						:key="index"
						:recordItem="recordItem"
					></RecordListItem>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import RecordListItem from './components/RecordListItem.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import type { UserInfo } from '@/pages/commonTypes'
import type { RecordItem } from '../payRecordTypings'

defineOptions({
	name: 'RecordList',
})

const currentUser = ref({} as UserInfo) // 当前默认用户
const recordList = ref([
	{
		title: '2025年6月',
		list: [
			{
				payType: 'alipay',
				payTypeDesc: '支付宝',
				orderNo: '10250032561001',
				feeTypeDesc: '气费',
				fee: 102.4,
			},
			{
				payType: 'wechat',
				payTypeDesc: '微信',
				orderNo: '10250032561001',
				feeTypeDesc: '气费/垃圾回收费/维修费保养费',
				fee: 102.4,
			},
		] as RecordItem[],
	},
	{
		title: '2025年5月',
		list: [
			{
				payType: 'alipay',
				payTypeDesc: '支付宝',
				orderNo: '10250032561001',
				status: 'paying',
				statusDesc: '支付中',
				feeTypeDesc: '气费',
				fee: 102.4,
			},
		] as RecordItem[],
	},
])

onMounted(() => {})

const changeUserClick = () => {}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.group-title {
	height: var(--list-sm-height);
	line-height: var(--list-sm-height);
	margin-top: 24rpx;
	margin-bottom: 16rpx;
	padding: 0px var(--content-default-padding-lg);
	font-size: var(--font-body-md);
	color: var(--light-text-secondary);
}
</style>
