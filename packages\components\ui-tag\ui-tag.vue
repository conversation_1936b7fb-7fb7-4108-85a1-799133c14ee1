<template>
	<view class="ui-tag" :class="[type, `ui-tag-${size}`]" @click="handleClick">
		<slot>
			<text v-if="!!text" class="ui-tag-text">{{ text }}</text>
			<i v-if="!!icon" :style="iconStyle" class="ui-tag-icon" :class="[iconfontClass, icon]"></i>
		</slot>
	</view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { TagType } from './types'
import type { TagProps } from './types'
import { SizeEnum } from '../../shared/enum'

defineOptions({
	name: 'UiTag',
})

const props = withDefaults(defineProps<TagProps>(), {
	type: TagType.default,
	size: SizeEnum.md,
	text: '',
	icon: '',
	iconfontClass: 'iconfont',
	iconColor: '',
})

const emits = defineEmits(['click'])

const handleClick = () => {
	emits('click')
}

const iconStyle = computed(() => {
	return {
		color: props.iconColor,
	}
})
</script>
<style scoped lang="scss">
@use './ui-tag.scss';
</style>
