<template>
	<Layout
		title="服务网点"
		left-text=""
		nav-bar-background="transparent"
		layout-background="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<UiSearch
				v-model="searchValue"
				placeholder="查找服务网点"
				@change="getServiceHallInfoList"
				@search="getServiceHallInfoList"
				@clear="getServiceHallInfoList"
			></UiSearch>
			<view class="list-wrap">
				<AddressItem
					type="page"
					v-for="address in serviceHallList"
					:key="address.id"
					:dataItem="address"
				></AddressItem>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import AddressItem from '../components/address-item/index.vue'
import UiSearch from '@e-cloud/eslink-plus-uniapp/components/ui-search/ui-search.vue'
import type { ServiceHallInfo } from '../homeTypings'
import { getServiceHallList } from '@/api/home.api'

defineOptions({
	name: 'ServiceHall',
})

// 附近营业厅
const serviceHallList = ref([] as ServiceHallInfo[])
const location = ref()
const searchValue = ref('')

// 页面加载完成
onLoad((options: any) => {
	// 页面类型
	if (options.location) {
		location.value = JSON.parse(options.location)
	}
	getServiceHallInfoList()
})

// 获取附近营业厅列表
const getServiceHallInfoList = () => {
	const params = {
		coordinates: location.value.longitude + ',' + location.value.latitude,
		name: searchValue.value,
	}
	getServiceHallList(params)
		.then(res => {
			serviceHallList.value = res
		})
		.catch(() => {})
}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.list-wrap {
	margin-top: var(--content-default-margin-md);
}
</style>
