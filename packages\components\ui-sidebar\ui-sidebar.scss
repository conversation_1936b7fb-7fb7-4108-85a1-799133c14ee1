.ui-sidebar {
	background: var(--light-bg-base);
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}
.ui-sidebar-item {
	display: flex;
	box-sizing: border-box;
	height: 56px;
	padding: 0 var(--content-default-padding-md);
	align-items: center;
	background: var(--light-fill-blank);
	color: var(--light-text-title);
	font-size: var(--font-body-lg);
	position: relative;
	&.active {
		background: var(--light-bg-base);
		color: var(--light-text-title, #171928);
		font-weight: 500;
	}
}
.ui-sidebar-item-tip {
	position: absolute;
	left: 0;
	top: 50%;
	width: 12rpx;
	height: 32rpx;
	transform: translateY(-50%);
	border-radius: 4px;
	background: var(--light-primary-default);
}
.ui-sidebar-item-next {
	border-radius: 0 var(--content-default-radius-md) 0 0;
}
.ui-sidebar-item-prev {
	border-radius: 0 0 var(--content-default-radius-md) 0;
}
