<template>
	<view class="company-selector">
		<view class="search-bar">
			<view class="search-input">
				<text class="search-icon">🔍</text>
				<input 
					type="text" 
					v-model="searchKeyword" 
					placeholder="搜索公司名称" 
					@input="handleSearch"
				/>
				<text v-if="searchKeyword" class="clear-icon" @click="clearSearch">✕</text>
			</view>
		</view>
		
		<view class="company-list">
			<view class="list-header">
				<text>历史抬头</text>
				<text class="manage-btn" @click="manageCompanies">管理</text>
			</view>
			
			<view class="list-content">
				<view 
					class="company-item" 
					v-for="(item, index) in filteredCompanies" 
					:key="index"
					@click="selectCompany(item)"
				>
					<view class="company-info">
						<view class="company-name">{{ item.companyName }}</view>
						<view class="company-tax" v-if="item.taxNumber">税号：{{ item.taxNumber }}</view>
					</view>
					<text class="forward-icon">›</text>
				</view>
				
				<view class="empty-tip" v-if="filteredCompanies.length === 0">
					<text>暂无匹配的公司</text>
				</view>
			</view>
		</view>
		
		<view class="add-company" @click="addNewCompany">
			<text class="plus-icon">+</text>
			<text>新增抬头</text>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

defineOptions({
	name: 'CompanySelector',
})

// 定义事件
const emit = defineEmits(['select', 'add', 'manage', 'close'])

// 搜索关键词
const searchKeyword = ref('')

// 模拟公司数据
const companyList = ref([
	{
		companyName: '腾讯科技（深圳）有限公司',
		taxNumber: '91440300708461136T',
		companyAddress: '深圳市南山区高新区科技中一路腾讯大厦',
		companyPhone: '0755-********',
		bankName: '招商银行深圳分行',
		bankAccount: '***************'
	},
	{
		companyName: '阿里巴巴（中国）有限公司',
		taxNumber: '91330100799655058B',
		companyAddress: '杭州市余杭区五常街道文一西路969号',
		companyPhone: '0571-********',
		bankName: '中国工商银行杭州分行',
		bankAccount: '1202021409900123456'
	},
	{
		companyName: '百度在线网络技术（北京）有限公司',
		taxNumber: '91110000802100433B',
		companyAddress: '北京市海淀区上地十街10号',
		companyPhone: '010-********',
		bankName: '中国建设银行北京分行',
		bankAccount: '1100190919004621234'
	}
])

// 过滤后的公司列表
const filteredCompanies = computed(() => {
	if (!searchKeyword.value) {
		return companyList.value
	}
	return companyList.value.filter(company => 
		company.companyName.includes(searchKeyword.value) || 
		(company.taxNumber && company.taxNumber.includes(searchKeyword.value))
	)
})

// 处理搜索
const handleSearch = () => {
	// 实际应用中可能需要调用API进行搜索
}

// 清除搜索
const clearSearch = () => {
	searchKeyword.value = ''
}

// 选择公司
const selectCompany = (company) => {
	emit('select', company)
}

// 添加新公司
const addNewCompany = () => {
	emit('add')
}

// 管理公司
const manageCompanies = () => {
	emit('manage')
}
</script>

<style scoped lang="scss">
.company-selector {
	background-color: #F5F7FA;
	height: 100vh;
	display: flex;
	flex-direction: column;
	
	.search-bar {
		padding: 20rpx 32rpx;
		background-color: #FFFFFF;
		
		.search-input {
			display: flex;
			align-items: center;
			background-color: #F5F7FA;
			border-radius: 36rpx;
			padding: 12rpx 24rpx;
			
			.search-icon {
				margin-right: 12rpx;
				font-size: 28rpx;
				color: #999;
			}
			
			.clear-icon {
				font-size: 28rpx;
				color: #999;
				padding: 0 10rpx;
			}
			
			input {
				flex: 1;
				height: 60rpx;
				font-size: 28rpx;
			}
		}
	}
	
	.company-list {
		flex: 1;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		
		.list-header {
			display: flex;
			justify-content: space-between;
			padding: 24rpx 32rpx;
			border-bottom: 1px solid #EBEDF0;
			font-size: 28rpx;
			color: #171928;
			
			.manage-btn {
				color: #3370FF;
			}
		}
		
		.list-content {
			.company-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 24rpx 32rpx;
				border-bottom: 1px solid #EBEDF0;
				
				.company-info {
					flex: 1;
					
					.company-name {
						font-size: 28rpx;
						color: #171928;
						margin-bottom: 8rpx;
					}
					
					.company-tax {
						font-size: 24rpx;
						color: #5F677D;
					}
				}
				
				.forward-icon {
					font-size: 36rpx;
					color: #999;
					font-weight: 300;
				}
			}
			
			.empty-tip {
				padding: 60rpx 0;
				text-align: center;
				color: #999;
				font-size: 28rpx;
			}
		}
	}
	
	.add-company {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 32rpx;
		background-color: #FFFFFF;
		margin-top: 20rpx;
		color: #3370FF;
		font-size: 28rpx;
		
		.plus-icon {
			margin-right: 8rpx;
			font-size: 32rpx;
			font-weight: bold;
		}
	}
}
</style>