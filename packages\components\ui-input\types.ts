import { SizeEnum } from '../../shared/enum'

export enum InputType {
	text = 'text',
	textarea = 'textarea',
	number = 'number',
	digit = 'digit',
	tel = 'tel',
}

export interface InputProps {
	/**
	 * @description 绑定值
	 */
	modelValue?: string | number
	/**
	 * @description 类型
	 */
	type?: keyof typeof InputType
	/**
	 * @description 是否带背景
	 */
	background?: boolean
	/**
	 * @description 布局方向
	 */
	vertical?: boolean
	/**
	 * @description 布局方向
	 */
	size?: keyof typeof SizeEnum
	/**
	 * @description label
	 */
	label?: string
	/**
	 * @description label宽度
	 */
	labelWidth?: string
	/**
	 * @description 提示文字
	 */
	placeholder?: string
	/**
	 * @description 是否禁用
	 */
	disabled?: boolean
	/**
	 * @description 是否必填
	 */
	required?: boolean
	/**
	 * @description 是否可清空
	 */
	clearable?: boolean
	/**
	 * @description 是否显示底部边框
	 */
	bottomLine?: boolean
}
