<template>
	<Layout
		title="缴费详情"
		left-text=""
		nav-bar-background="transparent"
		backgroundImageType="main"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<view class="header-view">
				<view class="pay-type-wrap">
					<image class="pay-type-icon" src="@/static/images/pay-record/wechat-icon.png"></image>
					<view class="pay-type-des">微信</view>
				</view>

				<view class="pay-money-wrap">
					<view class="pay-money-unit">￥</view>
					<UiNumber weight="normal" :value="300" valueSize="80rpx" unitSize="0" gap="0" unit=""></UiNumber>
				</view>
			</view>
			<view class="pay-detail-wrap">
				<UiTitle :title="'缴费信息' || '--'" size="md"></UiTitle>
				<UiCell v-for="(item, index) in dataList" :key="index" :title="item.title" :value="item.value"></UiCell>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiNumber from '@e-cloud/eslink-plus-uniapp/components/ui-number/ui-number.vue'

defineOptions({
	name: 'RecordList',
})

const dataList = ref([
	{
		title: '户名',
		value: '王超',
	},
	{
		title: '户号',
		value: '251515020',
	},
	{
		title: '地址',
		value: '杭州市滨江区建设大道亲亲家园17-2-1201',
	},
	{
		title: '账单',
		value: '账单到期+账单ID1',
	},
	{
		title: '表号',
		value: 'JK025151005',
	},
	{
		title: '项目',
		value: '-',
	},
	{
		title: '名称',
		value: '气费',
	},
	{
		title: '支付流水号',
		value: '20250531220915668873',
	},
	{
		title: '时间',
		value: '2025-05-31  12:20:09',
	},
])

onMounted(() => {})

const changeUserClick = () => {}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.header-view {
	margin-top: 16rpx;
	margin-bottom: 24rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	.pay-type-wrap {
		padding: var(--content-default-padding-sm) 0;
		gap: var(--content-default-padding-sm);
		display: flex;
		flex-direction: column;
		align-items: center;
		.pay-type-icon {
			width: 112rpx;
			height: 112rpx;
		}
		.pay-type-des {
			height: 48rpx;
			line-height: 48rpx;
			font-size: var(--font-title-md);
			color: var(--light-text-title);
		}
	}
	.pay-money-wrap {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: var(--content-default-padding-sm) 0;
		.pay-money-unit {
			font-size: var(--font-title-lg);
			color: var(--light-form-normal-default-text);
			font-weight: 700;
			margin-top: 20rpx;
		}
	}
}
.pay-detail-wrap {
	padding: var(--content-default-padding-sm) 0;
	border-radius: var(--content-default-radius-md);
	background: var(--light-fill-blank);
	:deep(.setting-cell-wrap .cell-left .cell-subTitle-md) {
		color: var(--light-form-normal-default-text-light);
		font-size: var(--font-body-md);
	}
}
</style>
