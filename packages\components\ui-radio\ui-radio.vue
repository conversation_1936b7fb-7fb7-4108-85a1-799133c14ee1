<template>
	<view class="ui-radio" :class="chooseClass">
		<view
			class="ui-radio-icon"
			:class="[`ui-radio-icon-${type}`, currentValue === value ? 'checked' : '', disabled ? 'disabled' : '']"
			@click="handleIconClick"
		>
			<view v-if="type === typeEnum.dot" class="ui-radio-icon-inner"></view>
			<i
				v-else-if="type === typeEnum.check"
				class="ui-radio-check-icon-inner iconfont-uniapp icon-uniapp-check-line"
			></i>
		</view>
		<view class="ui-radio-text" @click="handleTextClick">
			<slot>{{ label }}</slot>
		</view>
	</view>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue'
import { isBoolean, isEmpty, isNumber, isString } from '../../utils/validate'
import { typeEnum } from './types'
import type { RadioProps } from './types'

defineOptions({
	name: 'UiRadio',
})

const props = withDefaults(defineProps<RadioProps>(), {
	modelValue: undefined,
	type: typeEnum.check,
	disabled: false,
	labelDisabled: false,
	label: '',
	value: true,
	checked: undefined,
})

const currentValue = ref<string | number | boolean>(props.modelValue)

watch(
	() => [props.modelValue, props.checked],
	() => {
		currentValue.value = isEmpty(props.checked) ? props.modelValue : props.checked
	},
	{ immediate: true },
)

const emits = defineEmits(['update:modelValue', 'change'])

const handleIconClick = () => {
	if (props.disabled) {
		return
	}
	handleClick()
}

const handleTextClick = () => {
	if (props.disabled) {
		return
	}
	handleClick()
}

const handleClick = () => {
	const newValue = updateCurrentValue()
	const boolValue = isBoolean(newValue) || isNumber(newValue) || isString(newValue)
	if (isEmpty(props.checked)) {
		currentValue.value = newValue
	}

	emits('update:modelValue', boolValue)
	emits('change', boolValue)
}

const updateCurrentValue = () => {
	return currentValue.value === props.value ? undefined : props.value
}
const chooseClass = () => {
	const { disabled, value } = props
	return [currentValue === value ? 'checked' : '', disabled ? 'disabled' : ''].filter(item => item !== '')
}
</script>
<style scoped lang="scss">
@use './ui-radio.scss';
</style>
