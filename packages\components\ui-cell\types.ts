import { SizeEnum } from '../../shared/enum'
import { TagType } from '../ui-tag/types'
export interface CellProps {
	id?: number
	icon?: string
	title?: string
	arrow?: boolean
	size?: keyof typeof SizeEnum
	value?: string | number
	valuePrefix?: string
	valueType: 'text' | 'number' | 'money'
	valueSuffix?: string
	subTitle?: string
	tagValue?: string
	tagType?: keyof typeof TagType
	noPadding?: boolean
	underline?: boolean
	background?: boolean
}
