<template>
    <svg :style="{ width, height }">
        <use :xlink:href="symbolId" :fill="color"></use>
    </svg>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
    name: {
        type: String,
        required: true,
    },
    prefix: {
        type: String,
        default: '#icon-',
    },
    color: {
        type: String,
        default: '#000',
    },
    width: {
        type: String,
        default: '16px',
    },
    height: {
        type: String,
        default: '16px',
    },
})

const symbolId = computed(() => `${props.prefix}${props.name}`)
</script>