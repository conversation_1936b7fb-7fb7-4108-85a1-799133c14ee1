import request from '@/core/request'

// ---- 用户绑定信息接口 ----

/**
 * 获取绑定用户信息(加密)
 * @returns 用户信息
 */
export function getBindUserListApi(data?: any): Promise<any> {
	return request({
		url: '/utility/userBind/getBindUserInfo',
		data,
		errorMsg: '查询绑定用户列表失败',
	})
}

/**
 * 查询默认用户
 * @returns 查询结果
 */
export function queryDefaultUserApi(): Promise<any> {
	return request({
		url: '/utility/user/getDefaultUser',
		data: {},
		errorMsg: '查询默认用户失败',
	})
}

/**
 * 设置默认用户
 * @returns 设置结果
 */
export function setDefaultUserApi(id: string): Promise<any> {
	return request({
		url: '/utility/user/setDefaultUser/' + id,
		data: {},
		method: 'GET',
		errorMsg: '设置默认用户失败',
	})
}

/**
 * 更新用户关系
 * @returns 更新结果
 */
export function updateBindRemarkInfoApi(data?: any): Promise<any> {
	return request({
		url: '/utility/userBind/updateBindRemarkInfo',
		data,
		errorMsg: '保存失败',
	})
}

/**
 * 刷新用户信息
 * @returns 刷新结果
 */
export function refreshBindUserInfoApi(data?: any): Promise<any> {
	return request({
		url: '/utility/user/refreshBindUserInfo',
		data,
		errorMsg: '刷新失败',
	})
}

/**
 * 用户解绑
 * @param userTag 用户标识 ID或编号
 * @returns 解绑结果
 */
export function unUserBindApi(userId: string): Promise<any> {
	return request({
		url: `/utility/user/unUserBind/${userId}`,
		method: 'GET',
		data: {},
		errorMsg: '解绑失败',
	})
}
/**
 * 绑定用户
 * @returns 绑定结果
 */
export function bindingUserInfoApi(data?: any): Promise<any> {
	return request({
		url: '/utility/userBind/bindUser',
		data,
		errorMsg: '绑定失败',
	})
}

/**
 * 查询用户信息
 * @param userNo 用户号/手机号
 * @param noType 编号类型 如 userNo
 * @param userName 用户名称
 * @returns 用户信息
 */
export function getUserInfoApi(params: any): Promise<any> {
	return request({
		url: '/utility/userBind/publicUtilitiesUserInfoQuery',
		data: params,
		errorMsg: '查询用户失败',
	})
}

// =====================以下接口暂未用到=============================

/**
 * 发送短信
 * @param phones 手机号
 * @returns 发送结果
 */
export function sendSMS(phones: string): Promise<any> {
	return request({
		url: '/utility/userBind/check/sendSMS',
		data: {
			phones,
		},
	})
}

/**
 * 校验短信验证码
 * @param phones 手机号
 * @param code 短信验证码
 * @returns 校验结果
 */
export function checkSMSCode(phones: string, code: string): Promise<any> {
	return request({
		url: '/utility/userBind/check/SMSCode',
		data: {
			phones,
			code,
		},
	})
}

/**
 * 获取渠道标识
 * @returns 渠道标识
 */
export function getChannelCode(): Promise<any> {
	return request({
		url: '/utility/userBind/getChannelCode',
		data: {},
	})
}

/**
 * 校验是否跳转电话绑定页面
 * @returns 是否需要跳转电话绑定页面
 */
export function checkPhone(): Promise<any> {
	return request({
		url: '/utility/userBind/checkPhone',
		data: {},
	})
}
