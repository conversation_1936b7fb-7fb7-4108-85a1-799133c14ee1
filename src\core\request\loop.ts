import User from '../user'
import Task from './task'

export default class {
	static loopTime = 100 // 定时刷新 单位 ms
	static whiteUrl = ['/channel/login', '/sms/sendCode', '/sms/codeAuth']
	static loopPool: { [key: string]: Task } = {}
	static waitPool: Array<Task> = []
	static loading = false
	static currentPool: number = 0
	static loopTimer: any = null

	static startInterval() {
		if (this.loopTimer != null) return
		this.loopTimer = setInterval(() => {
			Object.keys(this.loopPool).forEach(key => {
				const isWaitting = this.loopPool[key].status === Task.STATUS_WAITTING
				const isWhiteUrl = this.whiteUrl.indexOf(this.loopPool[key].params.url) !== -1
				if (isWaitting && (User.requestReady || isWhiteUrl)) {
					this.push2Wait(this.loopPool[key])
					delete this.loopPool[key]
				}
			})
		}, this.loopTime)
	}
	static pushOne(task: Task): void {
		const id = Math.random().toString(36).replace('0.', '')
		this.loopPool[id] = task
		this.startInterval()
		console.log(this.loopPool)
	}

	static push2Wait(task: Task) {
		this.waitPool.push(task)
		task.status = Task.STATUS_READY
		if (!this.loading) {
			this.run()
		}
	}
	static run() {
		this.loading = true
		if (this.waitPool.length) {
			const task = this.waitPool.shift() as Task
			this.currentPool++
			task.execut()
				.then(res => {
					task.res = res
					task.success = true
				})
				.catch(err => {
					task.res = err
					task.success = false
				})
				.finally(() => {
					task.status = Task.STATUS_FINISH
					this.currentPool--
					this.run()
				})
		} else {
			this.stop()
		}
	}

	static stopLoop() {
		// 所有等待的都结束了，结束循环
	}

	static stop() {
		this.loading = false
	}
}
