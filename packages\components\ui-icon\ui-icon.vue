<template>
	<text class="ui-icon iconfont-uniapp" :style="{color: textColor}" :class="classNames" @click="handleClick"></text>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useMpOptions } from '../../hooks/useMpOptions'
import type { IconProps } from './types'
import { SizeEnum } from '../../shared/enum'

defineOptions({
	name: 'uiIcon',
	...useMpOptions(),
})
const props = withDefaults(defineProps<IconProps>(), {
	name: '',
	size: SizeEnum.md,
	customClass: '',
	textColor: '',
})
const emits = defineEmits(['click'])

const classNames = computed(() => {
	let classNames = []
	const { name, size, customClass } = props

	if (name) {
		classNames.push(`icon-uniapp-${name}`)
	}

	if (size) {
		classNames.push(`ui-icon-${size}`)
	}

	if (customClass) {
		classNames.push(customClass)
	}

	return classNames
})

const handleClick = () => {
	emits('click')
}
</script>
<style lang="scss">
@import url('./ui-icon.scss');
</style>
