<template>
	<view class="ui-number" :class="{ 'align-center': textValue }">
		<text class="ui-number-value" :style="valueStyle" :class="chooseClass(weight)">{{ _value }}</text>
		<text class="ui-number-unit" :style="{ fontSize: unitSize }">{{ unit }}</text>
	</view>
</template>

<script setup lang="ts">
import { watch, onMounted, ref, computed } from 'vue'
import type { NumberProps } from './types'
import { familyEnum } from './types'

defineOptions({
	name: 'UiNumber',
})

const props = withDefaults(defineProps<NumberProps>(), {
	unit: '',
	weight: familyEnum.bold,
	decimal: 2,
})
const textValue = ref(false) // value是字符串，则unit上下居中
const _value = ref('')
const valueStyle = computed(() => {
	return {
		fontSize: props.valueSize,
		lineHeight: `calc(${props.valueSize} + 8rpx)`,
		marginRight: props.gap,
	}
})

onMounted(() => {
	_value.value = dealNumber(props.value, props.decimal)
})

watch(
	() => props.value,
	(newValue: number | string) => {
		_value.value = dealNumber(newValue, props.decimal)
	},
)

const chooseClass = (weight: keyof typeof familyEnum) => {
	return [`number-${weight}`]
}

const dealNumber = (value: number | string, decimal: number) => {
	if (value || value === 0) {
		const numValue = Number(value)
		if (isNaN(numValue)) {
			textValue.value = true;
			return value
		}
		textValue.value = false;
		if (decimal < 0) {
			return numValue
		}
		console.log('numValue', numValue.toFixed(decimal))
		return numValue.toFixed(decimal)
	} else {
		textValue.value = true;
		return '--'
	}
	
}
</script>
<style scoped lang="scss">
@use './ui-number.scss';
</style>
