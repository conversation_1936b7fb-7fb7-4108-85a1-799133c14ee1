<template>
	<view class="order-select-container">
		<!-- 订单选择页面内容 -->
		<scroll-view
			class="order-select-scroll"
			scroll-y="true"
			@scroll="handleScroll"
			:refresher-enabled="true"
			:refresher-triggered="refresherTriggered"
			@refresherrefresh="handleRefresh"
			@scrolltolower="handleScrollToLower"
		>
			<!-- 用户信息卡片 -->
			<UserInfoCard
				ref="userInfoCard"
				pageType="payment"
				:userNo="currentUser.userNo"
				:addressId="currentUser.addressId"
				@changeUser="changeUserClick"
			></UserInfoCard>

			<!-- 订单筛选 -->
			<view class="filter-container filter-container--fixed">
				<view class="tab-container">
					<UiTabs
						:list="orderTabs"
						v-model="activeOrderTab"
						@change="handleOrderTabChange"
						type="fill-dark"
						size="sm"
					></UiTabs>
				</view>
				<view class="filter-dropdown">
					<UiDropdown v-model="selectedFilter" :options="filterOptions" size="sm"></UiDropdown>
				</view>
			</view>

			<!-- 订单列表 -->
			<view class="order-list">
				<view v-for="(group, groupIndex) in filteredOrders" :key="groupIndex" class="month-group">
					<view class="month-title">{{ group.month }}</view>
					<view class="order-items">
						<view
							v-for="(item, itemIndex) in group.items"
							:key="itemIndex"
							class="order-item"
							@click="toggleOrderSelection(group.month, itemIndex)"
						>
							<view class="order-content">
								<view class="order-date">{{ item.date }}</view>
								<view class="order-type-amount">
									<view class="order-type">{{ item.type }}</view>
									<view class="order-amount">
										<text class="order-amount-symbol">￥</text>
										<text class="order-amount-value">{{ item.amount }}</text>
									</view>
								</view>
							</view>
							<view class="order-radio">
								<UiRadio
									:checked="item.selected"
									:name="item.id"
									@change="(val: boolean) => handleOrderCheckboxChange(group.month, itemIndex, val)"
								></UiRadio>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部空白区域，为底部结算栏留出空间 -->
			<view class="footer-placeholder"></view>
		</scroll-view>
	</view>

	<!-- 底部结算栏 -->
	<view class="footer-bar">
		<view class="total-info">
			<view class="total-label">合计:</view>
			<view class="total-amount">
				<text class="total-symbol">￥</text>
				<text class="total-value">{{ totalAmount }}</text>
			</view>
			<view class="selected-count">已选{{ selectedCount }}个订单</view>
		</view>
		<view class="submit-btn">
			<UiButton
				type="primary"
				size="lg"
				text="去开票"
				:disabled="selectedCount === 0"
				@click="goToEdit"
			></UiButton>
		</view>
	</view>

	<!-- 帮助按钮 -->
	<view class="help-btn">
		<UiIcon name="question-fill" size="sm"></UiIcon>
		<text class="help-text">帮助</text>
	</view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiIcon from '@e-cloud/eslink-plus-uniapp/components/ui-icon/ui-icon.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiDropdown from '@e-cloud/eslink-plus-uniapp/components/ui-dropdown/ui-dropdown.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'

defineOptions({
	name: 'OrderSelect',
})

const emit = defineEmits(['next-step'])

// 滚动相关
const refresherTriggered = ref(false)

// 用户信息
const currentUser = ref({
	userName: '王超',
	userNo: '1252007',
	addressId: '杭州市滨江区建设大道钱江路128号****17-1-1201',
})

// 订单类型标签
const orderTabs = [
	{ label: '可开票订单', value: 'available' },
	{ label: '其他订单', value: 'other' },
]
const activeOrderTab = ref('available')

// 筛选选项
const filterOptions = [
	{ label: '订单筛选', value: 'all' },
	{ label: '2025年4月', value: '2025年4月' },
	{ label: '2025年3月', value: '2025年3月' },
	{ label: '2025年2月', value: '2025年2月' },
]
const selectedFilter = ref('all')

// 订单数据
const orderData = ref([
	{
		month: '2025年4月',
		items: [
			{
				date: '2025/04/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202504251',
			},
			{
				date: '2025/04/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202504252',
			},
			{
				date: '2025/04/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202504253',
			},
		],
	},
	{
		month: '2025年3月',
		items: [
			{
				date: '2025/03/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202503251',
			},
			{
				date: '2025/03/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202503252',
			},
			{
				date: '2025/03/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202503253',
			},
		],
	},
	{
		month: '2025年2月',
		items: [
			{
				date: '2025/02/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202502251',
			},
			{
				date: '2025/02/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202502252',
			},
			{
				date: '2025/02/25',
				type: '气费',
				amount: '102.40',
				selected: false,
				id: '202502253',
			},
		],
	},
])

// 当前选中的订单ID
const selectedOrderId = ref('')

// 处理用户选中
const changeUserClick = () => {}

// 根据筛选条件过滤订单
const filteredOrders = computed(() => {
	if (selectedFilter.value === 'all') {
		return orderData.value
	} else {
		return orderData.value.filter(group => group.month === selectedFilter.value)
	}
})

// 计算选中的订单数量和总金额
const selectedCount = computed(() => {
	let count = 0
	orderData.value.forEach(group => {
		group.items.forEach(item => {
			if (item.selected) count++
		})
	})
	return count
})

const totalAmount = computed(() => {
	let total = 0
	orderData.value.forEach(group => {
		group.items.forEach(item => {
			if (item.selected) {
				total += parseFloat(item.amount)
			}
		})
	})
	return total.toFixed(2)
})

// 处理订单标签切换
const handleOrderTabChange = (tab: string) => {
	activeOrderTab.value = tab
	// 切换标签时清除所有选中状态
	orderData.value.forEach(group => {
		group.items.forEach(item => {
			item.selected = false
		})
	})
}

// 切换订单选中状态
const toggleOrderSelection = (month: string, index: number) => {
	const groupIndex = orderData.value.findIndex(g => g.month === month)
	if (groupIndex !== -1) {
		const item = orderData.value[groupIndex].items[index]
		const newSelectedState = !item.selected

		// 如果是选中状态，先清除所有选中
		if (newSelectedState) {
			// 清除所有选中状态
			orderData.value.forEach(group => {
				group.items.forEach(item => {
					item.selected = false
				})
			})

			// 设置当前项为选中
			item.selected = true
			selectedOrderId.value = item.id
		} else {
			// 取消选中
			item.selected = false
			selectedOrderId.value = ''
		}
	}
}

// 处理订单单选按钮变更
const handleOrderCheckboxChange = (month: string, index: number, value: boolean) => {
	if (value) {
		// 清除所有选中状态
		orderData.value.forEach(group => {
			group.items.forEach(item => {
				item.selected = false
			})
		})

		// 设置当前项为选中
		const groupIndex = orderData.value.findIndex(g => g.month === month)
		if (groupIndex !== -1) {
			const item = orderData.value[groupIndex].items[index]
			item.selected = true
			selectedOrderId.value = item.id
		}
	} else {
		// 取消选中
		const groupIndex = orderData.value.findIndex(g => g.month === month)
		if (groupIndex !== -1) {
			orderData.value[groupIndex].items[index].selected = false
			selectedOrderId.value = ''
		}
	}
}

// 处理滚动事件
const handleScroll = () => {
	// 可以在这里处理其他滚动相关逻辑
}

// 处理下拉刷新
const handleRefresh = () => {
	// 模拟刷新操作
	setTimeout(() => {
		refresherTriggered.value = false
	}, 1000)
}

// 处理滚动到底部
const handleScrollToLower = () => {
	// 可以在这里加载更多数据
	console.log('滚动到底部')
}

// 导航函数
const goToHistory = () => {
	uni.navigateTo({
		url: '/pages/invoice/history/index',
	})
}

const goToEdit = () => {
	emit('next-step')
}
</script>

<style scoped lang="scss">
.order-select-container {
	position: relative;
	height: 100%;
	width: 100%;
	overflow: hidden;
}

.order-select-scroll {
	height: calc(
		100vh - var(--status-bar-height) - 88rpx - 120rpx - 128rpx
	); /* 减去状态栏、导航栏、步骤条和底部结算栏高度 */
	box-sizing: border-box;
	position: relative;
}

.user-info-card {
	margin-top: 24rpx;
	margin-bottom: 24rpx;

	.user-info-bg {
		background: linear-gradient(180deg, #273667 0%, #9cb4e0 100%);
		border-radius: 20rpx;
		position: relative;
		overflow: hidden;

		&::after {
			content: '';
			position: absolute;
			right: 0;
			top: 0;
			width: 104rpx;
			height: 104rpx;
			background-image: url('@/static/images/common/user-info-bg-icon.png');
			background-size: contain;
			background-repeat: no-repeat;
			z-index: 1;
		}
	}

	.user-info-header {
		display: flex;
		align-items: center;
		height: 112rpx;
		padding: 0 32rpx;
		position: relative;
		z-index: 2;

		.user-name {
			font-size: 38rpx;
			font-weight: 500;
			color: #ffffff;
		}

		.user-arrow {
			margin-left: 16rpx;
			display: flex;
			align-items: center;
		}

		.history-btn {
			margin-left: auto;
		}
	}

	.user-info-content {
		background-color: #ffffff;
		padding: 32rpx;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;

		.user-info-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.user-info-icon {
				margin-right: 20rpx;
				margin-top: 8rpx;
			}

			.user-info-text {
				font-size: 30rpx;
				color: #171928;
				line-height: 1.5;
			}
		}
	}
}

.filter-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 24rpx 0;
	padding: 0 32rpx;

	&--fixed {
		position: sticky;
		top: 0;
		z-index: 100;
		padding: 24rpx 32rpx;
		margin: 0;
		background-color: #f5f6fa; /* 添加背景色，防止内容透过 */
		border-bottom: 1px solid #eaedf7; /* 添加底部边框，增强视觉分离 */
	}
}

.order-list {
	margin-top: 0; /* 移除上边距，避免重复间距 */
	padding: 24rpx 32rpx 0 32rpx; /* 调整padding，顶部添加间距 */

	.month-group {
		margin-bottom: 24rpx;

		.month-title {
			font-size: 30rpx;
			color: #5f677d;
			padding: 10rpx 0;
			margin-bottom: 16rpx;
		}

		.order-items {
			.order-item {
				background-color: #ffffff;
				border-radius: 20rpx;
				padding: 24rpx 32rpx;
				margin-bottom: 24rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;

				.order-content {
					flex: 1;

					.order-date {
						font-size: 30rpx;
						color: #5f677d;
						margin-bottom: 20rpx;
					}

					.order-type-amount {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.order-type {
							font-size: 30rpx;
							font-weight: 500;
							color: #32364d;
						}

						.order-amount {
							display: flex;
							align-items: center;

							.order-amount-symbol {
								font-size: 20rpx;
								font-weight: 500;
								color: #171928;
								margin-top: 10rpx;
								margin-right: 2rpx;
							}

							.order-amount-value {
								font-size: 32rpx;
								color: #171928;
								font-family: TCloudNumber, sans-serif;
							}
						}
					}
				}

				.order-radio {
					margin-left: 20rpx;
				}
			}
		}
	}
}

.footer-placeholder {
	height: 160rpx; /* 为底部按钮留出空间 */
}

.footer-bar {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 128rpx;
	background-color: #ffffff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 32rpx;
	border-top: 1px solid #eaedf7;
	box-sizing: border-box;
	z-index: 99;

	.total-info {
		.total-label {
			font-size: 24rpx;
			color: #939bb1;
		}

		.total-amount {
			display: flex;
			align-items: center;

			.total-symbol {
				font-size: 24rpx;
				color: #ff6b2c;
				font-weight: 500;
			}

			.total-value {
				font-size: 52rpx;
				color: #ff6b2c;
				font-family: TCloudNumber, sans-serif;
			}
		}

		.selected-count {
			font-size: 24rpx;
			color: #5f677d;
		}
	}

	.submit-btn {
		width: 182rpx;
	}
}

.help-btn {
	position: fixed;
	right: 64rpx;
	bottom: 160rpx;
	width: 112rpx;
	height: 112rpx;
	background-color: #ffffff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-shadow:
		0px 8px 8px -4px rgba(24, 39, 75, 0.04),
		0px 4px 8px -4px rgba(24, 39, 75, 0.06);
	z-index: 99;

	.help-text {
		font-size: 24rpx;
		color: #5f677d;
		margin-top: 4rpx;
	}
}
</style>
