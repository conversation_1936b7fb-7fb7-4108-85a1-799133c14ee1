<template>
	<view class="order-select-container">
		<!-- 用户信息卡片 - 移出滚动区域 -->
		<view class="user-info-wrapper" :style="{ height: userInfoHeight + 'rpx', overflow: 'hidden' }">
			<UserInfoCard
				ref="userInfoCard"
				pageType="payment"
				:userNo="currentUser.userNo"
				:addressId="currentUser.addressId"
				@changeUser="changeUserClick"
			></UserInfoCard>
		</view>

		<!-- 订单筛选 - 移出滚动区域 -->
		<view class="filter-container">
			<view class="tab-container">
				<UiTabs
					:list="orderTabs"
					:modelValue="props.orderData.activeOrderTab"
					@update:modelValue="handleOrderTabChange"
					type="fillDark"
					size="sm"
				></UiTabs>
			</view>
			<view class="filter-dropdown">
				<UiDropdown :modelValue="props.orderData.selectedFilter" @update:modelValue="handleFilterChange" :options="filterOptions" size="sm"></UiDropdown>
			</view>
		</view>

		<!-- 订单列表滚动区域 -->
		<scroll-view
			class="order-select-scroll"
			scroll-y="true"
			@scroll="handleScroll"
			@scrolltolower="handleScrollToLower"
		>
			<!-- 订单列表 -->
			<view class="order-list">
				<view v-for="(group, groupIndex) in filteredOrders" :key="groupIndex" class="month-group">
					<view class="month-title">{{ group.month }}</view>
					<view class="order-items">
						<view
							v-for="(item, itemIndex) in group.items"
							:key="itemIndex"
							class="order-item"
							:class="{ 
								'selected': item.selected && props.orderData.activeOrderTab === 'available', 
								'not-selectable': props.orderData.activeOrderTab !== 'available' 
							}"
							@click="props.orderData.activeOrderTab === 'available' ? toggleOrderSelection(group.month, itemIndex) : null"
						>
							<view class="order-content">
								<view class="order-date">{{ item.date }}</view>
								<view class="order-type-amount">
									<view class="order-type">{{ item.type }}</view>
									<view class="order-amount">
										<text class="order-amount-symbol">￥</text>
										<text class="order-amount-value">{{ item.amount }}</text>
									</view>
								</view>
							</view>
							<view class="order-checkbox" @click.stop v-if="props.orderData.activeOrderTab === 'available'">
								<UiRadio
									:checked="item.selected"
									:name="item.id"
									@change="(val: boolean) => handleOrderCheckboxChange(group.month, itemIndex, val)"
								></UiRadio>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部空白区域，为底部结算栏留出空间，仅在可开票订单标签下显示 -->
			<view class="footer-placeholder" v-if="props.orderData.activeOrderTab === 'available'"></view>
		</scroll-view>
	</view>

	<!-- 底部结算栏 - 仅在可开票订单标签下显示 -->
	<view class="footer-bar" v-if="props.orderData.activeOrderTab === 'available'">
		<view class="total-info">
			<view class="total-label">合计:</view>
			<view class="total-amount">
				<text class="total-symbol">￥</text>
				<text class="total-value">{{ totalAmount }}</text>
			</view>
			<view class="selected-count">已选{{ selectedCount }}个订单</view>
		</view>
		<view class="submit-btn">
			<UiButton
				type="primary"
				size="lg"
				text="去开票"
				:disabled="selectedCount === 0"
				@click="goToEdit"
			></UiButton>
		</view>
	</view>

	<!-- 帮助按钮 -->
	<view class="help-btn">
		<view class="help-icon">
			<image class="help-icon-image" src="/static/images/invoice/file-list-fill.png" mode="aspectFit"></image>
		</view>
		<text class="help-text">帮助</text>
	</view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiDropdown from '@e-cloud/eslink-plus-uniapp/components/ui-dropdown/ui-dropdown.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'

defineOptions({
	name: 'OrderSelect',
})

// 接收从父组件传递的数据
const props = defineProps({
	orderData: {
		type: Object,
		required: true
	}
})

const emit = defineEmits(['update:orderData', 'next-step'])

// 滚动相关
const userInfoCardRef = ref(null)
const userInfoHeight = ref(300) // 用户信息卡片的初始高度，根据实际情况调整
const userInfoMaxHeight = ref(300) // 用户信息卡片的最大高度
const scrollThreshold = 120 // 滚动阈值，单位rpx

// 用户信息
const currentUser = ref({
	userName: '王超',
	userNo: '1252007',
	addressId: '杭州市滨江区建设大道钱江路128号****17-1-1201',
})

// 订单类型标签
const orderTabs = [
	{ label: '可开票订单', value: 'available' },
	{ label: '其他订单', value: 'other' },
]

// 筛选选项
const filterOptions = [
	{ label: '订单筛选', value: 'all' },
	{ label: '2025年4月', value: '2025年4月' },
	{ label: '2025年3月', value: '2025年3月' },
	{ label: '2025年2月', value: '2025年2月' },
]

// 上一次滚动位置
const lastScrollTop = ref(0)

// 处理用户选中
const changeUserClick = () => {}

// 当前显示的订单数据源
const currentOrderData = computed(() => {
	return props.orderData.activeOrderTab === 'available' 
		? props.orderData.availableOrderData 
		: props.orderData.otherOrderData
})

// 根据筛选条件过滤订单
const filteredOrders = computed(() => {
	if (props.orderData.selectedFilter === 'all') {
		return currentOrderData.value
	} else {
		return currentOrderData.value.filter(group => group.month === props.orderData.selectedFilter)
	}
})

// 计算选中的订单数量
const selectedCount = computed(() => {
	let count = 0
	props.orderData.availableOrderData.forEach(group => {
		group.items.forEach(item => {
			if (item.selected) count++
		})
	})
	return count
})

// 计算总金额
const totalAmount = computed(() => {
	return props.orderData.totalAmount
})

// 处理订单标签切换
const handleOrderTabChange = (tab: string) => {
	// 更新父组件中的数据
	const newOrderData = {
		...props.orderData,
		activeOrderTab: tab,
		// 重置筛选选项
		selectedFilter: 'all'
	}
	
	// 如果切换到其他订单，暂存选中状态但不清空
	if (tab !== 'available') {
		// 不清空selectedOrders和totalAmount，只是暂时不显示
	} else {
		// 切换回可开票订单时，重新计算总金额
		let total = 0
		if (newOrderData.selectedOrders && newOrderData.selectedOrders.length > 0) {
			newOrderData.selectedOrders.forEach(item => {
				total += parseFloat(item.amount)
			})
			newOrderData.totalAmount = total.toFixed(2)
		}
	}
	
	emit('update:orderData', newOrderData)
}

// 处理筛选选项变更
const handleFilterChange = (value: string) => {
	emit('update:orderData', {
		...props.orderData,
		selectedFilter: value
	})
}

// 切换订单选中状态
const toggleOrderSelection = (month: string, index: number) => {
	// 在"其它订单"标签下不允许选择订单
	if (props.orderData.activeOrderTab !== 'available') return
	
	const groupIndex = props.orderData.availableOrderData.findIndex(g => g.month === month)
	if (groupIndex !== -1) {
		// 创建数据的深拷贝，以避免直接修改 props
		const newAvailableOrderData = JSON.parse(JSON.stringify(props.orderData.availableOrderData))
		const item = newAvailableOrderData[groupIndex].items[index]
		const newSelectedState = !item.selected
		
		// 更新选中状态
		item.selected = newSelectedState
		
		// 更新选中的完整订单数据
		let selectedOrders = props.orderData.selectedOrders || []
		
		if (newSelectedState) {
			// 添加到选中列表
			selectedOrders.push({
				...item,
				month: month
			})
		} else {
			// 从选中列表中移除
			selectedOrders = selectedOrders.filter(order => order.id !== item.id)
		}
		
		// 计算总金额
		let total = 0
		selectedOrders.forEach(item => {
			total += parseFloat(item.amount)
		})
		
		// 更新父组件中的数据
		emit('update:orderData', {
			...props.orderData,
			availableOrderData: newAvailableOrderData,
			selectedOrders: selectedOrders,
			totalAmount: total.toFixed(2)
		})
	}
}

// 处理订单复选框变更
const handleOrderCheckboxChange = (month: string, index: number, value: boolean) => {
	if (props.orderData.activeOrderTab !== 'available') return
	
	const groupIndex = props.orderData.availableOrderData.findIndex(g => g.month === month)
	if (groupIndex !== -1) {
		// 创建数据的深拷贝，以避免直接修改 props
		const newAvailableOrderData = JSON.parse(JSON.stringify(props.orderData.availableOrderData))
		const item = newAvailableOrderData[groupIndex].items[index]
		
		// 更新选中状态
		item.selected = value
		
		// 更新选中的完整订单数据
		let selectedOrders = props.orderData.selectedOrders || []
		
		if (value) {
			// 添加到选中列表
			selectedOrders.push({
				...item,
				month: month
			})
		} else {
			// 从选中列表中移除
			selectedOrders = selectedOrders.filter(order => order.id !== item.id)
		}
		
		// 计算总金额
		let total = 0
		selectedOrders.forEach(item => {
			total += parseFloat(item.amount)
		})
		
		// 更新父组件中的数据
		emit('update:orderData', {
			...props.orderData,
			availableOrderData: newAvailableOrderData,
			selectedOrders: selectedOrders,
			totalAmount: total.toFixed(2)
		})
	}
}


// 处理滚动事件
const handleScroll = (e: any) => {
	const scrollTop = e.detail.scrollTop
	const isScrollingDown = scrollTop > lastScrollTop.value
	lastScrollTop.value = scrollTop
	
	// 计算用户信息卡片高度
	if (scrollTop <= scrollThreshold) {
		// 当滚动距离小于阈值时，逐渐显示用户信息卡片
		const ratio = (scrollThreshold - scrollTop) / scrollThreshold
		userInfoHeight.value = userInfoMaxHeight.value * ratio
	} else if (isScrollingDown) {
		// 向上滚动超过阈值，隐藏用户信息卡片
		userInfoHeight.value = 0
	}
}

// 处理滚动到底部
const handleScrollToLower = () => {
	// 可以在这里加载更多数据
	console.log('滚动到底部')
}

const goToEdit = () => {
	// 直接使用已保存的选中订单数据
	emit('next-step', {
		selectedOrders: props.orderData.selectedOrders,
		totalAmount: props.orderData.totalAmount
	})
}

// 获取用户信息卡片高度
const getUserInfoCardHeight = () => {
	nextTick(() => {
		const query = uni.createSelectorQuery()
		query
			.select('.user-info-wrapper .user-info-card')
			.boundingClientRect(data => {
				if (data) {
					userInfoMaxHeight.value = data.height
					userInfoHeight.value = userInfoMaxHeight.value
				}
			})
			.exec()
	})
}

// 组件挂载时获取用户信息卡片高度
onMounted(() => {
	// 初始时完整展示用户信息卡片
	userInfoHeight.value = 300 // 设置一个初始值，确保卡片可见
	// 延迟获取实际高度，确保组件已完全渲染
	setTimeout(() => {
		getUserInfoCardHeight()
	}, 100)
})
</script>

<style scoped lang="scss">
.order-select-container {
	position: relative;
	height: 100%;
	width: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.user-info-wrapper {
	transition: height 0.3s ease;
	margin-bottom: 24rpx;
}

.filter-container {
	display: flex;
	justify-content: space-between;
	align-items: center;
	z-index: 10;
	margin-bottom: 24rpx; /* 确保与用户信息卡片有间距 */
}

.order-select-scroll {
	flex: 1;
	height: 0; /* 让flex布局控制高度 */
	box-sizing: border-box; /* 确保与筛选栏有间距 */
}

.user-info-card {
	margin-bottom: 0;

	.user-info-bg {
		background: linear-gradient(180deg, #273667 0%, #9cb4e0 100%);
		border-radius: 20rpx;
		position: relative;
		overflow: hidden;

		&::after {
			content: '';
			position: absolute;
			right: 0;
			top: 0;
			width: 104rpx;
			height: 104rpx;
			background-image: url('@/static/images/common/user-info-bg-icon.png');
			background-size: contain;
			background-repeat: no-repeat;
			z-index: 1;
		}
	}

	.user-info-header {
		display: flex;
		align-items: center;
		height: 112rpx;
		padding: 0 32rpx;
		position: relative;
		z-index: 2;

		.user-name {
			font-size: 38rpx;
			font-weight: 500;
			color: #ffffff;
		}

		.user-arrow {
			margin-left: 16rpx;
			display: flex;
			align-items: center;
		}

		.history-btn {
			margin-left: auto;
		}
	}

	.user-info-content {
		background-color: #ffffff;
		padding: 32rpx;
		border-top-left-radius: 20rpx;
		border-top-right-radius: 20rpx;

		.user-info-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 16rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.user-info-icon {
				margin-right: 20rpx;
				margin-top: 8rpx;
			}

			.user-info-text {
				font-size: 30rpx;
				color: #171928;
				line-height: 1.5;
			}
		}
	}
}

.order-list {
	padding: 24rpx 32rpx 0;

	.month-group {
		margin-bottom: 24rpx;

		.month-title {
			font-size: 30rpx;
			color: #5f677d;
			padding: 10rpx 0;
			margin-bottom: 16rpx;
		}

		.order-items {
			.order-item {
				background-color: #ffffff;
				border-radius: 20rpx;
				padding: 24rpx 32rpx;
				margin-bottom: 24rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border: 1px solid transparent; /* 添加透明边框，避免选中时的布局跳动 */
				
				&.selected {
					border: 1px solid #4365F4; /* 选中状态添加蓝色边框 */
				}
				
				&.not-selectable {
					opacity: 0.7; /* 降低不可选择项的透明度 */
					background-color: #f5f7fa; /* 更改背景色，使其看起来不可交互 */
					cursor: not-allowed; /* 鼠标样式显示为不可点击 */
				}

				.order-content {
					flex: 1;

					.order-date {
						font-size: 30rpx;
						color: #5f677d;
						margin-bottom: 20rpx;
					}

					.order-type-amount {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.order-type {
							font-size: 30rpx;
							font-weight: 500;
							color: #32364d;
						}

						.order-amount {
							display: flex;
							align-items: center;

							.order-amount-symbol {
								font-size: 20rpx;
								font-weight: 500;
								color: #171928;
								margin-top: 10rpx;
								margin-right: 2rpx;
							}

							.order-amount-value {
								font-size: 32rpx;
								color: #171928;
								font-family: TCloudNumber, sans-serif;
							}
						}
					}
				}

				.order-checkbox {
					margin-left: 20rpx;
				}
			}
		}
	}
}

.footer-placeholder {
	height: 160rpx; /* 为底部按钮留出空间 */
}

.footer-bar {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	height: 128rpx;
	background-color: #ffffff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 32rpx;
	border-top: 1px solid #eaedf7;
	box-sizing: border-box;
	z-index: 99;

	.total-info {
		.total-label {
			font-size: 24rpx;
			color: #939bb1;
		}

		.total-amount {
			display: flex;
			align-items: center;

			.total-symbol {
				font-size: 24rpx;
				color: #ff6b2c;
				font-weight: 500;
			}

			.total-value {
				font-size: 52rpx;
				color: #ff6b2c;
				font-family: TCloudNumber, sans-serif;
			}
		}

		.selected-count {
			font-size: 24rpx;
			color: #5f677d;
		}
	}

	.submit-btn {
		width: 182rpx;
	}
}

.help-btn {
	position: fixed;
	right: 64rpx;
	bottom: 160rpx;
	width: 112rpx;
	height: 112rpx;
	fill: var(--light-fill-blank, #FFF);
	border: #ADBFFF 1px solid;
	background-color: #ffffff;
	border-radius: 50%;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	box-shadow:
		0px 8px 8px -4px rgba(24, 39, 75, 0.04),
		0px 4px 8px -4px rgba(24, 39, 75, 0.06);
	z-index: 99;

	.help-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		
		.help-icon-image {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.help-text {
		font-size: 24rpx;
		color: #5f677d;
		margin-top: 4rpx;
	}
}
</style>