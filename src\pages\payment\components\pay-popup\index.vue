<template>
	<view class="pay-popup-wrap">
		<view class="pay-popup-content">
			<view class="pay-cell-wrap" v-if="payType.startsWith('bill')">
				<view class="pay-cell-left">
					<view class="cell-title">账单缴费</view>
					<view class="cell-value-left">
						<view class="cell-value">{{ billInfo.userNo }}</view>
					</view>
				</view>
				<view class="pay-money-wrap">
					<view class="pay-money-icon">￥</view>
					<view class="bill-pay-total-value">{{ billMoney }}</view>
				</view>
			</view>
			<view class="pay-cell-wrap" v-if="payType.includes('iot')">
				<view class="pay-cell-left">
					<view class="cell-title">余额充值</view>
					<view class="cell-value-left">
						<view class="cell-value">
							{{ meterInfo.meterNo || '--' }}
						</view>
						<view class="cell-value-line">|</view>
						<view class="cell-value-money">{{ '(' + (meterInfo.tableSurplusAmt || '0.00') + '元)' }}</view>
					</view>
				</view>
				<view class="pay-money-wrap">
					<view class="pay-money-icon">￥</view>
					<view class="bill-pay-total-value">{{ accountMoney }}</view>
				</view>
			</view>
			<view class="pay-cell-wrap" v-if="payType.includes('account')">
				<view class="pay-cell-left">
					<view class="cell-title">账户预存</view>
					<view class="cell-value-left">
						<view class="cell-value">{{ billInfo.userNo }}</view>
					</view>
				</view>
				<view class="pay-money-wrap">
					<view class="pay-money-icon">￥</view>
					<view class="bill-pay-total-value">{{ accountMoney }}</view>
				</view>
			</view>
			<view class="pay-total-wrap">
				<view class="cell-title">合计</view>
				<view class="bill-pay-total-wrap">
					<view class="bill-pay-total-icon">￥</view>
					<view class="bill-pay-total-value">{{ payAmount }}</view>
				</view>
			</view>
		</view>
		<view class="pay-foot-btn">
			<UiButton
				type="primary"
				width="100%"
				size="lg"
				style-name="light"
				text="确认缴费"
				position="center"
				@click="payClick"
			></UiButton>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { PropType } from 'vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import { getPayType, getPayAmount } from '@/core/pay/payUtils'
import type { MeterInfo } from '@/pages/commonTypes'
import type { BillInfoRes } from '../../paymentTypings'

defineOptions({
	name: 'PayPopup',
})

const props = defineProps({
	// 用户
	billInfo: {
		type: Object as PropType<BillInfoRes>,
		default: {},
	},
	// 表具
	meterInfo: {
		type: Object as PropType<MeterInfo>,
		default: {},
	},
	// 支付金额
	payAmount: {
		type: String,
		default: '',
	},
})

const payType = ref('')
const billMoney = ref('')
const accountMoney = ref('')
const initData = () => {
	payType.value = getPayType(props.meterInfo, props.billInfo.payableTotalAmt, Number(props.payAmount))
	const res = getPayAmount(props.billInfo.payableTotalAmt, props.payAmount)
	billMoney.value = res.billMoney
	accountMoney.value = res.accountMoney
}
initData()
const emits = defineEmits(['paySubmit'])

// 确认缴费点击
const payClick = () => {
	emits('paySubmit')
}

defineExpose({
	initData,
})
</script>

<style lang="scss" scoped>
.pay-popup-content {
	padding: 0 32rpx;
	.pay-cell-wrap {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		border-bottom: 1px solid var(--light-border-light);
		.pay-cell-left {
			flex: 1;
			.cell-title {
				height: 72rpx;
				font-weight: 600;
				font-size: var(--font-body-md);
				color: var(--light-form-normal-default-label);
				display: flex;
				flex-direction: row;
				align-items: center;
			}
			.cell-value-left {
				display: flex;
				flex-direction: row;
				align-items: center;
				gap: var(--form-md-margin);
				.cell-value {
					height: 72rpx;
					font-size: var(--font-body-md);
					color: var(--light-form-normal-default-text);
					display: flex;
					flex-direction: row;
					align-items: center;
				}
				.cell-value-line {
					color: var(--light-border-darker);
					font-size: 28rpx;
				}
				.cell-value-money {
					color: var(--light-form-normal-default-secondary);
					font-size: var(--font-body-md);
				}
			}
		}

		.pay-money-wrap {
			height: var(--list-md-height);
			display: flex;
			flex-direction: row;
			align-items: center;
			.pay-money-icon {
				font-weight: 600;
				font-size: var(--font-body-sm);
				color: var(--light-form-normal-default-text);
				margin-top: 6rpx;
			}
			.bill-pay-total-value {
				font-size: var(--font-number-md);
				color: var(--light-form-normal-default-text);
			}
		}
	}
	.pay-total-wrap {
		height: var(--form-md-height-md);
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		.bill-pay-total-wrap {
			display: flex;
			flex-direction: row;
			align-items: flex-end;
			.bill-pay-total-icon {
				font-weight: 600;
				font-size: var(--font-body-sm);
				color: var(--light-text-subcolor);
				margin-bottom: 7rpx;
			}
			.bill-pay-total-value {
				font-size: var(--font-number-lg);
				color: var(--light-text-subcolor);
			}
		}
	}
}
.pay-foot-btn {
	padding: var(--content-default-padding-md);
	background-color: var(--light-fill-blank);
}
</style>
