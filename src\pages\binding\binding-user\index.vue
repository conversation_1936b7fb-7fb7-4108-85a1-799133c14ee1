<template>
	<Layout title="绑定户号" left-text="" show-nav-bar layout-background="default" @left-click="navigateBack">
		<view class="page-content no-padding-wrap">
			<view class="tab-wrap">
				<UiCardTabs v-model="bindingType" :options="tabList" @change="handleTabClick">
					<view class="content-wrap">
						<view class="user-no-input-wrap">
							<UiInput v-model="userNo" background clearable></UiInput>
							<view class="remind-wrap">
								<UiTips
									type="warning"
									content="未经户主同意，请勿随意绑定他人户号，否则造成的财产损失和法律责任将由绑定在承担"
								></UiTips>
							</view>
						</view>
					</view>
				</UiCardTabs>
			</view>

			<UiFooterBtns :btnList="btnList"></UiFooterBtns>
			<UiPopup
				v-model="popupShow"
				ref="popupRef"
				position="bottom"
				round
				is-mask-click
				title="选择绑定的户号信息"
				backgroundType="lighter"
			>
				<view class="popup-content">
					<binding-query-user-item
						v-for="item in userList"
						:key="item.userNo"
						:userInfo="item"
						@itemClick="itemClick"
					></binding-query-user-item>
					<view class="popup-foot-view">
						<view class="popup-foot-btn">
							<UiButton
								type="normal"
								width="100%"
								size="lg"
								style-name="light"
								text="取消"
								position="center"
								@click="popupCancel"
							></UiButton>
						</view>
						<view class="popup-foot-btn">
							<UiButton
								type="secondary"
								width="100%"
								size="lg"
								style-name="light"
								text="确定"
								position="center"
								:disabled="bindingBtnDisabled"
								@click="bindingClick"
							></UiButton>
						</view>
					</view>
				</view>
			</UiPopup>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { showMsg } from '@/core/util'
import { getUserInfoApi } from '@/api/userBind.api'
import Layout from '@/layout/index.vue'
import UiTips from '@e-cloud/eslink-plus-uniapp/components/ui-tips/ui-tips.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiPopup from '@e-cloud/eslink-plus-uniapp/components/ui-popup/ui-popup.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCardTabs from '@e-cloud/eslink-plus-uniapp/components/ui-card-tabs/ui-card-tabs.vue'
import UiFooterBtns from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/ui-footer-btns.vue'
import BindingQueryUserItem from '../components/binding-query-user-item/index.vue'
import type { BtnItemProps } from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/types'
import type { UserInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'BindindUser',
})

const userNo = ref('')
const userList = ref([] as UserInfo[])
const tabList = ref([
	{ label: '户号', value: '0' },
	{ label: '表号', value: '1' },
])
const bindingType = ref('0')
const popupShow = ref(false)

const btnList = ref([
	{
		text: '下一步',
		type: 'secondary',
		size: 'lg',
		click: () => {
			if (!userNo.value) {
				const meterTypeObj = tabList.value.find(item => item.value === bindingType.value)
				let type = ''
				if (meterTypeObj) {
					type = meterTypeObj.label
				}
				showMsg('请输入' + type)
				return
			}
			let params = {
				userNo: userNo.value,
				meterNo: '',
			}
			if (bindingType.value == '1') {
				params = {
					userNo: '',
					meterNo: userNo.value,
				}
			}
			getUserInfoApi(params)
				.then((res: any) => {
					if (Array.isArray(res)) {
						userList.value = res
						popupShow.value = true
					} else {
						showMsg('未查询到用户信息', res)
					}
				})
				.catch(() => {})
		},
	},
] as BtnItemProps[])

const bindingBtnDisabled = computed(() => {
	const selectUser = userList.value.find(item => item.selected)
	if (selectUser) {
		return false
	} else {
		return true
	}
})

// tab切换
const handleTabClick = (value: string) => {
	bindingType.value = value
}

// 选中用户点击事件
const itemClick = (selectUser: any) => {
	if (Array.isArray(userList.value)) {
		userList.value.forEach(item => {
			if (item.addressId == selectUser.addressId) {
				item.selected = true
			} else {
				item.selected = false
			}
		})
	}
}

// 弹窗取消按钮点击
const popupCancel = () => {
	popupShow.value = false
}
// 绑定确认点击事件
const bindingClick = () => {
	const selectUser = userList.value.find(item => item.selected)
	if (!selectUser) {
		showMsg('请选择用户')
		return
	}
	uni.navigateTo({
		url:
			'/pages/binding/binging-validation/index?userNo=' +
			(selectUser.userNo || '') +
			'&meterNo=' +
			(selectUser.meterNo || '') +
			'&addressId=' +
			(selectUser.addressId || ''),
	})
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	.tab-wrap {
		padding-top: 24rpx;
	}
	.content-wrap {
		padding: 4rpx 24rpx;
		min-height: 510rpx;
		.user-no-input-wrap {
			gap: var(--content-default-margin-sm);
			border-radius: var(--content-default-radius-md);
			padding: var(--content-default-padding-sm) var(--content-default-padding-md);
			background: var(--light-fill-blank);
			.remind-wrap {
				margin-top: 30rpx;
			}
		}
	}

	.next-btn-wrap {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		background-color: var(--light-bg-base);
		height: 128rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		padding: var(--content-default-padding-sm) var(--content-default-padding-md);
	}
	.query-user-list-wrap {
		padding: var(--content-default-padding-md) var(--content-default-padding-lg);
		border: 1px solid var(--light-border-light);
	}
	.popup-content {
		background: var(--light-fill-lighter);
		padding: var(--content-default-padding-md) var(--content-default-padding-lg);
	}
	.popup-foot-view {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		height: 160rpx;
		.popup-foot-btn {
			width: calc((100% - 16rpx) / 2);
		}
	}
}
</style>
