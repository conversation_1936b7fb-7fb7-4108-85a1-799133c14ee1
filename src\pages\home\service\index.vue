<template>
	<Layout title="服务" left-text="" show-tabbar left-icon="" scroll :currentIndex="1" titlePosition="left">
		<view class="page-content">
			<view class="search-wrap">
				<UiSearch
					v-model="searchValue"
					placeholder="查找服务内容"
					@search="searchClick(true)"
					@change="val => searchClick(val ? true : false)"
					@clear="searchClick(false)"
				></UiSearch>
			</view>
			<view class="search-result-wrap" v-if="showSearchRes">
				<UiCell
					v-for="(item, index) in searchList"
					:key="index"
					:title="item.menuName"
					size="lg"
					:arrow="true"
					@cellClick="cellClick(item)"
				></UiCell>
			</view>
			<view class="page-wrap" v-else>
				<view class="tabs-wrap">
					<UiTabs v-model="activeTab" :list="tabList" size="lg" type="blank" @click="getMenuList"></UiTabs>
				</view>
				<view class="menu-wrap">
					<view class="left-wrap">
						<UiSidebar v-model="menuActive" :options="menuOptions" @change="menuGroupChange"></UiSidebar>
					</view>
					<view class="right-wrap">
						<view
							class="menu-wrap-item"
							:id="'menuGroup' + group.id"
							v-for="(group, index) in menuList"
							:key="index"
						>
							<UiTitle :title="group.groupName" :noPadding="false" size="sm"></UiTitle>
							<view class="menu-item-wrap" v-if="group.menuList">
								<view class="menu-item" v-for="(item, index) in group.menuList" :key="index">
									<image class="menu-item-icon" :src="item.icon"></image>
									<view class="menu-name">{{ item.menuName }}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { nextTick, ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import UiSearch from '@e-cloud/eslink-plus-uniapp/components/ui-search/ui-search.vue'
import UiSidebar from '@e-cloud/eslink-plus-uniapp/components/ui-sidebar/ui-sidebar.vue'
import { getServiceMenuList } from '@/api/service.api'
import type { MenuInfo, MenuItem, MenuOption } from './serviceTypings'

defineOptions({
	name: 'ServicePage',
})

const searchValue = ref('') // 搜索内容
const activeTab = ref('2') // 激活的标签 居民/工商业
const menuActive = ref('') // 激活的菜单分组
const showSearchRes = ref(false)
const tabList = ref([
	{ label: '居民', value: '2', type: 'round' },
	{ label: '工商业', value: '1', type: 'round' },
])

// 用户信息
const searchList = ref<MenuItem[]>([])

const menuOptions = ref<MenuOption[]>([])

const menuList = ref<MenuInfo[]>([])

// 页面加载完成
onLoad((options: any) => {
	// 消息类型
	if (options.type) {
		activeTab.value = options.type
	}
	if (options.id) {
		menuActive.value = options.id
	}
	getMenuList() // 第一次加载
})
// 查询菜单列表
const getMenuList = (value?: string) => {
	const params = {
		userType: activeTab.value,
	}
	getServiceMenuList(params)
		.then(res => {
			if (Array.isArray(res)) {
				menuList.value = res
				menuOptions.value = [] as MenuOption[]
				if (res.length > 0) {
					res.forEach((menu: MenuInfo) => {
						menuOptions.value.push({
							label: menu.groupName,
							value: menu.id.toString(),
						})
					})
					// 如果传过来的有id则直接选中该分组，否则默认选中第一个
					if (!value && menuActive.value) {
						menuGroupChange(menuActive.value)
					} else {
						menuActive.value = res[0].id.toString()
					}
				}
			}
		})
		.catch(() => {})
}

// 点击左测列表，滚动到相应模块
const menuGroupChange = (value: string) => {
	nextTick(() => {
		const dom = document.getElementById('menuGroup' + value)
		if (dom) {
			dom.scrollIntoView({
				behavior: 'smooth', // 平滑过渡
				block: 'start', // 上边框与视窗顶部平齐。默认值
			})
		}
	})
}

// 搜索按钮点击事件
const searchClick = (search: boolean) => {
	searchList.value = [] as MenuItem[]
	menuList.value.forEach((group: MenuInfo) => {
		if (Array.isArray(group.menuList)) {
			group.menuList.forEach((item: MenuItem) => {
				if (searchValue.value && item.menuName.includes(searchValue.value)) {
					searchList.value.push(item)
				}
			})
		}
	})
	if (cellList.value.length === 0 && search) {
		cellList.value.push({
			id: 0,
			title: '无搜索结果',
			arrow: false,
			size: 'lg',
		})
	}
	showSearchRes.value = search
}

// 搜索结果cell点击事件
const cellClick = (item: MenuItem) => {
	if (item.url) {
		uni.navigateTo({
			url: item.url,
		})
	}
}
</script>
<style lang="scss" scoped>
.page-content {
	height: 100%;
	display: flex;
	flex-direction: column;
	min-height: 0;
	.search-wrap {
		margin-bottom: var(--content-default-margin-md);
		background: var(--light-fill-blank);
	}
	.page-wrap {
		display: flex;
		flex-direction: column;
		flex: 1;
		min-height: 0;

		.tabs-wrap {
			border-bottom: 1px solid var(--light-border-light);
		}
		.menu-wrap {
			margin-top: var(--content-default-margin-md);
			display: flex;
			flex-direction: row;
			flex: 1;
			min-height: 0;
			.left-wrap {
				width: 192rpx;
				background: var(--light-fill-blank);
			}
			.right-wrap {
				flex: 1;
				margin-left: var(--content-default-padding-md);
				overflow: auto;
				.menu-wrap-item {
					border-radius: var(--content-default-radius-md);
					background: var(--light-fill-blank);
					padding: 20rpx;
					margin-top: var(--content-default-margin-md);
					&:first-child {
						margin-top: 0;
					}
					.menu-item-wrap {
						display: flex;
						flex-direction: row;
						flex-wrap: wrap;
						.menu-item {
							width: calc(100% / 3);
							display: flex;
							flex-direction: column;
							align-items: center;
							margin-top: 20rpx;
							image {
								width: 80rpx;
								height: 80rpx;
								border: 1px solid lightgrey;
								border-radius: 50%;
							}
							.menu-name {
								font-size: var(--font-body-md);
								color: var(--light-text-secondary);
								text-align: center;
								height: 44rpx;
								line-height: 44rpx;
								margin-top: 20rpx;
							}
						}
					}
				}
			}
		}
	}

	.search-result-wrap {
		padding: var(--content-default-padding-sm) 0;
	}
}
</style>
