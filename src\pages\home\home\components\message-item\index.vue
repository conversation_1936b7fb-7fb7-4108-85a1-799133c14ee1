<template>
	<view class="message-item-wrap">
		<image class="message-item-icon" src="/static/images/home/<USER>"></image>
		<view class="message-content">{{ messageInfo.title || '--' }}</view>
		<view class="message-date">
			{{ messageInfo.createDate ? dayjs(messageInfo.createDate).format('MM/DD') : '--' }}
		</view>
	</view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import dayjs from 'dayjs'
import type { MessageInfo } from '../../homeTypings'
defineOptions({
	name: 'MessageItem',
})

defineProps({
	messageInfo: {
		type: Object as PropType<MessageInfo>,
		default: {},
	},
})
</script>

<style lang="scss" scoped>
.message-item-wrap {
	display: flex;
	flex-direction: row;
	align-items: center;
	height: var(--list-sm-height);
	gap: var(--form-sm-margin);
	.message-item-icon {
		width: 32rpx;
		height: 32rpx;
	}
	.message-content {
		flex: 1;
		font-size: var(--font-body-md);
		color: var(--light-form-normal-default-label);
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.message-date {
		font-size: var(--font-body-md);
		color: var(--light-form-normal-default-text-light);
	}
}
</style>
