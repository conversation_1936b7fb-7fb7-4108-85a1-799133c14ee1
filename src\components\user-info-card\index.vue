<template>
	<view class="user-info-wrap" :class="{ payment: type == 'payment' }" @click="changeUserClick">
		<image v-if="type == 'payment'" class="user-info-img" src="@/static/images/common/user-info-bg-img.png"></image>
		<image class="user-info-right-icon" src="@/static/images/common/user-info-bg-icon.png"></image>
		<view class="user-info-header">
			<div class="user-name-wrap">
				<UiTitle
					:title="userInfo.userName || '--'"
					textColor="#fff"
					suffixIcon="arrow-right-s-line"
					size="lg"
				></UiTitle>
			</div>
			<view class="action-button">
				<slot name="action"></slot>
			</view>
		</view>
		<view class="user-info-content">
			<UiCell v-for="(prop, index) in userProps" :key="index" :title="userInfo[prop.value]" size="sm">
				<template #prefix>
					<image class="user-info-icon" :src="prop.icon"></image>
				</template>
			</UiCell>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { UserInfo } from '@/pages/commonTypes'
import { showMsg } from '@/core/util'
import { getBindUserListApi, getUserInfoApi } from '@/api/userBind.api'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'
import userNoIcon from '@/static/images/common/user-no-icon.png'
import addressIcon from '@/static/images/common/address-icon.png'

defineOptions({
	name: 'UserInfoCard',
})

const props = defineProps({
	// 户号
	userNo: {
		type: String,
		default: '',
	},
	// 地址id
	addressId: {
		type: String,
		default: '',
	},
	// 是否显示切换户号按钮
	showChangeUser: {
		type: Boolean,
		default: true,
	},
	type: {
		type: String,
		default: 'default',
	},
})
// 用户信息
const userInfo = ref({} as UserInfo)
const userNo = ref('')
const addressId = ref('')

const emits = defineEmits(['changeUser', 'payRecord', 'getUserInfo'])

onMounted(() => {
	if (props.userNo && props.addressId) {
		userNo.value = props.userNo
		addressId.value = props.addressId
		queryUserInfo() // 如果有传用户信息，则查询用户信息
	} else {
		getBindUserList() // 没传用户信息则查询绑定用户列表
	}
})
// 展示的字段
const userProps = ref([
	{
		key: '户号',
		value: 'userNo',
		icon: userNoIcon,
	},
	{
		key: '地址',
		value: 'userAddress',
		icon: addressIcon,
	},
])

// 获取绑定用户列表
const getBindUserList = () => {
	const params = {
		needPage: true,
		pageNum: 1,
		pageSize: 10,
	}
	getBindUserListApi(params)
		.then(res => {
			if (res && res.userBindList && res.userBindList.length > 0) {
				res.userBindList.forEach((item: UserInfo) => {
					if (item.defaultUser) {
						userInfo.value = item
					}
				})
				// 默认用户不存在，则取列表第一个
				if (!userInfo.value.userNo) {
					userInfo.value = res.userBindList[0]
				}
				emits('getUserInfo', userInfo.value) // 获取到用户信息了
			} else {
				showMsg('未查询到用户信息', res)
			}
		})
		.catch(() => {})
}
// 查询用户信息
const queryUserInfo = () => {
	let params = {
		userNo: userNo.value,
		addressId: addressId.value,
	}
	getUserInfoApi(params)
		.then((res: any) => {
			if (Array.isArray(res) && res.length) {
				userInfo.value = res[0]
				emits('getUserInfo', userInfo.value) // 获取到用户信息了
			} else {
				showMsg('未查询到用户信息', res)
			}
		})
		.catch(() => {})
}
// 切换户号按钮点击
const changeUserClick = (event: any) => {
	uni.navigateTo({
		url: '/pages/binding/binding-list/index?userNo=' + userNo.value + '&pageType=selectUser',
		events: {
			// 为指定事件添加一个监听器，获取被打开页面传送到当前页面的数据
			selectCallBack: (data: UserInfo) => {
				userNo.value = data.userNo || ''
				addressId.value = data.addressId || ''
				queryUserInfo()
				emits('changeUser', data)
			},
		},
	})
}

defineExpose({
	userInfo,
	changeUserClick,
})
</script>

<style lang="scss" scoped>
.user-info-wrap {
	position: relative;
	border-radius: var(--content-default-radius-md);
	background: linear-gradient(180deg, #273667 0%, #9cb4e0 100%);
	overflow: hidden;
	.user-info-header {
		height: var(--list-lg-height);
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		color: white;
		.user-name-wrap {
			flex: 1;
			:deep(.ui-title-lg .ui-title-title) {
				font-size: var(--font-body-xlg);
			}
		}
	}
	.user-info-content {
		background-color: #fff;
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-margin-lg) 0;
		.user-info-icon {
			width: 32rpx;
			height: 32rpx;
			margin-right: 12rpx;
			margin-top: 6rpx;
		}
		:deep(.setting-cell-wrap .cell-left .cell-title-sm) {
			color: var(--light-list-normal-default-text);
			font-weight: 400;
		}
	}
	.user-info-img {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
	}
	.user-info-right-icon {
		position: absolute;
		top: 0;
		right: 0;
		width: 104rpx;
		height: 104rpx;
	}
}
.payment {
	padding: var(--content-default-padding-xs);
	background: transparent;
}
</style>
