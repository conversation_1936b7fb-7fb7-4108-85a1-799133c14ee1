import { SizeEnum } from '../../shared/enum'

export enum TabType {
	default = 'default',
	fillDark = 'fillDark',
	blank = 'blank',
	round = 'round',
	fillLight = 'fillLight',
}

export enum DotType {
	primary = 'primary',
	success = 'success',
	error = 'error',
}

export interface TabProps {
	label: string
	value?: string | number
	dot?: keyof typeof DotType
	badge?: string | number
}

export interface TabsProps {
	/**
	 * @description 选中的标签
	 */
	modelValue?: string | number
	/**
	 * @description 标签类型
	 */
	type: keyof typeof TabType
	/**
	 * @description 尺寸
	 */
	size: Exclude<keyof typeof SizeEnum, 'min' | 'xlg'>
	/**
	 * @description 标签列表
	 */
	list: Array<TabProps | string>
}
