<template>
	<Layout
		title="消息中心"
		left-text=""
		nav-bar-background="transparent"
		layout-background="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<view class="page-header-wrap">
				<UiTabs v-model="activeTab" :list="tabList" size="lg"></UiTabs>
				<view>
					<UiButton
						type="transparent"
						size="md"
						style-name="light"
						icon="icon-brush-line"
						:text="'全部已读(' + unReadCount + ')'"
						@click="allReadClick"
					></UiButton>
				</view>
			</view>
			<view class="list-wrap">
				<view
					class="list-item"
					v-for="(item, index) in activeTab == '1' ? noticeList : activitiesList"
					:key="index"
					@click="itemClick(item)"
				>
					<view class="icon-wrap" :class="{ 'icon-wrap-activities': activeTab == '4' }">
						<image v-if="activeTab == '1'" src="/static/images/home/<USER>"></image>
						<image v-else src="/static/images/home/<USER>"></image>
						<view v-if="!item.read" class="message-dot"></view>
					</view>
					<view class="item-content">
						<view class="item-header-wrap">
							<view class="item-title">{{ item.title || '-' }}</view>
							<view class="item-date">
								{{ item.createDate ? dayjs(item.createDate).format('YYYY/MM/DD HH:mm') : '--' }}
							</view>
						</view>
						<view class="item-desc">{{ item.intro || '--' }}</view>
					</view>
				</view>
			</view>
		</view>
		<UiPopupConfirm
			v-model="showConfirm"
			ref="popupConfirmRef"
			title="全部设为已读？"
			cancelText="取消"
			confirmText="确定"
			width="686rpx"
			@confirm="confirmClick"
		>
			<view>所有通知公告和活动推荐全部设为已读</view>
		</UiPopupConfirm>
	</Layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import Layout from '@/layout/index.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiPopupConfirm from '@e-cloud/eslink-plus-uniapp/components/ui-popup-confirm/ui-popup-confirm.vue'
import type { MessageInfo } from '../homeTypings'
import { getNoticeList, allRead } from '@/api/home.api'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'MessageCenter',
})

// 全部已读二次确认弹窗
const showConfirm = ref(false)
// 消息列表
const noticeList = ref([] as MessageInfo[])
// 活动列表
const activitiesList = ref([] as MessageInfo[])
const activeTab = ref('1')
const tabList = ref([
	{ label: '通知公告', value: '1', type: 'blank', dot: '' },
	{ label: '活动推荐', value: '4', type: 'blank', dot: '' },
])

const pageConfig = {
	pageNum: 1,
	pageSize: 10,
}

// 页面加载完成
onLoad((options: any) => {
	// 消息类型
	if (options.type) {
		activeTab.value = options.type
	}
})

// 页面显示时
onShow(() => {
	if (activeTab.value) {
		getNoticeInfoList('1')
		getNoticeInfoList('4')
	}
})

// 未读消息数量
const unReadCount = computed(() => {
	const noticeCount = noticeList.value.filter(item => !item.read).length
	const activitiesCount = activitiesList.value.filter(item => !item.read).length
	// 更新tab的红点状态
	tabList.value.forEach(item => {
		if (item.value === '1') {
			if (noticeCount > 0) {
				item.dot = 'error'
			} else {
				item.dot = 'light'
			}
		} else if (item.value === '4') {
			if (activitiesCount > 0) {
				item.dot = 'error'
			} else {
				item.dot = 'light'
			}
		}
	})
	return noticeCount + activitiesCount
})

// 获取消息通知/活动推荐列表
const getNoticeInfoList = (type: string) => {
	const params = {
		type: type,
		pageNum: pageConfig.pageNum,
		pageSize: pageConfig.pageSize,
	}
	getNoticeList(params)
		.then(res => {
			if (res && Array.isArray(res.list)) {
				if (res.list.length > 0) {
					const firstItem = res.list[0]
					if (firstItem.type == '1') {
						noticeList.value = res.list
					} else {
						activitiesList.value = res.list
					}
				}
			}
		})
		.catch(() => {})
}

// 全部已读
const allReadClick = () => {
	showConfirm.value = true
}

// 全部已读确认点击
const confirmClick = () => {
	const params = {}
	allRead(params).then(res => {
		showMsg('更新成功')
		getNoticeInfoList('1')
		getNoticeInfoList('4')
		showConfirm.value = false
	})
}

// 列表cell点击
const itemClick = (item: any) => {
	uni.navigateTo({
		url: '/pages/home/<USER>/message-detail/index?id=' + item.id,
	})
}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.page-header-wrap {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}
.list-wrap {
	margin-top: var(--content-default-margin-md);
	.list-item {
		display: flex;
		flex-direction: row;
		align-items: center;
		padding-right: var(--content-default-padding-md);
		gap: var(--content-default-margin-md);
		.icon-wrap {
			position: relative;
			display: flex;
			flex-direction: row;
			justify-content: center;
			align-items: center;
			width: 96rpx;
			height: 96rpx;
			border-radius: 48rpx;
			background: var(--light-primary-l2);
			.message-dot {
				position: absolute;
				top: 0;
				right: 0;
				width: 20rpx;
				height: 20rpx;
				border-radius: 10rpx;
				background: var(--light-error-default);
				border: 1px solid var(--light-fill-lighter);
			}
			image {
				width: 48rpx;
				height: 48rpx;
			}
		}
		.icon-wrap-activities {
			background: var(--light-important-l1);
		}
		.item-content {
			flex: 1;
			padding: var(--content-default-padding-sm) 0;
			border-bottom: 1px solid var(--light-border-light);
			min-width: 0;
			.item-header-wrap {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;
				height: var(--list-sm-height);
				min-width: 0;
				.item-title {
					font-size: var(--font-body-lg);
					color: var(--light-text-title);
					white-space: nowrap;
					text-overflow: ellipsis;
					overflow: hidden;
					flex: 1;
				}
				.item-date {
					font-size: var(--font-body-sm);
					color: var(--light-text-secondary);
					margin-left: 12rpx;
					margin-top: 4rpx;
				}
			}
			.item-desc {
				height: 60rpx;
				line-height: 60rpx;
				width: 100%;
				font-size: var(--font-body-md);
				color: var(--light-text-light);
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
			}
		}
	}
}
</style>
