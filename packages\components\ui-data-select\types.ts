export interface OptionItem {
	label: string
	value: string | number
	disabled?: boolean
	[key: string]: any
}

export type SelectionType = number | string | OptionItem

export interface DataSelectProps {
	/**
	 * @description 绑定值
	 */
	modelValue: SelectionType
	/**
	 * @description 列数据
	 */
	options: Array<SelectionType>
	/**
	 * @description 作为 value 唯一标识的键名，绑定值为对象类型时必填
	 */
	valueKey?: string
	/**
	 * @description 设置选择器中间选中框的样式
	 */
	indicatorStyle?: string
	/**
	 * @description 设置选择器中间选中框的类名，注意页面或组件的style中写了scoped时，需要在类名前写/deep/
	 */
	indicatorClass?: string
	/**
	 * @description 选择器高度
	 */
	height?: string
}
