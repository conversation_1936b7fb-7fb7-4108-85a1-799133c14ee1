import { SizeEnum } from '../../shared/enum'

export enum InputType {
	text = 'text',
	textarea = 'textarea',
	number = 'number',
	digit = 'digit',
	tel = 'tel',
	idcard = 'idcard',
	'safe-password' = 'safe-password',
	nickname = 'nickname',
	password = 'password',
}

export interface InputProps {
	modelValue: string | number
	size?: SizeEnum.lg | SizeEnum.md
	placeholder?: string
	showPassword?: boolean
	type?: InputType
	maxlength?: number
	disabled?: boolean
	suffix?: string
	prefix?: string
	placeholderClass?: string
	clearable?: boolean
	customClass?: string
}
