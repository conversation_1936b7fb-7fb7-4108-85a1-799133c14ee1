import { ref } from 'vue'
import { tenantAndChannelEnApi } from '@/api/common.api'

export default class {
	public static forwardObj = {} as any // 缴费查询弹窗提示信息
	public static moneySwiftItem = ref([]) // 缴费金额快捷选择标签
	public static testEnv = ref(false)
	// 获取公共配置
	public static init() {
		return new Promise<any>((resolve, reject) => {
			tenantAndChannelEnApi()
				.then(res => {
					if (res) {
						// const userBindConfig = res.result.userBindConfig || {}
						// 混合缴费配置
						const mixedPaymentConfig = res.mixedPaymentConfig || {}
						this.forwardObj = this.turnForwardListToObj(mixedPaymentConfig.forward || [])
						this.moneySwiftItem.value = mixedPaymentConfig.moneySwiftItem || []

						// 环境配置
						const environmentConfig = res.environmentConfig || {}
						this.testEnv.value = environmentConfig.testEnvironment || false

						resolve(res)
					}
				})
				.catch(err => {
					reject(err)
				})
		})
	}

	private static turnForwardListToObj = (forwardList: any) => {
		const newArray = forwardList.map((item: any) => [item.code, { tip: item.tip, url: item.toUrl }])
		return Object.fromEntries(newArray)
	}
}
