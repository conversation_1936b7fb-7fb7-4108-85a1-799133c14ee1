import { defineConfig } from 'vite'
import copyPlugin from 'rollup-plugin-copy'
import { resolve } from 'path'
import config from './vite.config'

export default ({ mode }) => {
	return defineConfig({
		...config(mode),
		plugins: [
			// @ts-ignore
			...config(mode).plugins,
			copyPlugin({
				targets: [
					{
						src: resolve(__dirname, 'tenant/ext.json'),
						dest: resolve(__dirname, 'dist/build/mp-alipay'),
					},
				],
				hook: 'writeBundle', // 确保在写入 bundle 后执行
			}),
		],
	})
}
