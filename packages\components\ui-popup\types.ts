export enum PopupPosition {
	top = 'top',
	bottom = 'bottom',
	center = 'center',
	left = 'left',
	right = 'right',
}

export enum IconPositionEnum {
	'top-left' = 'top-left',
	'top-right' = 'top-right',
	'bottom-left' = 'bottom-left',
	'bottom-right' = 'bottom-right',
}

export interface PopupProps {
	/**
	 * @description 是否显示
	 */
	modelValue?: boolean
	/**
	 * @description 弹出层标题
	 */
	title?: string
	/**
	 * @description 是否圆角
	 */
	round?: boolean
	/**
	 * @description 弹出层位置
	 */
	position?: keyof typeof PopupPosition
	/**
	 * @description zIndex
	 */
	zIndex?: number | string
	/**
	 * @description 宽度
	 */
	width?: string
	/**
	 * @description 显示遮罩
	 */
	maskShow?: boolean
	/**
	 * @description 是否允许点击遮罩关闭
	 */
	isMaskClick?: boolean
	/**
	 * @description 是否展示关闭按钮
	 */
	closeable?: boolean
	/**
	 * @description 关闭按钮图标
	 */
	closeIcon?: string
	/**
	 * @description 图标字体名
	 */
	iconfontClass?: string
	/**
	 * @description 关闭按钮位置
	 */
	iconPosition?: keyof typeof IconPositionEnum
	/**
	 * @description 背景类型
	 */
	backgroundType?: 'blank' | 'lighter'
}
