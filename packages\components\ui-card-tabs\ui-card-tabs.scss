.ui-card-tabs {
	width: 100%;
	box-sizing: border-box;
	background: transparent;
}

.ui-card-tabs-bottom {
	position: relative;
	height: 24rpx;
	background: var(--light-fill-blank);
}

.ui-card-tab-header-list {
	box-sizing: border-box;
	height: 100rpx;
}

.ui-card-tab-header-list-inner {
	display: flex;
	flex-wrap: nowrap;
	justify-content: flex-start;
	align-items: center;
	position: relative;
}

.ui-card-tab-header-item {
	position: relative;
	box-sizing: border-box;
	height: 100rpx;
	padding-top: 12px;
	flex: 1;
	&.active {
		.ui-card-tab-header-item-active-background {
			opacity: 1;
		}
	}
	.ui-card-tab-header-item-active-background {
		box-sizing: border-box;
		position: absolute;
		left: 0;
		bottom: 0;
		right: 0;
		margin: 0 50rpx;
		height: 100rpx;
		background: rgba(255, 255, 255, 1);
		opacity: 0;
		transition: all 0.3s ease-out;
		&::before,
		&::after {
			position: absolute;
			content: '';
			width: 100rpx;
			height: 100%;
			bottom: 0;
			background-size: contain;
			background-repeat: no-repeat;
			background-position: center;
		}

		&::before {
			// 保留1像素安全区
			left: -100rpx + 1rpx;
			background-image: url('../../assets/images/components/ui-card-tabs/ui-card-tab-left.png');
		}
		&::after {
			// 保留1像素安全区
			right: -100rpx + 1rpx;
			background-image: url('../../assets/images/components/ui-card-tabs/ui-card-tab-right.png');
		}
	}

	&:first-child {
		.ui-card-tab-header-item-inner {
			padding: 0 50rpx 0 40rpx;
			// justify-content: flex-start;
		}
		.ui-card-tab-header-item-background {
			border-top-left-radius: var(--radius-lg);
		}
		.ui-card-tab-header-item-active-background {
			// margin: 0 50rpx 0 40rpx;
			margin-left: 40rpx;
			&::before {
				left: -40rpx;
				background: rgba(255, 255, 255, 1);
				border-top-left-radius: var(--radius-lg);
				background-image: none;
				width: 40rpx;
			}
		}
	}
	&:last-child {
		.ui-card-tab-header-item-inner {
			padding: 0 40rpx 0 50rpx;
			// justify-content: flex-end;
		}
		.ui-card-tab-header-item-background {
			border-top-right-radius: var(--radius-lg);
		}
		.ui-card-tab-header-item-active-background {
			// margin: 0 40rpx 0 50rpx;
			margin-right: 40rpx;
			&::after {
				right: -40rpx;
				border-top-right-radius: var(--radius-lg);
				background: rgba(255, 255, 255, 1);
				background-image: none;
				width: 40rpx;
			}
		}
	}
	&.active {
		color: var(--light-tabs-focus-text-text);
		font-weight: 600;
		z-index: 9;
	}
}

.ui-card-tab-header-item-inner {
	padding: 0 100rpx;
	white-space: nowrap;
	position: relative;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
}

.ui-card-tab-header-item-background {
	position: absolute;
	width: 100%;
	height: 88rpx;
	left: 0;
	bottom: 0;
	background: var(--light-primary-l1);
}

.ui-card-tabs-content {
	padding-top: 12rpx;
	position: relative;
	overflow: hidden;
}

.ui-card-tabs-linear {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 510rpx;
	background: linear-gradient(180deg, #ffffff 3.45%, rgba(255, 255, 255, 0) 76.22%);
}
