<template>
	<Layout
		ref="layout"
		:show-tabbar="showTabbar"
		nav-bar-background="transparent"
		left-text=""
		left-icon=""
		layout-background="mineLinearGradient"
		:currentIndex="2"
		@onReachBottom="onReachBottom"
	>
		<UiRadio v-model="radioChecked"></UiRadio>
		<view class="mt-40" @click="handlePopupConfirmClick">点击触发popup-confirm</view>
		<view class="mt-40" @click="handlePopupClick">点击触发popup</view>
		<view class="mt-40">tabs</view>
		<view class="padding-12">
			<UiInput v-model="inputValue" size="md" clearable required vertical background label="标题文字"></UiInput>
		</view>

		<view class="width: 100%; height: 50px;">
			<textarea style="border 1px solid red;" :auto-height="true" :maxlength="500" />
		</view>
		<view>
			<UiTabs v-model="activeTab" :list="tabList" size="md" type="blank" @click="handleTabClick"></UiTabs>
		</view>
		<view class="mt-40" style="margin-top: 400rpx">Tags</view>
		<view class="list-wrapper">
			<UiTag
				v-for="(tag, index) in tagList"
				:key="index"
				:type="tag.type"
				:size="tag.size"
				icon="icon-add-line"
				text="标签"
			></UiTag>
		</view>
		<view class="mt-40">light风格</view>
		<UiPopup
			v-model="popupShow"
			ref="popupRef"
			position="bottom"
			round
			mask-show
			is-mask-click
			title="选择绑定的户号信息"
			@open="handlePopupChange"
			@close="handlePopupChange"
		>
			asdf
		</UiPopup>
		<UiPopupConfirm v-model="confirmVisible" ref="popupConfirmRef" :before-close="beforeClose">
			<view>这是一段自定义内容，通过插槽显示</view>
		</UiPopupConfirm>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onBeforeMount } from 'vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiPopup from '@e-cloud/eslink-plus-uniapp/components/ui-popup/ui-popup.vue'
import UiPopupConfirm from '@e-cloud/eslink-plus-uniapp/components/ui-popup-confirm/ui-popup-confirm.vue'
import UiRadio from '@e-cloud/eslink-plus-uniapp/components/ui-radio/ui-radio.vue'
import Layout from '@/layout/index.vue'

defineOptions({
	name: 'DemoPage',
})
let layout = ref()
const inputValue = ref('')
const tagList = ref([])
const tabList = ref([])
const activeTab = ref('one')
const popupConfirmRef = ref()
const confirmVisible = ref(false)
const popupShow = ref(false)
const popupRef = ref()
const showTabbar = ref(true)
const radioChecked = ref(false)

onBeforeMount(() => {
	initTagList()
	initTabList()
})

const handleTabClick = data => {
	console.log(data)
}

const handlePopupConfirmClick = () => {
	confirmVisible.value = true
	// popupConfirmRef.value
	// 	.show({ title: 'xxx' })
	// 	.then(() => {
	// 		console.log('点击了确定')
	// 	})
	// 	.catch(() => {
	// 		console.log('点击了取消')
	// 	})
}

const handlePopupClick = () => {
	popupShow.value = true
}

const handlePopupChange = show => {
	console.log('show', show)
	if (show) {
		showTabbar.value = false
	} else {
		setTimeout(() => {
			showTabbar.value = true
		}, 300)
	}
}

const beforeClose = () => {
	return new Promise(resolve => {
		setTimeout(() => {
			resolve(true)
		}, 2000)
	})
}

const initTagList = () => {
	const sizeList = ['md', 'sm', 'min']
	const typeList = [
		'default',
		'primary',
		'warning',
		'success',
		'error',
		'accent',
		'doing',
		'info',
		'otherflow',
		'important',
	]
	tagList.value = typeList
		.map(item => {
			return sizeList.map(size => ({
				type: item,
				size: size,
			}))
		})
		.flat()
}

const initTabList = () => {
	tabList.value = [
		{ label: '标签1', value: 'one', type: 'blank' },
		{ label: '标签2', value: 'two', type: 'blank' },
		{ label: '发射机哦对房价哦3', value: 'three', badge: 999 },
		{ label: '标签4', value: 'four', dot: 'error', badge: 88 },
		{ label: '标签5', value: 'five', dot: 'success' },
		{ label: '标签6', value: 'six', dot: 'primay' },
	]
}
const onReachBottom = () => {
	console.log('触发上拉加载更多')
	setTimeout(() => {
		layout.value.endBottomLoading('')
	}, 1000)
}
</script>
<style>
.list-wrapper {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	align-items: center;
	gap: 20rpx;
	padding: 24rpx 8rpx;
	background-color: #f8f9ff;
}

.mt-40 {
	margin-top: 40rpx;
}
.padding-12 {
	padding: 24rpx;
}
</style>
