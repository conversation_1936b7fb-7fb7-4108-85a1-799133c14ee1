export enum MessageType {
	success = 'success',
	error = 'error',
	warning = 'warning',
	info = 'info',
}

export interface MessageProps {
	/**
	 * @description 显示时间;消息显示时间，超过显示时间组件自动关闭，设置为0 将不会关闭，需手动调用 close 方法关闭
	 */
	duration?: number
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
}

export interface MessageExpose {
	open: (payload: { type: keyof typeof MessageType; message: string }) => void
	success: (message: string) => void
	error: (message: string) => void
	warning: (message: string) => void
	info: (message: string) => void
	close: () => void
}
