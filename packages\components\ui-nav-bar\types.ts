export interface NavBarProps {
	/**
	 * @description 标题
	 */
	title?: string
	/**
	 * @description 左侧文字
	 */
	leftText?: string
	/**
	 * @description 右侧文字
	 */
	rightText?: string
	/**
	 * @description 图标字体名
	 */
	iconfontClass?: string
	/**
	 * @description 左侧图标
	 */
	leftIcon?: string
	/**
	 * @description 右侧图标
	 */
	rightIcon?: string
	/**
	 * @description 是否固定顶部，为true时自动开启安全区域
	 */
	fixed?: boolean
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
	/**
	 * @description 标题栏风格
	 */
	type?: 'light' | 'dark'
	/**
	 * @description 是否开启点击反馈
	 */
	clickable?: boolean
	/**
	 * @description 是否开启顶部安全区适配
	 */
	safeAreaInsetTop?: boolean
	/**
	 * @description 背景样式
	 */
	background?: string
	/**
	 * @description 标题位置
	 */
	titlePosition?: string
}
