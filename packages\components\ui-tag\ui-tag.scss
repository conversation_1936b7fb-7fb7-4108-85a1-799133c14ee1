$tag-types: 'default', 'primary', 'warning', 'success', 'error', 'accent', 'doing', 'info', 'otherflow', 'important';

@mixin set-tag-size($size) {
	height: var(--tag-#{$size}-height);
	border-radius: var(--tag-#{$size}-radius);
	padding: 0 var(--tag-#{$size}-padding);
	font-size: var(--font-body-#{$size});
	line-height: var(--font-body-#{$size});

	.ui-tag-icon {
		font-size: var(--icon-#{$size});
		line-height: var(--icon-#{$size});
	}

	.ui-tag-text + .ui-tag-icon {
		margin-left: var(--tag-min-margin);
	}
}

@mixin set-tag-type($type) {
	$prefix: --light-tag;

	border-color: var(--light-tag-#{$type}-border);
	background-color: var(--light-tag-#{$type}-background);
	color: var(#{$prefix}-#{$type}-text);
}

.ui-tag {
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	width: fit-content;
	border-width: 2rpx;
	border-style: solid;
	font-weight: 400;
	white-space: nowrap;


	@each $type in $tag-types {
		&.#{$type} {
			@include set-tag-type($type);
		}
	}
}

.ui-tag-md {
	@include set-tag-size('md');
}

.ui-tag-sm {
	@include set-tag-size('sm');
}

.ui-tag-min {
	@include set-tag-size('min');
}
