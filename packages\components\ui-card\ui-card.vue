<template>
	<view class="ui-card">
		<view class="ui-card-header" v-if="withHeader">
			<slot name="header">
				<ui-title
					:size="size"
					:prefix-icon="prefixIcon"
					:suffix-icon="suffixIcon"
					:title="title"
					:sub-title="subTitle"
					@suffix-click="handleSuffixClick"
					@prefix-click="handlePrefixClick"
					@click.stop="handleHeaderClick"
				>
					<template #prefix v-if="$slots.prefix">
						<slot name="prefix"></slot>
					</template>
					<template #title v-if="$slots.title">
						<slot name="title"></slot>
					</template>
					<template #subTitle v-if="$slots.subTitle">
						<slot name="subTitle"></slot>
					</template>
					<template #suffix v-if="$slots.suffix">
						<slot name="suffix"></slot>
					</template>
					<template #extra v-if="showArrorwRight || $slots.extra">
						<slot name="extra">
							<ui-icon
								class="ui-card-arrow-right"
								v-if="showArrorwRight"
								name="arrow-right-s-line"
								size="lg"
								@click="handleArrowRightClick"
							></ui-icon>
						</slot>
					</template>
				</ui-title>
			</slot>
		</view>
		<view class="ui-card-content">
			<slot></slot>
		</view>
	</view>
</template>
<script setup lang="ts">
import UiTitle from '../ui-title/ui-title.vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import type { CardProps } from './types'
import { SizeEnum } from '../../shared/enum'

defineOptions({
	options: {
		styleIsolation: 'shared',
		virtualHost: true,
	},
})

const props = withDefaults(defineProps<CardProps>(), {
	size: SizeEnum.md,
	prefixIcon: '',
	suffixIcon: '',
	title: '',
	subTitle: '',
	withHeader: true,
	showArrorwRight: true,
})

const emits = defineEmits(['suffix-click', 'prefix-click', 'header-click', 'arrow-right-click'])

const handleSuffixClick = e => {
	emits('suffix-click', e)
}
const handlePrefixClick = e => {
	emits('prefix-click', e)
}
const handleHeaderClick = e => {
	emits('header-click', e)
}
const handleArrowRightClick = e => {
	emits('arrow-right-click', e)
}
</script>
<style lang="scss">
@import url('./ui-card.scss');
</style>
