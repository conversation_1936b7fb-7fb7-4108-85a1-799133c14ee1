import CryptoJS from 'crypto-js'

const eks = ['be7b2b01889ecde9', '6c326d5f307fb38e'] // 目前有两个加密key，线上都在用
const jsonEncryptWhiteUrl = [
	'/utility/en/personalCenter/feeQuery',
	'/utility/en/personalCenter/getBillInfoListPage',
	'/utility/en/personalCenter/billQueryAndIotPAYQUERY',
	'/selfHelpGateway/utility/en/userBind/getBindUserInfo',
	'/selfHelpGateway/utility/en/userBind/publicUtilitiesUserInfoQuery',
	'/selfHelpGateway/en/loginUser/getUserInfo',
	'/selfHelpGateway/en/column/getList',
	'/selfHelpGateway/utility/en/user/getDefaultUser',
	'/selfHelpGateway/utility/en/rechargePayment/usmartAndPb',
	'/selfHelpGateway/utility/en/config/tenantAndChannel',
	'/selfHelpGateway/utility/en/rechargePayment/meterList',
	'/selfHelpGateway/utility/en/rechargePayment/queryMeterInfo',
	'/selfHelpGateway/utility/en/unifiedOrder',
]
const strEncryptWhiteUrl = ['/cloudselfhelp/utility/en/userBind/getBindUserInfo']

export default class Crypto {
	// aes加密
	static encrypt(data: string, index = 0): string {
		const keyStr = eks[index] // 加密key默认用第一个
		try {
			const key = CryptoJS.enc.Utf8.parse(keyStr)
			const encrypted = CryptoJS.AES.encrypt(data, key, {
				mode: CryptoJS.mode.ECB,
				padding: CryptoJS.pad.Pkcs7,
			})
			return encrypted.toString()
		} catch (error) {
			return ''
		}
	}

	// aes解密
	static decrypt(data: string, index = 0): string {
		if (index < eks.length) {
			// 解密需要把所有的key都试一遍
			const keyStr = eks[index]
			try {
				const key = CryptoJS.enc.Utf8.parse(keyStr)
				const decryptStr = CryptoJS.AES.decrypt(data, key, {
					mode: CryptoJS.mode.ECB,
					padding: CryptoJS.pad.Pkcs7,
				})
				const res = CryptoJS.enc.Utf8.stringify(decryptStr)
				if (res) {
					return res.toString()
				} else {
					return this.decrypt(data, index + 1)
				}
			} catch (error) {
				return this.decrypt(data, index + 1)
			}
		} else {
			return ''
		}
	}
	// 判断是否为JSON格式加密接口
	static isJSONEncryptUrl(url: string) {
		return jsonEncryptWhiteUrl.indexOf(url) !== -1
	}
	// 判断是否为String格式加密接口
	static isStrEncryptUrl(url: string) {
		return strEncryptWhiteUrl.indexOf(url) !== -1
	}
}
