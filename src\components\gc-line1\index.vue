<template>
	<div class="gc-line1" id="chart" ref="chartDom"></div>
</template>
<script lang="ts" setup>
import { defineProps, onMounted, ref, watch } from 'vue'
import options from './options.ts'
import * as echarts from 'echarts'

defineOptions({ name: 'GcLine1' })
const props = defineProps({
	legend: {
		type: Array,
		default() {
			return ['2025年6月']
		},
	},
	modelValue: {
		type: Array,
		default() {
			return [
				{ yValue1: Math.ceil(Math.random() * 20), xValue: '01' },
				{ yValue1: Math.ceil(Math.random() * 120), xValue: '02' },
				{ yValue1: Math.ceil(Math.random() * 150), xValue: '03' },
				{ yValue1: Math.ceil(Math.random() * 200), xValue: '04' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '05' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '06' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '07' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '08' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '09' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '10' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '11' },
				{ yValue1: Math.ceil(Math.random() * 250), xValue: '12' },
			]
		},
	},
})
// echart dom
const chartDom = ref<null | HTMLElement>(null)
// echart 实例
let myChart: any = null
const currentValue = ref(props.modelValue)
onMounted(() => {
	init()
})
watch(
	() => props.modelValue,
	val => {
		currentValue.value = val
		init()
	},
)
const init = () => {
	chartDom.value && !myChart && (myChart = echarts.init(chartDom.value))
	myChart.setOption(options(currentValue.value, props))
}
</script>
<style lang="scss" scoped>
.gc-line1 {
	height: 448rpx;
	width: 654rpx;
}
</style>
