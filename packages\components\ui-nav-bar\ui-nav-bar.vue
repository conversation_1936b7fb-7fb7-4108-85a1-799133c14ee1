<template>
	/* #ifndef H5 */
	<view class="ui-nav-bar" :style="{ zIndex: zIndex }">
		<view class="ui-nav-bar-container" :class="initClass()" :style="containerStyle">
			<view class="ui-nav-bar-content">
				<view class="ui-nav-bar-left" @click="handleLeftClick">
					<!-- 支付宝小程序左上角返回按钮无法去除 -->
					/* #ifndef MP-ALIPAY */
					<slot name="left">
						<i class="ui-nav-bar-icon ui-nav-bar-icon-left" :class="chooseLeftIconClass()"></i>
						<text class="ui-nav-bar-text">{{ leftText }}</text>
					</slot>
					/* #endif */
				</view>
				<view :class="[titlePosition == 'center' ? 'ui-nav-bar-title-center' : 'ui-nav-bar-position-left']">
					<slot name="title">
						<text>{{ title || '' }}</text>
					</slot>
				</view>
				<view class="ui-nav-bar-right" @click="handleRightClick">
					<slot name="right">
						<text class="ui-nav-bar-text">{{ rightText }}</text>
						<i
							class="ui-nav-bar-icon ui-nav-bar-icon-right"
							:class="[props.iconfontClass, props.rightIcon]"
						></i>
					</slot>
				</view>
			</view>
		</view>
	</view>
	/* #endif */ /* #ifdef H5 */
	<view></view>
	/* #endif */
</template>

<script setup lang="ts">
import { computed, nextTick } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import type { NavBarProps } from './types'

defineOptions({
	name: 'UiNavBar',
})

const props = withDefaults(defineProps<NavBarProps>(), {
	title: '',
	leftText: '返回',
	rightText: '',
	leftIcon: undefined,
	rightIcon: '',
	fixed: false,
	zIndex: 99,
	type: 'light',
	iconfontClass: 'iconfont',
	clickable: true,
	background: '',
	safeAreaInsetTop: true,
	titlePosition: 'center',
})

const emits = defineEmits(['click'])

const containerStyle = computed(() => {
	const hasMenuButton = !!uni.getMenuButtonBoundingClientRect
	const menuButtonInfo = hasMenuButton ? uni.getMenuButtonBoundingClientRect() : { height: 44 }
	const menuButtonTop = hasMenuButton ? `${menuButtonInfo.top * 2}rpx` : 'env(safe-area-inset-top)'
	const menuButtonHeight = `${menuButtonInfo.height * 2}rpx`

	// 开启fixed时，默认开启安全区域
	if (!props.fixed && !props.safeAreaInsetTop) {
		return { height: menuButtonHeight, background: props.background }
	}
	return {
		height: `calc(${menuButtonHeight} + ${menuButtonTop})`,
		paddingTop: menuButtonTop,
		background: props.background,
	}
})

onShow(() => {
	/* #ifdef H5 */
	nextTick(() => {
		document.title = props.title || ''
	})
	/* #endif */
})

const handleLeftClick = () => {
	if (!props.clickable) {
		return
	}

	emits('click-left')
}

const handleRightClick = () => {
	if (!props.clickable) {
		return
	}

	emits('click-right')
}

const initClass = () => {
	const { type, fixed, safeAreaInsetTop } = props

	return [type, fixed ? 'fixed' : '', safeAreaInsetTop ? 'safe-top-area' : ''].filter(item => !!item)
}

const chooseLeftIconClass = () => {
	const { leftIcon, iconfontClass } = props

	return [
		leftIcon !== undefined ? leftIcon : 'icon-uniapp-arrow-left-s-line',
		leftIcon !== undefined ? iconfontClass : 'iconfont-uniapp',
	]
}
</script>
<style scoped lang="scss">
@use './ui-nav-bar.scss';
</style>
