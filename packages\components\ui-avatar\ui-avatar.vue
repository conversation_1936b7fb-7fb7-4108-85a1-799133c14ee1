<template>
	<view class="ui-avatar" :class="classNames" @click="handleClick">
		<slot>
			<image v-if="src" class="ui-avatar-image" :src="src" :mode="fit" @error="handleImageError"></image>
			<template v-else-if="text">
				{{ text }}
			</template>
			<ui-icon v-else class="ui-avatar-icon" customClass="ui-avatar-icon" :name="icon"></ui-icon>
		</slot>
	</view>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useMpOptions } from '../../hooks/useMpOptions'
import UiIcon from '../ui-icon/ui-icon.vue'
import { SizeEnum } from '../../shared/enum'

import type { AvatarProps } from './types'
import { ImageFitEnum } from './types'

defineOptions({
	name: 'uiAvatar',
	...useMpOptions(),
})

const props = withDefaults(defineProps<AvatarProps>(), {
	size: SizeEnum.md,
	//  'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
	src: '',
	text: '',
	icon: 'user-fill',
	// 'scaleToFill' | 'aspectFit' | 'aspectFill' | 'widthFix' | 'heightFix' | 'top' | 'bottom' | 'center' | 'left' | 'right' | 'top left' | 'top right' | 'bottom left' | 'bottom right'
	fit: ImageFitEnum.aspectFill,
	customClass: '',
})

const emits = defineEmits(['error', 'click'])

const classNames = computed(() => {
	const classNames = []
	const { size, customClass } = props
	if (size) {
		classNames.push(`ui-avatar-${size}`)
	}

	if (customClass) {
		classNames.push(customClass)
	}

	return classNames
})

const handleImageError = event => {
	emits('error', event)
}

const handleClick = event => {
	emits('click', event)
}
</script>
<style lang="scss">
@import url('./ui-avatar.scss');
</style>
