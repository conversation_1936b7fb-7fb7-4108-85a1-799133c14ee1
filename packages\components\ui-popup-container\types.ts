export enum PopupPosition {
	top = 'top',
	bottom = 'bottom',
	center = 'center',
	left = 'left',
	right = 'right',
}

export interface PopupContainerProps {
	/**
	 * @description 是否开启动画
	 */
	animation?: boolean
	/**
	 * @description 弹出层位置
	 */
	position?: keyof typeof PopupPosition
	/**
	 * @description 是否允许点击遮罩关闭
	 */
	isMaskClick?: boolean
	/**
	 * @description 背景颜色
	 */
	backgroundColor?: string
	/**
	 * @description 遮罩背景颜色
	 */
	maskBackgroundColor?: string
	/**
	 * @description 四周圆角值（左上、右上、右下、左下）
	 */
	borderRadius?: [string, string, string, string]
	/**
	 * @description 显示遮罩
	 */
	maskShow?: boolean
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
}
