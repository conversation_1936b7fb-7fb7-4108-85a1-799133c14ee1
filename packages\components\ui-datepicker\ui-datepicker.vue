<template>
	<view class="ui-datepicker">
		<picker :fields="fields" mode="date" :value="currentValue" @change="handlerChange" @cancel="handlerCancel">
			<view class="ui-datepicker-value">
				<view class="uni-datepicker-current">{{ currentValue }}</view>
				<UiIcon name="arrow-down-s-fill"></UiIcon>
			</view>
		</picker>
	</view>
</template>
<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue'
import UiIcon from '../ui-icon/ui-icon.vue'
import { type DatePickerProps } from './types'
import dayjs from '../../lib/dayjs/esm'

const props = withDefaults(defineProps<DatePickerProps>(), {
	options: [],
	modelValue: '',
	type: 'day',
})
const emit = defineEmits(['update:modelValue', 'cancel', 'change'])
const handlerCancel = () => {
	emit('cancel')
}
const fields = ref(props.type)
const handlerChange = (e: any) => {
	currentValue.value = e.detail.value
	emit('update:modelValue', e.detail.value)
	emit('change', e.detail.value)
}
const getDate = (type?: string) => {
	let date = dayjs(new Date())
	if (type === 'start') {
		date = date.add(-10, 'year')
	} else if (type === 'end') {
		date = date.add(10, 'year')
	}
	if (fields.value === 'year') {
		return date.format('YYYY')
	}
	if (fields.value === 'month') {
		return date.format('YYYY-MM')
	}
	if (fields.value === 'day') {
		return date.format('YYYY-MM-DD')
	}
}
const currentValue = ref(props.modelValue || getDate())
</script>
<style lang="scss" scoped>
@use './ui-datepicker.scss';
</style>
