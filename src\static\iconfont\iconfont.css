@font-face {
  font-family: "iconfont";
  src: url('iconfont.eot?t=1753863230547'); /* IE9*/
  src: url('iconfont.eot?t=1753863230547#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url("iconfont.woff2?t=1753863230547") format("woff2"),
  url("iconfont.woff?t=1753863230547") format("woff"),
  url('iconfont.ttf?t=1753863230547') format('truetype'); /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
}

.iconfont {
  font-family: 'iconfont' !important;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


.icon-Lable-icon-L:before { content: "\ea01"; }
.icon-Select-icon-L:before { content: "\ea02"; }
.icon-add-line:before { content: "\ea03"; }
.icon-arrow-drop-down:before { content: "\ea04"; }
.icon-arrow-drop-up:before { content: "\ea05"; }
.icon-brush-line:before { content: "\ea06"; }
.icon-change-icon:before { content: "\ea07"; }
.icon-close:before { content: "\ea08"; }
.icon-eye-close:before { content: "\ea09"; }
.icon-eye-open:before { content: "\ea0a"; }
.icon-file-copy-line:before { content: "\ea0b"; }
.icon-help-icon:before { content: "\ea0c"; }
.icon-home-smile-fill:before { content: "\ea0d"; }
.icon-home-smile-line:before { content: "\ea0e"; }
.icon-info-icon:before { content: "\ea0f"; }
.icon-lock-icon:before { content: "\ea10"; }
.icon-map-pin-fill:before { content: "\ea11"; }
.icon-meter-selecte:before { content: "\ea12"; }
.icon-phone-icon:before { content: "\ea13"; }
.icon-qr-code-fill:before { content: "\ea14"; }
.icon-record-line:before { content: "\ea15"; }
.icon-refresh-line:before { content: "\ea16"; }
.icon-service-fill:before { content: "\ea17"; }
.icon-service-line:before { content: "\ea18"; }
.icon-sysicon:before { content: "\ea19"; }
.icon-user-3-fill:before { content: "\ea1a"; }
.icon-user-3-line:before { content: "\ea1b"; }
.icon-user-icon:before { content: "\ea1c"; }

