<template>
	<div class="ui-money" :class="`ui-money-${size}`">
		<div v-if="hasUnit" class="ui-money-unit" :class="`number-${type} unit-size-${size}`">{{ unit }}</div>
		<div class="ui-money-value" :class="`number-${type} value-size-${size}`">
			<slot />
		</div>
	</div>
</template>
<script lang="ts" setup>
import { defineProps } from 'vue'
import { type MoneyProps } from './types'

withDefaults(defineProps<MoneyProps>(), {
	type: 'normal',
	size: 'md',
	unit: '￥',
	hasUnit: true,
})
</script>
<style lang="scss" scoped>
@use './ui-money.scss';
</style>
