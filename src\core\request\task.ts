import request from './request'

export default class Task {
	static STATUS_WAITTING = 0 // 请求被创建未推送到任务列表
	static STATUS_READY = 1 // 推送到任务列表
	static STATUS_LOADING = 2 // 移出任务列表，开始请求
	static STATUS_RETRY = 3 // 错误重连
	static STATUS_FINISH = 4 // 请求已返回结果
	status: number
	params: any
	res: any
	success: boolean = false
	constructor(params: any) {
		this.params = params
		this.status = Task.STATUS_WAITTING
	}

	execut(): Promise<any> {
		return request(this.params)
	}

	then(): Task {
		return this
	}

	catch(): Task {
		return this
	}
}
