<template>
	<Layout
		title=""
		left-text=""
		nav-bar-background="transparent"
		layout-background="default"
		:currentIndex="2"
		@left-click="navigateBack"
	>
		<div class="page-content" v-html="noticeDetail.content"></div>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import type { MessageInfo } from '../homeTypings'
import { getNoticeDetail } from '@/api/home.api'

defineOptions({
	name: 'MessageDetail',
})

const noticeDetail = ref({} as MessageInfo)

// 页面加载完成
onLoad((options: any) => {
	if (options.id) {
		getNoticeInfoDetail(options.id)
	}
})

// 获取消息通知/活动推荐详情
const getNoticeInfoDetail = (id: number) => {
	const params = {
		id: id,
	}
	getNoticeDetail(params)
		.then(res => {
			noticeDetail.value = res || {}
		})
		.catch(() => {})
}

// 导航栏点击返回
const navigateBack = () => {
	uni.navigateBack()
}
</script>
<style lang="scss" scoped>
.page-header-wrap {
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}
</style>
