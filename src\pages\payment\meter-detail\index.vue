<template>
	<Layout
		title=""
		:leftArrow="true"
		left-text=""
		layout-background="paymentLinearGradient"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<view class="meter-detail-header">
				<view class="meter-detail-title">表具信息</view>
				<image class="page-image" src="/static/images/payment/pageImage.png"></image>
				<image class="meter-image" src="/static/images/payment/meterInfo.png"></image>
			</view>

			<view class="meter-detail-wrap" v-if="meterData">
				<view v-for="item in meterProps" :key="item.prop">
					<view class="meter-detail-item" v-if="meterData[item.prop]">
						<view class="meter-info-label">{{ item.label }}</view>
						<view class="meter-info-value">{{ meterData[item.prop] + (item.unit || '') }}</view>
					</view>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue'
import Layout from '@/layout/index.vue'

defineOptions({
	name: 'MeterList',
})

const meterData = ref<any>()
const meterProps = ref([
	{ label: '用户名', prop: 'userName' },
	{ label: '用户号', prop: 'userNo' },
	{ label: '用户地址', prop: 'userAddress' },
	{ label: '手机号', prop: 'contactPhone' },
	{ label: '表具编号', prop: 'meterNo' },
	{ label: '表具型号', prop: 'meterType' },
	{ label: '表具类型', prop: 'meterTypeDes' },
	{ label: '表具余额', prop: 'meterBalanceAmt', unit: '元' },
	{ label: '阶梯余量', prop: 'cycSurplus' },
	// { label: '通讯时间', prop: 'meterReadingTime' },
	// { label: '其他费用', prop: 'otherBalance' },
	// { label: '保险失效时间', prop: 'insuranceEndDate' },
	// { label: '转入气量（注：表具电子屏不显示转入气量）', prop: 'initMeterReading' },
	{ label: '最后计费时间', prop: 'lastSettleDate' },
	{ label: '最后计费表底', prop: 'lastMeterReading' },
])

onMounted(() => {
	const instance = getCurrentInstance()?.proxy as any
	const eventChannel = instance.getOpenerEventChannel()
	eventChannel.on('meterDetail', function (data: any) {
		if (data) {
			const { userInfo = {}, meterInfo = {} } = data
			let result = {
				...userInfo,
				...meterInfo,
			}
			meterData.value = result
		}
	})
})

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	// todo 不知道怎么切图，所以后面还要改
	.meter-detail-header {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		justify-content: space-between;
		position: relative;
		.meter-detail-title {
			height: 160rpx;
			line-height: 160rpx;
			padding: 0 24rpx;
			font-weight: 600;
			font-size: var(--font-title-xlg);
		}
		.page-image {
			width: 300rpx;
			height: 116rpx;
		}
		.meter-image {
			width: 128rpx;
			height: 128rpx;
			position: absolute;
			right: 24rpx;
			bottom: 16rpx;
		}
	}

	.meter-detail-wrap {
		border-radius: var(--content-default-radius-md);
		padding: var(--content-default-padding-xs) 0;
		background-color: var(--light-fill-blank);
		.meter-detail-item {
			padding: 24rpx var(--content-default-padding-md);
			display: flex;
			flex-direction: row;
			justify-content: space-between;
			.meter-info-label {
				font-size: var(--font-body-md);
				color: var(--light-form-normal-default-secondary);
				margin-right: 20rpx;
			}
			.meter-info-value {
				font-weight: 600;
				font-size: var(--font-body-md);
				color: var(--light-form-normal-default-text);
				text-align: right;
				flex: 1;
			}
		}
	}
}
</style>
