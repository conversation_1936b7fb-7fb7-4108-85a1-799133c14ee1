<template>
	<UiPopupContainer ref="popupRef" position="center" :z-index="zIndex" @change="handleChange">
		<view class="ui-popup-message">
			<view class="ui-popup-message-box fixforpc-width" :class="'ui-popup-' + _type">
				<slot>
					<text class="ui-popup-message-text" :class="'ui-popup-' + _type + '-text'">
						{{ _message }}
					</text>
				</slot>
			</view>
		</view>
	</UiPopupContainer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { MessageProps, MessageExpose } from './types'
import { MessageType } from './types'
import UiPopupContainer from '../ui-popup-container/ui-popup-container.vue'

defineOptions({
	name: 'UiMessage',
})

const props = withDefaults(defineProps<MessageProps>(), {
	duration: 3000,
})

const emits = defineEmits(['change'])

let timer: any = null
const popupRef = ref()
const _message = ref(props.message)
const _type = ref(props.type)

const handleChange = ({ show }: { show: boolean }) => {
	emits('change', show)
}

const openMessage = (type: string, message: string) => {
	popupRef.value.open()
	_message.value = message
	_type.value = type
	handleChange({ show: true })
	countClose()
}
const countClose = () => {
	if (props.duration < 1) {
		return
	}
	if (!!timer) {
		clearTimeout(timer)
	}
	timer = setTimeout(() => {
		popupRef.value.close()
		handleChange({ show: false })
	}, props.duration)
}

defineExpose<MessageExpose>({
	open({ type, message }: { type: keyof typeof MessageType; message: string }) {
		openMessage(type, message)
	},
	success(message: string) {
		openMessage(MessageType.success, message)
	},
	error(message: string) {
		openMessage(MessageType.error, message)
	},
	warning(message: string) {
		openMessage(MessageType.warning, message)
	},
	info(message: string) {
		openMessage(MessageType.info, message)
	},
	close() {
		popupRef.value.close()
		handleChange({ show: false })
	},
})
</script>
<style scoped lang="scss">
@use './ui-message.scss';
</style>
