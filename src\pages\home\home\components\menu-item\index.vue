<template>
	<view class="menu-item-wrap" :style="{ width: (100 * 1.0) / 4 + 'vw' }" @click="menuClick">
		<image
			class="menu-item-icon"
			:style="{ opacity: menuInfo.status == 1 ? 1.0 : 0.2 }"
			:src="menuInfo.icon"
		></image>
		<view class="menu-item-title">{{ menuInfo.title }}</view>
	</view>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import type { MenuInfo } from '../../homeTypings'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'MenuItem',
})

const props = defineProps({
	// 菜单信息
	menuInfo: {
		type: Object as PropType<MenuInfo>,
		default: {},
	},
})

const emits = defineEmits(['menuClick'])

const menuClick = () => {
	if (props.menuInfo.status == 1) {
		emits('menuClick', props.menuInfo)
	} else {
		showMsg('该功能暂未开通')
	}
}

defineExpose({})
</script>

<style lang="scss" scoped>
.menu-item-wrap {
	flex-shrink: 0;
	height: 148rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	.menu-item-icon {
		width: 96rpx;
		height: 96rpx;
	}
	.menu-item-title {
		font-size: var(--font-body-md);
		color: var(--light-text-title);
		margin-top: var(--content-default-margin-xs);
	}
}
</style>
