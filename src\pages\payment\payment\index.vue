<template>
	<Layout
		title="综合缴费"
		:leftArrow="true"
		left-text=""
		layoutBackground="paymentLinearGradient"
		:scrollTop="scrollTop"
		@left-click="navigateBack"
	>
		<view class="page-content" id="page-content">
			<UserInfoCard
				ref="userInfoCard"
				pageType="payment"
				type="payment"
				:userNo="userNo"
				:addressId="addressId"
				@changeUser="changeUserClick"
				@getUserInfo="handleGetUserInfo"
			>
				<template #action>
					<UiButton
						type="lighter"
						size="md"
						width="100%"
						style-name="dark"
						text="缴费记录"
						icon="icon-record-line"
						@click="payRecordClick"
					></UiButton>
				</template>
			</UserInfoCard>
			<view class="cell-list-wrap">
				<view class="account-info-wrap">
					<view class="info-item">
						<image class="info-icon" src="/static/images/payment/account-money-icon.png"></image>
						<view class="info-item-content">
							<view class="info-item-title">账户余额</view>
							<UiMoney size="sm">{{ billInfoRes.accountBalance || '0' }}</UiMoney>
						</view>
					</view>
					<view class="line"></view>
					<view class="info-item" @click="meterListClick">
						<image class="info-icon" src="/static/images/payment/meter-icon.png"></image>
						<view class="info-item-content">
							<view class="content-top">
								<view class="info-item-title">我的表具</view>
								<i class="iconfont icon-sysicon right-arrow"></i>
							</view>
							<UiMoney size="sm" :hasUnit="false">{{ meterCount }}</UiMoney>
						</view>
					</view>
				</view>
				<view class="bill-total-wrap" @click="billTotalClick">
					<UiCell title="账单总额" value="0.00元" :arrow="true">
						<template #value>
							<view class="cell-content-wrap">
								<view class="money-unit">￥</view>
								<UiNumber
									weight="normal"
									:value="billInfoRes.payableTotalAmt || '0.00'"
									valueSize="40rpx"
									unitSize="30rpx"
									gap="0"
									unit="￥"
								></UiNumber>
								<UiTag
									type="default"
									size="sm"
									:text="(billInfoRes.feeRecordCount || '0') + '笔'"
								></UiTag>
							</view>
						</template>
					</UiCell>
				</view>
			</view>

			<payment-input
				ref="payInput"
				:meter="currentMeter"
				:userNo="userInfo.userNo"
				:totalMoney="billInfoRes.payableTotalAmt"
				@meterChange="meterChangeClick"
				@scrollToBottom="scrollToBottom"
			></payment-input>
			<view class="foot-wrap-height"></view>
		</view>
		<view class="payment-foot-wrap">
			<view class="foot-left-wrap">
				<view class="foot-left-label">合计：</view>
				<view class="foot-money-icon">￥</view>
				<view class="foot-money-value">{{ payMoney || '0' }}</view>
			</view>
			<view class="pay-btn">
				<UiButton
					type="primary"
					size="lg"
					width="100%"
					style-name="light"
					text="立即缴费"
					@click="paymentClick"
				></UiButton>
			</view>
		</view>
		<UiPopup v-model="billDetailShow" ref="popupRef" position="bottom" round is-mask-click title="账单明细">
			<bill-list-popup :billList="billList" @cancel="billListPopCancel"></bill-list-popup>
		</UiPopup>
		<UiPopup v-model="meterListShow" ref="popupRef" position="bottom" round is-mask-click title="余额充值">
			<meter-list-popup
				:meterListRes="meterListRes"
				:currentMeter="currentMeter"
				@selectedMeter="selectedMeter"
			></meter-list-popup>
		</UiPopup>
		<UiPopup v-model="payPopupShow" ref="popupRef" position="bottom" round is-mask-click title="缴费明细">
			<pay-popup
				ref="payPopupRef"
				:billInfo="billInfoRes"
				:meterInfo="currentMeter"
				:payAmount="payMoney"
				@cancel="payPopupCancel"
				@paySubmit="paySubmit"
			></pay-popup>
		</UiPopup>
	</Layout>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import PayPopup from '../components/pay-popup/index.vue'
import BillListPopup from '../components/bill-list-popup/index.vue'
import MeterListPopup from '../components/meter-list-popup/index.vue'
import PaymentInput from '../components/payment-input/index.vue'
import UiTag from '@e-cloud/eslink-plus-uniapp/components/ui-tag/ui-tag.vue'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiPopup from '@e-cloud/eslink-plus-uniapp/components/ui-popup/ui-popup.vue'
import UiMoney from '@e-cloud/eslink-plus-uniapp/components/ui-money/ui-money.vue'
import UiNumber from '@e-cloud/eslink-plus-uniapp/components/ui-number/ui-number.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import { usmartAndPbApi, queryMeterList, payUnifiedOrder } from '@/api/payment.api'
import { getPrePayParams } from '@/core/pay/payUtils'
import { showMsg } from '@/core/util'
import weChatPay from '@/core/pay/useWechatPay'
import type { UserInfo, MeterInfo } from '@/pages/commonTypes'
import type { BillInfoRes, MeterListRes, BillInfoGroup } from '../paymentTypings'

defineOptions({
	name: 'Payment',
})

const userInfoCard = ref() // 用户信息模块
const payInput = ref() // 输入模块
const payPopupRef = ref() // 缴费二次确认弹窗
const userNo = ref('')
const addressId = ref('')
const scrollTop = ref(0) // 页面滚动位置
const billInfoRes = ref({} as BillInfoRes) // 账单信息
const billDetailShow = ref(false) // 账单详情弹窗
const meterListShow = ref(false) // 表具列表弹窗
const payPopupShow = ref(false) // 缴费二次确认弹窗
// 表具列表返回值
const meterListRes = ref({} as MeterListRes)
// 表具列表
const meterList = ref([] as MeterInfo[])
const billList = ref([] as BillInfoGroup[])

// 当前选中的表具
let currentMeter = ref({} as MeterInfo)
const { weixinPay } = weChatPay()

// 用户信息
const userInfo = computed(() => {
	if (userInfoCard && userInfoCard.value) {
		return userInfoCard.value.userInfo || {}
	} else {
		return {}
	}
})

// 表具数量
const meterCount = computed(() => {
	let count = 0
	if (meterList.value && Array.isArray(meterList.value)) {
		count = meterList.value.length
	}
	return count
})

// 输入的缴费金额
const payMoney = computed(() => {
	if (payInput && payInput.value) {
		return payInput.value.payMoney
	} else {
		return '0'
	}
})

onLoad((options: any) => {
	if (options.userNo && options.addressId) {
		userNo.value = options.userNo
		addressId.value = options.addressId
	}
})
// userInfoCard获取到用户信息回调
const handleGetUserInfo = (userInfo: UserInfo) => {
	userNo.value = userInfo.userNo
	addressId.value = userInfo.addressId
	getBillList()
	getMeterList()
}
// 查询账单列表
const getBillList = () => {
	let params = { userNo: userNo.value, addressId: addressId.value }
	usmartAndPbApi(params)
		.then((res: BillInfoRes) => {
			if (res) {
				billInfoRes.value = res
				if (res.feeRecordList && res.feeRecordList.length > 0) {
					billList.value = res.feeRecordList
				}
			} else {
				showMsg('未知错误')
			}
		})
		.catch(() => {})
}

// 查询表具列表
const getMeterList = () => {
	queryMeterList({ userNo: userNo.value, addressId: addressId.value })
		.then((res: MeterListRes) => {
			console.log('res', res)
			meterListRes.value = res
			if (res && Array.isArray(res.meterList)) {
				meterList.value = res.meterList
				if (res.meterList.length > 0) {
					console.log('res.meterList[0]', res.meterList[0])
					currentMeter.value = res.meterList[0]
				}
			}
		})
		.catch(() => {})
}
// 切换户号成功
const changeUserClick = (user: UserInfo) => {
	userNo.value = user.userNo
	addressId.value = user.addressId
}
// 缴费记录点击
const payRecordClick = (event: any) => {
	uni.navigateTo({
		url: '/pages/pay-record/record-list/index',
	})
}
// 我的表具点击
const meterListClick = () => {
	uni.navigateTo({
		url:
			'/pages/payment/meter-list/index?userNo=' +
			userInfo.value.userNo +
			'&addressId=' +
			userInfo.value.addressId,
	})
}
// 账单总额点击
const billTotalClick = () => {
	if (billList.value.length > 0) {
		billDetailShow.value = true
	} else {
		showMsg('暂无账单')
	}
}
// 账单总额弹窗关闭
const billListPopCancel = () => {
	billDetailShow.value = false
}
// 表具选择点击
const meterChangeClick = () => {
	meterListShow.value = true
}
// 表具选择确定
const selectedMeter = (meter: MeterInfo) => {
	currentMeter.value = meter
	meterListShow.value = false
}
// 立即缴费点击
const paymentClick = () => {
	if (!payInput.value.payMoney) {
		showMsg('请输入金额')
		return
	}
	if (
		billInfoRes.value.payableTotalAmt &&
		Number(payInput.value.payMoney) < Number(billInfoRes.value.payableTotalAmt)
	) {
		showMsg('缴费金额不能小于账单金额')
		return
	}
	// 适配微信小程序，微信小程序在页面一加载popup就存在了，initData方法也调过了，所以弹窗展示都是空的，公众号是在弹窗展示的时候页面才会存在
	if (payPopupRef && payPopupRef.value) {
		payPopupRef.value.initData()
	}
	payPopupShow.value = true
}
// 缴费二次弹窗关闭
const payPopupCancel = () => {
	payPopupShow.value = false
}
// 二次弹窗确认缴费
const paySubmit = () => {
	const params = getPrePayParams(userInfo.value, billInfoRes.value, currentMeter.value, payInput.value.payMoney)
	payUnifiedOrder(params)
		.then(res => {
			// 调起支付
			if (res && res.prepayCode) {
				let prepayCode = res.prepayCode
				weixinPay(prepayCode, userInfo)
				// 关闭弹窗
				payPopupShow.value = false
			} else {
				showMsg('下单失败', res.result)
			}
		})
		.catch(() => {})
}

// 金额改变量因为下面多动态展示组件，因此需要页面滚动到底部
const instance = getCurrentInstance()
const scrollToBottom = () => {
	const query = uni.createSelectorQuery().in(instance)
	query
		.select('#page-content')
		.boundingClientRect((rect: any) => {
			nextTick(() => {
				if (rect && rect.height) {
					scrollTop.value = rect.height // 滚动到底部
				}
			})
		})
		.exec()
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss">
.page-content {
	.cell-list-wrap {
		margin-top: 24rpx;
		border-radius: var(--content-default-radius-md);
		.account-info-wrap {
			border-radius: var(--content-default-radius-md);
			background: var(--light-fill-blank);
			display: flex;
			flex-direction: row;
			align-items: center;
			gap: var(--content-default-margin-sm);
			margin-top: var(--content-default-margin-md);
			.info-item {
				flex: 1;
				padding: var(--content-default-padding-md) var(--content-default-padding-sm);
				display: flex;
				flex-direction: row;
				.info-icon {
					width: 48rpx;
					height: 48rpx;
					margin-right: var(--content-default-margin-sm);
				}
				.info-item-content {
					flex: 1;
					.content-top {
						display: flex;
						flex-direction: row;
						justify-content: space-between;
						align-items: center;
						height: 48rpx;
						.info-item-title {
							font-size: var(--font-body-md);
							color: var(--light-form-normal-default-secondary);
						}
						.right-arrow {
							color: var(--light-icon-light);
						}
					}
					.unit-size-sm {
						font-size: var(--font-body-md);
						padding-top: 0;
					}
				}
			}
			.line {
				width: 2rpx;
				height: 64rpx;
				background: var(--light-fill-light);
			}
		}
		.bill-total-wrap {
			height: 120rpx;
			border-radius: var(--content-default-radius-md);
			padding: var(--content-default-padding-sm) 0;
			margin-top: var(--content-default-margin-md);
			background-color: var(--light-fill-blank);
			.cell-content-wrap {
				display: flex;
				flex-direction: row;
				align-items: center;
			}
		}
		.no-icon {
			padding: 0 var(--content-default-padding-md);
		}
	}

	.foot-wrap-height {
		height: 128rpx;
	}
}
.payment-foot-wrap {
	width: 100%;
	height: 128rpx;
	padding: 0 var(--content-default-padding-md);
	background: var(--light-fill-blank);
	position: fixed;
	bottom: 0;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	.foot-left-wrap {
		display: flex;
		flex-direction: row;
		align-items: flex-end;
		height: 64rpx;
		.foot-left-label {
			font-size: var(--font-body-sm);
			color: var(--light-text-light);
			padding-bottom: 12rpx;
		}
		.foot-money-icon {
			font-weight: 600;
			font-size: var(--font-body-sm);
			color: var(--light-text-subcolor);
			padding-bottom: 12rpx;
		}
		.foot-money-value {
			font-size: var(--font-title-xlg);
			color: var(--light-text-subcolor);
		}
	}
	.pay-btn {
		width: 208rpx;
	}
}
</style>
