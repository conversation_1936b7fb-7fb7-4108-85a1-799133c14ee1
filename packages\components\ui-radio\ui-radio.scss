.ui-radio {
	box-sizing: border-box;
	display: flex;
	align-items: center;
}

.ui-radio-icon {
	box-sizing: border-box;
	width: 40rpx;
	height: 40rpx;
	transition: all 0.3s ease-out;
}

.ui-radio-icon-dot,
.ui-radio-icon-check {
	display: flex;
	align-items: center;
	justify-content: center;
	border-width: 2rpx;
	border-style: solid;
	border-radius: var(--radius-circle);
	background: var(--light-form-normal-default-background);
	border-color: var(--light-button-disable-normal-border);

	.ui-radio-icon-inner {
		width: 20rpx;
		height: 20rpx;
		border-radius: var(--radius-circle);
	}

	&.checked {
		border-color: var(--light-form-normal-focus-border);
		background: var(--light-form-normal-focus-background-choice);

		.ui-radio-icon-inner {
			background: var(--light-form-normal-focus-icon-primary);
		}
	}
	&.disabled {
		border: 1px solid var(--light-form-normal-disable-border)
	}
}

.ui-radio-icon-check {
	&.checked {
		background: var(--light-primary-default);
	}
	&.disabled {
		background: var(--light-form-normal-disable-background);
	}
}

.ui-radio-text {
	color: var(--light-form-normal-focus-label);
	font-weight: 400;
	font-size: var(--font-body-md);
}

.ui-radio-text:not(:empty) {
	margin-left: var(--content-default-margin-sm);
}

.ui-radio-check-icon-inner {
	font-size: 40rpx;
	color: #fff;
	font-weight: 300;
}
