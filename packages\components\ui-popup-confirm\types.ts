export enum ConfirmType {
	success = 'success',
	error = 'error',
	warning = 'warning',
	info = 'info',
}

export interface PopupConfirmProps {
	/**
	 * @description 是否显示
	 */
	modelValue?: boolean
	/**
	 * @description 确认框类型
	 */
	type?: keyof typeof ConfirmType
	/**
	 * @description 确认框标题
	 */
	title?: string
	/**
	 * @description 确认框内容
	 */
	message?: string
	/**
	 * @description 确认框取消按钮文字
	 */
	cancelText?: string
	/**
	 * @description 确认框确认按钮文字
	 */
	confirmText?: string
	/**
	 * @description 是否显示取消按钮
	 */
	showCancalButton?: boolean
	/**
	 * @description 是否显示确认按钮
	 */
	showConfirmButton?: boolean
	/**
	 * @description 是否拦截按钮事件
	 */
	beforeClose?: (() => void) | (() => Promise<unknown>) | null
	/**
	 * @description 确认框宽度
	 */
	width?: string
	/**
	 * @description 显示遮罩
	 */
	maskShow?: boolean
	/**
	 * @description 显示层级
	 */
	zIndex?: number | string
}
