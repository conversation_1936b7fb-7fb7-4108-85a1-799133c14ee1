<template>
	<Layout
		title="我的户号"
		left-text=""
		layoutBackground="paymentLinearGradient"
		nav-bar-background="transparent"
		@left-click="navigateBack"
	>
		<view class="page-content">
			<view class="tabs-wrap">
				<UiTabs v-model="activeTab" :list="tabList" size="md" type="round" @click="handleTabClick"></UiTabs>
			</view>
			<binding-list-item
				class="binding-list"
				v-for="item in bindingList"
				:key="item.userId"
				:dataItem="item"
				:pageType="pageType"
				@itemClick="itemClick"
				@setDefaultUser="setDefaultUser"
				@refresh="getBindUserList"
			></binding-list-item>
			<view class="padding-bottom-view"></view>
			<UiFooterBtns :btnList="btnList"></UiFooterBtns>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import BindingListItem from '../components/binding-list-item/index.vue'
import UiTabs from '@e-cloud/eslink-plus-uniapp/components/ui-tabs/ui-tabs.vue'
import UiFooterBtns from '@e-cloud/eslink-plus-uniapp/components/ui-footer-btns/ui-footer-btns.vue'
import { getBindUserListApi, setDefaultUserApi } from '@/api/userBind.api'
import type { UserInfo } from '@/pages/commonTypes'
import { onShow, onLoad } from '@dcloudio/uni-app'

defineOptions({
	name: 'BindingListPage',
})

const bindingList = ref([] as UserInfo[])
const activeTab = ref('2')

const tabList = ref([
	{ label: '居民户', value: '2' },
	{ label: '工商户', value: '1' },
])
const pageConfig = {
	page: 1,
	pageSize: 10,
}
const btnList = ref([
	{
		text: '绑定户号',
		type: 'secondary',
		size: 'lg',
		click: () => {
			uni.navigateTo({
				url: '/pages/binding/binding-user/index',
			})
		},
	},
])
const pageType = ref('binding') // 页面类型 -- 分为绑定（binding）和选择用户（selectUser）
const selectedUserNo = ref('')
let eventChannel = undefined as any

// 页面加载完成
onLoad((options: any) => {
	// 页面类型
	if (options.pageType) {
		pageType.value = options.pageType
	}
	// 默认选中的用户
	if (options.userNo) {
		selectedUserNo.value = options.userNo
	}
})

onMounted(() => {
	const instance = getCurrentInstance()?.proxy as any
	eventChannel = instance.getOpenerEventChannel()
})
// 刷新列表
onShow(() => {
	getBindUserList()
})

// tab切换
const handleTabClick = (value: string) => {
	activeTab.value = value
	getBindUserList()
}

// 设置默认用户
const setDefaultUser = (item: UserInfo) => {
	setDefaultUserApi(String(item.id))
		.then(() => {
			bindingList.value.forEach((user: UserInfo) => {
				user.defaultUser = 0
			})
			const defaultUser = bindingList.value.find(user => user.userId == item.userId)
			if (defaultUser) {
				defaultUser.defaultUser = 1
			}
		})
		.catch(() => {})
}

// 获取绑定用户列表
const getBindUserList = () => {
	const params = {
		needPage: true,
		userType: activeTab.value,
		pageNum: pageConfig.page,
		pageSize: pageConfig.pageSize,
	}
	getBindUserListApi(params)
		.then(res => {
			if (res && res.userBindList) {
				bindingList.value = res.userBindList
				bindingList.value.forEach((item: UserInfo) => {
					if (item.userNo == selectedUserNo.value) {
						item.selected = true
					}
				})
			} else {
				bindingList.value = []
			}
		})
		.catch(() => {})
}

// item点击
const itemClick = (item: UserInfo) => {
	if (pageType.value != 'binding') {
		bindingList.value.forEach(item => {
			item.selected = false
		})
		item.selected = true
		// 回传选中的item
		eventChannel.emit('selectCallBack', item)
		uni.navigateBack()
	}
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	// padding: 24rpx;
}
.tabs-wrap {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.padding-bottom-view {
	height: 144rpx;
	width: 100%;
}
.binding-btn-wrap {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: var(--light-bg-base);
	display: flex;
	flex-direction: row;
	align-items: center;
	padding: var(--content-default-padding-md);
}
</style>
