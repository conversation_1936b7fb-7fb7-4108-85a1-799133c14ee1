<template>
	<view class="ui-step">
		<view :class="`ui-step-${type}`">
			<view
				:class="{ 'ui-step-item-active': current === index + 1, 'ui-step-item-focus': current >= index + 1 }"
				class="ui-step-item"
				v-for="(item, index) in list"
				:key="index"
			>
				<view class="ui-step-item-success" v-if="index + 1 < current"></view>
				<view class="ui-step-item-index" v-if="index + 1 >= current">{{ index + 1 }}</view>
				<view class="ui-step-item-text">{{ item }}</view>
				<view class="ui-step-item-line-left"></view>
				<view class="ui-step-item-line-right"></view>
			</view>
		</view>
	</view>
</template>
<script lang="ts" setup>
defineOptions({
	name: 'UiStep',
})
const props = withDefaults(
	defineProps<{
		list: string[]
		type: 'horizontal' | 'vertical'
		current: number
	}>(),
	{
		list: ['步骤1', '步骤2'],
		type: 'horizontal',
		current: 1,
	},
)
</script>
<style scoped lang="scss">
@use './ui-step.scss';
</style>
