<template>
	<view class="record-list-item-wrap" @click="itemClick">
		<image
			v-if="recordItem.payType === 'alipay'"
			class="type-icon"
			src="@/static/images/pay-record/alipay-icon.png"
		></image>
		<image
			v-if="recordItem.payType === 'wechat'"
			class="type-icon"
			src="@/static/images/pay-record/wechat-icon.png"
		></image>
		<view class="record-list-item-content">
			<UiTitle :title="recordItem.payTypeDesc || '--'" size="md" :noPadding="true">
				<template #subTitle>
					<view class="order-num-wrap">
						<view class="order-num-line"></view>
						<view class="order-num-value">{{ recordItem.orderNo || '--' }}</view>
					</view>
				</template>
			</UiTitle>
			<UiCell
				:title="recordItem.feeTypeDesc"
				:value="recordItem.fee"
				valuePrefix="￥"
				size="sm"
				:noPadding="true"
			></UiCell>
		</view>
	</view>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import type { PropType } from 'vue'
import type { RecordItem } from '../../payRecordTypings'
import UiCell from '@e-cloud/eslink-plus-uniapp/components/ui-cell/ui-cell.vue'
import UiTitle from '@e-cloud/eslink-plus-uniapp/components/ui-title/ui-title.vue'

defineOptions({
	name: 'RecordListItem',
})

const props = defineProps({
	// 缴费记录
	recordItem: {
		type: Object as PropType<RecordItem>,
		default: '',
	},
})

onMounted(() => {})

// item点击
const itemClick = () => {
	uni.navigateTo({
		url: '/pages/pay-record/record-detail/index',
	})
}
</script>
<style lang="scss" scoped>
.record-list-item-wrap {
	background-color: var(--light-fill-blank);
	border-radius: var(--content-default-radius-md);
	padding: var(--content-default-padding-sm) var(--content-default-padding-lg);
	display: flex;
	flex-direction: row;
	gap: var(--content-default-margin-sm);
	margin-top: 24rpx;
	&:first-child {
		margin-top: 0;
	}
	.type-icon {
		width: 64rpx;
		height: 64rpx;
		margin-top: 24rpx;
	}
	.record-list-item-content {
		flex: 1;
		.order-num-wrap {
			display: flex;
			flex-direction: row;
			align-items: center;
			.order-num-line {
				width: 2rpx;
				height: 24rpx;
				background-color: #cbd1e1;
			}
			.order-num-value {
				margin-left: var(--list-md-margin);
			}
		}
		.tag-wrap {
			width: 130rpx;
		}
	}
}
</style>
