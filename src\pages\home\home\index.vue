<template>
	<Layout
		title="金卡燃气营业厅"
		show-tabbar
		:currentIndex="0"
		scroll
		layout-background="homeLinearGradient"
		type="light"
		left-text=""
		left-icon=""
		:right-text="demoBtn"
		titlePosition="left"
		@onReachBottom="onReachBottom"
		@right-click="handleRightClick"
	>
		<view class="no-padding-wrap">
			<view class="menu-list-wrap">
				<menu-item
					v-for="menu in menuList"
					:key="menu.menuId"
					:menuInfo="menu"
					@menuClick="menuClick"
				></menu-item>
			</view>
			<view class="page-content">
				<UserInfoCard
					ref="userInfoCard"
					:currentUser="currentUser"
					:userList="userList"
					@refresh="getBindUserList"
					@userTypeClick="userTypeClick"
				></UserInfoCard>
				<view class="business-menu-wrap">
					<view
						class="business-menu-item"
						v-for="(item, index) in businessMenuList"
						:key="index"
						@click="businessMenuClick(item)"
					>
						<image class="business-menu-icon" :src="item.icon"></image>
						<view class="business-menu-label">{{ item.title }}</view>
					</view>
				</view>
				<CardItem title="精选活动" more @moreClick="activityMoreClick('4')">
					<swiper
						:style="{ height: '188rpx' }"
						circular
						:indicator-dots="true"
						:autoplay="true"
						:interval="3000"
						:duration="1000"
					>
						<swiper-item v-for="item in activitiesList" :key="item.id" style="overflow: auto">
							<image class="activities-image" :src="item.icon"></image>
						</swiper-item>
					</swiper>
				</CardItem>
				<CardItem title="最新消息" more @moreClick="activityMoreClick('1')">
					<MessageItem v-for="message in messageList" :key="message.id" :messageInfo="message"></MessageItem>
				</CardItem>
				<CardItem
					title="我的附近"
					more
					noPadding
					v-if="serviceHallList.length"
					@moreClick="serviceHallMoreClick"
				>
					<AddressItem v-for="address in serviceHallList" :key="address.id" :dataItem="address"></AddressItem>
				</CardItem>
			</view>
		</view>
	</Layout>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Layout from '@/layout/index.vue'
import MenuItem from './components/menu-item/index.vue'
import CardItem from './components/card-item/index.vue'
import UserInfoCard from './components/user-info-card/index.vue'
import MessageItem from './components/message-item/index.vue'
import AddressItem from './components/address-item/index.vue'
import type { MenuInfo, MessageInfo, ServiceHallInfo } from './homeTypings'
import type { UserInfo } from '@/pages/commonTypes'
import { getBindUserListApi } from '@/api/userBind.api'
import { getMenuList, getNoticeList, getServiceHallList } from '@/api/home.api'
import { showMsg } from '@/core/util'

defineOptions({
	name: 'HomePage',
	styleIsolation: 'shared',
})

uni.setNavigationBarTitle({
	title: '首页',
})

const userInfoCard = ref() // 用户信息模块
const demoBtn = import.meta.env.DEV ? ref('demo') : ref('')

const userList = ref([]) // 查询到的用户列表
const currentUser = ref({} as UserInfo) // 当前默认用户

const dataList = ref([])

const menuList = ref<MenuInfo[]>([])

const businessMenuList = ref<MenuInfo[]>([])

// 精选活动
const activitiesList = ref<MessageInfo[]>([])

// 最新消息
const messageList = ref<MessageInfo[]>([])
// 附近营业厅
const serviceHallList = ref<ServiceHallInfo[]>([])

const location = ref()

onMounted(() => {
	// 初始化页面数据
	initPage()
})

let layout = ref()

// 初始化页面数据
const initPage = () => {
	// 查询当前绑定的用户
	getBindUserList()
	// 查询首页菜单
	getMenuInfoList()
	// 查询精选活动
	getNoticeInfoList(4)
	// 查询消息通知
	getNoticeInfoList(1)
	// uni.getLocation({
	// 	type: 'gcj02',
	// 	success: function (res) {
	// 		debugger
	// 		location.value = res
	// 		// 获取附近营业厅列表
	// 		getServiceHallInfoList(res)
	// 	},
	// })
	const res = {
		latitude: 30.33071,
		longitude: 120.342893,
	}
	location.value = res
	getServiceHallInfoList(res)
}

// 获取绑定用户列表
const getBindUserList = () => {
	const params = {
		needPage: true,
		userType: userInfoCard.value.userType,
		pageNum: 1,
		pageSize: 10,
	}
	getBindUserListApi(params)
		.then(res => {
			if (res && res.userBindList) {
				userList.value = res.userBindList
				userList.value.forEach((item: UserInfo) => {
					if (item.defaultUser) {
						currentUser.value = item
					}
				})
				// 默认用户不存在，则取列表第一个
				if (!currentUser.value.userNo && userList.value.length > 0) {
					currentUser.value = userList.value[0]
				}
				userInfoCard.value.reSetSwiperHeight()
			} else {
				userList.value = []
			}
		})
		.catch(() => {})
}

// 查询首页菜单
const getMenuInfoList = () => {
	getMenuList({})
		.then((res: any) => {
			if (Array.isArray(res.mainMenus)) {
				menuList.value = res.mainMenus
			}
			if (Array.isArray(res.indexMenus)) {
				const list: MenuInfo[] = [
					...res.indexMenus,
					{
						title: '更多',
						icon: '/static/images/home/<USER>',
						url: '/pages/home/<USER>/index',
					},
				]
				businessMenuList.value = list
			}
		})
		.catch(() => {
			// debugger
		})
}

// 获取消息通知/活动推荐列表
const getNoticeInfoList = (type: number) => {
	const params = {
		type: type,
		pageNum: 1,
		pageSize: 2,
	}
	getNoticeList(params)
		.then(res => {
			if (res && Array.isArray(res.list)) {
				if (res.list.length > 0) {
					const firstItem = res.list[0]
					if (firstItem.type == '1') {
						messageList.value = res.list
					} else {
						activitiesList.value = res.list
					}
				}
			}
		})
		.catch(() => {})
}

// 获取附近营业厅列表
const getServiceHallInfoList = (location: any) => {
	const params = {
		coordinates: location.longitude + ',' + location.latitude,
	}
	getServiceHallList(params)
		.then(res => {
			if (Array.isArray(res)) {
				if (res.length > 2) {
					serviceHallList.value = res.slice(0, 2)
				} else {
					serviceHallList.value = res
				}
			}
		})
		.catch(() => {})
}
// 顶部菜单点击事件
const menuClick = (menu: MenuInfo) => {
	uni.navigateTo({
		url: menu.url,
	})
}

// 业务菜单点击事件
const businessMenuClick = (menu: MenuInfo) => {
	if (menu.url) {
		uni.navigateTo({
			url: menu.url,
		})
	} else {
		showMsg('该功能暂未开通')
	}
}

// 用户类型切换
const userTypeClick = () => {
	// 查询当前绑定的用户
	getBindUserList()
}
// 精选活动右箭头按钮点击
const activityMoreClick = (type: string) => {
	uni.navigateTo({
		url: '/pages/home/<USER>/message-center/index?type=' + type,
	})
}
// 我的附近右箭头按钮点击
const serviceHallMoreClick = () => {
	uni.navigateTo({
		url: '/pages/home/<USER>/service-hall/index?location=' + JSON.stringify(location.value),
	})
}
// 上拉加载更多demo
const onReachBottom = () => {
	console.log('触发上拉加载更多')
	if (dataList.value.length < 13) {
		// configList.forEach(item => {
		// 	dataList.value.push(item)
		// })
	} else {
		setTimeout(() => {
			layout.value.endBottomLoading('')
		}, 1000)
	}
}

const handleRightClick = () => {
	uni.navigateTo({
		url: '/pages/demo/index',
	})
	console.log('right text click')
}
</script>
<style lang="scss" scoped>
.menu-list-wrap {
	margin-top: 40rpx;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	overflow: auto;
}
.page-content {
	padding: 40rpx 24rpx 144rpx 24rpx;
	border-radius: 32rpx;
	.business-menu-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: var(--content-default-padding-lg) 0;
		border-radius: var(--content-default-radius-md);
		background: var(--light-fill-blank);
		margin-top: 24rpx;
		.business-menu-item {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-left: 20rpx;
			flex: 1;
			&:first-child {
				margin-left: 0;
			}
		}
		.business-menu-icon {
			width: 60rpx;
			height: 60rpx;
		}
		.business-menu-label {
			height: 44rpx;
			margin-top: var(--content-default-margin-xs);
			font-size: var(--font-body-md);
			color: var(--light-text-title);
		}
	}
	.activities-image {
		height: 188rpx;
		width: 100%;
	}
}
.list-item {
	height: 100px;
	line-height: 100px;
	border-bottom: 1px solid lavender;
}
.header-view {
	width: 100%;
	height: 280rpx;
	background-color: #61c9cb;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	.title-view {
		width: 100%;
		text-align: center;
		line-height: 60rpx;
		color: white;
	}
	.button-view-wrap {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		width: 100%;
		.button-view {
			background-color: white;
			width: 180rpx;
			text-align: center;
			padding: 10rpx 20rpx;
			border: 1px solid #666;
			border-radius: 10rpx;
		}
	}
}
.login-view {
	height: 120rpx;
	margin: 20rpx;
	background-color: #f2f2f2;
	display: flex;
	flex-direction: row;
	padding: 20rpx;
	align-items: center;
	.login-left-title {
		color: #666;
	}
	.login-left-content {
		color: #999;
	}
	.login-right-view {
		display: flex;
		flex-direction: row;
		justify-content: flex-end;
		flex: 1;
		.login-button {
			border: 1px solid #666;
			border-radius: 5px;
			padding: 10rpx 20rpx;
			font-size: 12px;
			background-color: white;
		}
	}
}

:deep(.uni-swiper-dot) {
	width: 12rpx;
	height: 12rpx;
	border-radius: 6rpx;
}
:deep(.uni-swiper-dot-active) {
	width: 24rpx;
	height: 12rpx;
	border-radius: 6rpx;
}
</style>
