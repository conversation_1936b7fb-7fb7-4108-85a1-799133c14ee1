<template>
	<Layout title="绑定认证" show-nav-bar backgroundImageType="default" @left-click="navigateBack">
		<view class="page-content">
			<UserInfoCard
				ref="userInfoCard"
				:userNo="userNo"
				:addressId="addressId"
				:showChangeUser="false"
			></UserInfoCard>
			<view class="validate-content-wrap"></view>
			<UiCardTabs v-model="validateType" :options="validateTypeList" @change="handleTabClick">
				<view class="validate-form-wrap">
					<UiInput
						v-model="userName"
						label="户名"
						labelWidth="140rpx"
						clearable
						placeholder="请输入燃气户主姓名"
						required
					></UiInput>
					<UiInput
						v-model="phone"
						v-if="validateType === '0'"
						type="number"
						labelWidth="140rpx"
						label="手机号码"
						clearable
						placeholder="请输入营业厅预留的开户手机号"
						required
					></UiInput>
					<UiInput
						v-model="idCardNo"
						v-if="validateType === '1'"
						labelWidth="140rpx"
						label="身份证号"
						clearable
						placeholder="请输入身份证号码"
						required
					></UiInput>
				</view>
			</UiCardTabs>

			<view class="footer-wrap">
				<view class="footer-btn">
					<UiButton
						type="normal"
						width="100%"
						size="lg"
						style-name="light"
						text="上一步"
						@click="navigateBack"
					></UiButton>
				</view>
				<view class="footer-btn">
					<UiButton
						type="secondary"
						width="100%"
						size="lg"
						style-name="light"
						text="立即绑定"
						@click="bindingClick"
					></UiButton>
				</view>
			</view>
		</view>
	</Layout>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import Layout from '@/layout/index.vue'
import UserInfoCard from '@/components/user-info-card/index.vue'
import UiInput from '@e-cloud/eslink-plus-uniapp/components/ui-input/ui-input.vue'
import UiButton from '@e-cloud/eslink-plus-uniapp/components/ui-button/ui-button.vue'
import UiCardTabs from '@e-cloud/eslink-plus-uniapp/components/ui-card-tabs/ui-card-tabs.vue'
import { bindingUserInfoApi } from '@/api/userBind.api'
import { showMsg } from '@/core/util'
import type { UserInfo } from '@/pages/commonTypes'

defineOptions({
	name: 'BindindUser',
})

const userInfo = ref({} as UserInfo)
const validateType = ref('0')
const validateTypeList = ref([
	{ label: '手机号码', value: '0' },
	{ label: '身份证', value: '1' },
])

const userInfoCard = ref() // 用户信息组件

const userNo = ref('') // 传过来的户号
const meterNo = ref('') // 传过来的表号
const addressId = ref('') // 传过来的地址id

const userName = ref('') // 用户名
const phone = ref('') // 手机号
const idCardNo = ref('') // 身份证号

onLoad((options: any) => {
	userNo.value = options.userNo || ''
	meterNo.value = options.meterNo || ''
	addressId.value = options.addressId || ''
})

// tab切换
const handleTabClick = (value: string) => {
	validateType.value = value
}
// 立即绑定点击
const bindingClick = () => {
	if (!userName.value) {
		showMsg('请输入用户名')
		return
	}
	if (validateType.value == '0') {
		if (!phone.value) {
			showMsg('请输入手机号码')
			return
		}
	} else {
		if (!idCardNo.value) {
			showMsg('请输入身份证号码')
			return
		}
	}
	let params = {
		userNo: userNo.value,
		meterNo: meterNo.value,
		addressId: addressId.value,
		userName: userName.value,
		contactPhone: '',
		iDcardNo: '',
	}
	if (validateType.value == '0') {
		params.contactPhone = phone.value
	} else {
		params.iDcardNo = idCardNo.value
	}
	bindingUserInfoApi(params)
		.then(() => {
			showMsg('绑定成功')
			setTimeout(() => {
				uni.navigateBack({ delta: 2 })
			}, 1500)
		})
		.catch(() => {})
}

const navigateBack = () => {
	uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.page-content {
	width: 100%;
	.validate-content-wrap {
		margin: 20rpx 0;
	}
	.validate-form-wrap {
		gap: 10px;
		padding: var(--content-default-padding-sm) var(--content-default-padding-md);
		border-radius: var(--content-default-radius-md);
		background-color: var(--light-fill-blank);
	}
	.footer-wrap {
		position: fixed;
		bottom: env(safe-area-inset-bottom);
		left: 0;
		height: 128rpx;
		width: 100%;
		padding: var(--content-default-padding-sm) var(--content-default-padding-md);
		background-color: var(--light-bg-base);
		display: flex;
		flex-direction: row;
		justify-content: space-around;
		align-items: center;
		.footer-btn {
			width: 47%;
		}
	}
}
</style>
